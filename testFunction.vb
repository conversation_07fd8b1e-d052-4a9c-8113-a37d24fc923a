Public Class CommonFunctions
	
 Public Function ReturnListausWindBraceSingle(TextLine As String)
	Dim oDict As New Dictionary(Of String, Object) : Dim WBlist As New ArrayList
	If TextLine Is Nothing Then : Logger.Debug("txt input not valid:" & TextLine) : Exit Function : End If
	laskuri = 0 : Dim TxtData() As String = TextLine.Split(";"c)
	'to wall
	WBlist.Add(TextLine)

	For Each oVal In TxtData 'seinän nurkasta alas seinään
		Try
			If laskuri>0 'occ eka
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				Try
					oArvo = ParaData(1).Split(New Char() {"/"c })
				Catch
					oArvo = oVal 'yksittäinen tietue
				End Try
				oDict.Add(ParaData(0), oArvo)
			End If
		Catch
			'oDict.Add(TxtData(0), TxtData.Count) 'aina occ nimi ilman on -merkkiä
		End Try
		laskuri += 1
	Next

	WallRivi = TxtData(0) & "_w;"
	For Each oEntry As KeyValuePair(Of String, Object) In oDict
		WallRivi += oEntry.Key & "="
		If oEntry.Value.length = 1 Then
			WallRivi += oEntry.Value(0) & ";"
		ElseIf oEntry.Value.length = 3 Then
			If oEntry.Key = "SDA_to"

				WallRivi += oEntry.Value(0) & "/" & "s" & "/" & "wind_brace_s_1;" ' staattinen from samoja
			ElseIf oEntry.Key = "SDA_to_mir"

				WallRivi += oEntry.Value(0) & "/" & "s" & "/" & "wind_brace_s_1;" ' staattinen
			Else
				WallRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
			End If
		End If
	Next

	WBlist.Add(Left(WallRivi, Len(WallRivi) -1)) 'seinän juoksettaminen

	oPcs = CInt(oDict("br_zigzag_pcs")(0))
	wind_br_start = CInt(oDict("wind_br_start_purlin")(0))

	If oPcs>1 Then
		'[ orsilista
		Dim WindBraceCalList As New ArrayList
		WindBraceCalList.Add("s/wind_brace_corner")
		For i = 1 To e_roof_pcs
			ok1_purlin_pcs = Parameter("r" & i & "_k1_purlin_pcs")
			ok2_purlin_pcs = Parameter("r" & i & "_k2_purlin_pcs")
			For j = 1 To ok1_purlin_pcs
				WindBraceCalList.Add("p" & i & "k/wind_brace_k1_" & j)
			Next
			For k = 1 To ok2_purlin_pcs
				WindBraceCalList.Add("p" & i & "k/wind_brace_k2_" & k)
			Next
		Next

		WindBraceCalList.Add("p" & e_roof_pcs & "k/wind_brace_end")

		If e_ridge_width>0 Then 'miten purlin lisäys
			WindBraceCalList.Add("h/wind_brace_end")
		End If

		']
		For i = 2 To oPcs

			alkuNum = wind_br_start + (i - 2) * 2 'joka toiseen oletuksena, tarve vaihtaa?!
			VertartailuNodelkm = WindBraceCalList.Count - 1 'katon osuus
			Try
			AlkuNode = WindBraceCalList(alkuNum)
			Catch
				Logger.Debug("Error nodes count: " & VertartailuNodelkm & " vs br_zigzag_pcs: " & oPcs)
				Continue For
			End Try
			'tarkista miten toimii jos ei tasan!!
			
			ero_k = VertartailuNodelkm - alkuNum  'jäljellä
			'LoppuNum = alkuNum + 2

			If ero_k = 1 Then 'yksi jää väliin kun ei ole riitävästi nodea
				LoppuNum = alkuNum + 1
			ElseIf ero_k = 3 Then 'turha' väli hypätään yli kun viimeisen täytyy olla viimeinne
				LoppuNum = alkuNum + 3
			Else 'normi tilannne
				LoppuNum = alkuNum + 2
			End If
						
			Try
				LoppuNode = WindBraceCalList(LoppuNum)
			Catch
				LoppuNode = WindBraceCalList(alkuNum + 1) 'ei mene tasan kahden askelluksella
			End Try

			RoofRivi = TxtData(0) & "_" & i & ";"
			For Each oEntry As KeyValuePair(Of String, Object) In oDict
				RoofRivi += oEntry.Key & "="
				If oEntry.Value.length = 1 Then
					RoofRivi += oEntry.Value(0) & ";"
				ElseIf oEntry.Value.length = 3 Then
					oModuuli = oDict("SDA_to")(1) 'miten useampi katto vaihto 
					oSDA_to_node = oDict("SDA_to")(2)

					oNode = Left(oSDA_to_node, Len(oSDA_to_node) -1) 'miten k2 vaihto

					If oEntry.Key = "SDA_from"
						RoofRivi += oEntry.Value(0) & "/" & AlkuNode & ";"
					ElseIf oEntry.Key = "SDA_to"

						RoofRivi += oEntry.Value(0) & "/" & LoppuNode & ";"
					ElseIf oEntry.Key = "SDA_from_mir"

						RoofRivi += oEntry.Value(0) & "/" & AlkuNode & ";"
					ElseIf oEntry.Key = "SDA_to_mir"

						RoofRivi += oEntry.Value(0) & "/" & LoppuNode & ";"

					Else
						RoofRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
					End If
				End If
			Next
			WBlist.Add(Left(RoofRivi, Len(RoofRivi) -1))
		Next 'pcs vaihto
	End If
	'Logger.Debug("ero_k:" & ero_k)
	Return WBlist
End Function

End Class