Sub Main() 'iprops


FName2 = "1"
iProperties.Value("Project", "Part Number") = "H" & FName2 & "_" & ProjectNum
iProperties.Value("Project", "Description") = "HALLI " & ProjectNum


StartTime = Now : auto = iLogicVb.Automation
addIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")

Dim iLogic As Object = addIn.Automation
Dim myArrayList As New ArrayList
Dim ruleName As String = "iProps" ' Set Rule name
Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
	On Error Resume Next
	If Not myArrayList.Contains(oRefDoc.FullFileName) And oRefDoc.FullFileName.ToUpper.Contains("\PROJ\") Then
		myArrayList.Add(oRefDoc.FullFileName)
	End If
	Dim rule As Object
	rule = auto.GetRule(oRefDoc, ruleName)
	If (rule Is Nothing) Then
	Else
		Logger.Debug(oRefDoc.DisplayName & " : " & oRefDoc.FullFileName & " :" & Now)
		Dim i As Integer
		i = auto.RunRuleDirect(rule)
	End If
Next



End Sub

