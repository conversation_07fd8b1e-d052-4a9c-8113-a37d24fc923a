Sub main() 'ReadWriteUCS
	'WriteUCSposition()
	ReadUCSposition()
End Sub

Function WriteUCSposition()
	Dim oUserCoordinateSystems As UserCoordinateSystems = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems
	Dim UCSMatrices As List(Of Matrix) = New List(Of Matrix)
	Dim ucsNameList As New ArrayList

	For Each oUCS As UserCoordinateSystem In oUserCoordinateSystems
		' Get the transformation matrix of the UCS
		Dim oMatrix As Matrix = oUCS.Transformation
		' Store the matrix in the list
		UCSMatrices.Add(oMatrix)
		ucsNameList.Add(oUCS.Name)
	Next

	SharedVariable("UCSMatrices") = UCSMatrices
	SharedVariable("ucsNameList") = ucsNameList
End Function

Function ReadUCSposition()
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmUserCoordinateSystems As UserCoordinateSystems = oAsmDoc.ComponentDefinition.UserCoordinateSystems
	UCSMatrices = SharedVariable("UCSMatrices")
	ucsNameList = SharedVariable("ucsNameList") : nameLaskuri = 0
	Dim oUcs As UserCoordinateSystem
	Dim oAsmUCS As UserCoordinateSystem
	Dim oUCSDefinition As UserCoordinateSystemDefinition = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems.CreateDefinition

	For Each oMatrix As Matrix In UCSMatrices
		oUCSDefinition.Transformation = oMatrix
		oUCSname = ucsNameList(nameLaskuri)
		Try
			oUcs = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems(oUCSname)
			oUcs.Transformation = oMatrix
		Catch
			Logger.Debug("Creating :" & oUCSname)
			oAsmUCS = oAsmUserCoordinateSystems.Add(oUCSDefinition)
			oAsmUCS.Name = oUCSname
		End Try
		nameLaskuri += 1
	Next
End Function