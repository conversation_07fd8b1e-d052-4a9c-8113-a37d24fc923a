Sub main()
SendParamsPart()	
End Sub

Function SendParamsPart()
StartTime = Now
Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim asmParams As Parameters = oAsmDoc.ComponentDefinition.Parameters

' Define allowed parameter names (only these will be synced)
Dim allowedParams As New List(Of String) From {"ridge_tube_code", "ridge_frame_code", "e_wind_brace_wa_code_end", "e_wind_brace_code_st", "e_wind_brace_code_end_r", "e_wind_brace_wa_code_st", "e_wind_brace_code_end", "e_wind_brace_code_end", "e_wab_brace_code_st", "e_wab_brace_code_end"}
' Define allowed part names (only these parts will be processed)
Dim allowedPartNames As New List(Of String) From {"_s", "_h" , "_p1","_p2"} ' Add part name keywords to include

' Loop through all referenced documents (parts and subassemblies)
For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
    ' Check if the document is a part
    If TypeOf oRefDoc Is PartDocument Then
        Dim partDoc As PartDocument = oRefDoc
        Dim partParams As Parameters = partDoc.ComponentDefinition.Parameters

        ' Get part name
        Dim partName As String = System.IO.Path.GetFileNameWithoutExtension(partDoc.FullDocumentName)

        ' Skip parts that do NOT match the allowed list
        If Not allowedPartNames.Any(Function(x) partName.Contains(x)) Then Continue For

        ' Iterate through allowed assembly parameters
        For Each paramName As String In allowedParams
            ' Check if the parameter exists in both assembly and part
            Dim asmParam As Parameter = Nothing
            Dim partParam As Parameter = Nothing

            Try
                asmParam = asmParams.Item(paramName)
                partParam = partParams.Item(paramName)
            Catch
                ' Skip if the parameter does not exist in either document
                Continue For
            End Try

            ' If values are different, update the part parameter
            If asmParam.Value <> partParam.Value And Len(asmParam.Value) >1 Then
				
				Logger.Debug(partName & " changing parameter: " & paramName & " to " & asmParam.Value)
                partParam.Value = asmParam.Value
            End If
        Next

    End If
Next
Logger.Debug("SendParamsPart : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds,1))
End Function