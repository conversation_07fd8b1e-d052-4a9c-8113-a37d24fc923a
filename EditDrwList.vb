Imports System.IO
Imports System.Text
Imports System.Windows.Forms
AddReference "C:\Vault_BH\ExternalRules_BH_Vault\MyTableForm.dll"
Sub Main()
'SharedVariable.Remove("GenDrwList")
Try
	OnkoGenDrwList_SH = SharedVariable(GenDrwList)
	Logger.Debug("using sh parameter:")
	GenDrwList = SharedVariable(GenDrwList)
Catch
	If System.IO.File.Exists(ThisDoc.Path & "\drw.csv") Then
		GenDrwList = ImportCSVToDictionary(ThisDoc.Path & "\drw.csv")
		Logger.Debug("txt parameter:")
	Else
		Try 'listan hakeminen
			NumberListEdit = SharedVariable("NumberListEdit")
		Catch
			BUfile = ThisDoc.Path & "\data.csv"
			Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
			NumberListEdit = ImportCSVToDictionary(BUfile)
			SharedVariable("NumberListEdit") = NumberListEdit
		End Try
		GenDrwList = ConvertModelListToDrwList(NumberListEdit)
		Logger.Debug("Initial calucation of drw list")
	End If
End Try

Drwlista = ShowEditlist(GenDrwList)
GenerateMissingDetDrw(Drwlista)

ExportDictionaryToCSV(Drwlista, ThisDoc.Path & "\drw.csv")
End Sub

Function GenerateMissingDetDrw(ByRef dict As System.Collections.Generic.Dictionary(Of String, String()))
	If dict Is Nothing Then
		Logger.Debug("Dictionary is empty")
		Exit Function
	End If
	StartTime = Now
	Dim oFilesGenS As New ArrayList
	Dim oFilesGenK As New ArrayList
	Dim oFilesGenH As New ArrayList
	teksti = ""
	For Each oVal In dict
		DrwNum = oVal.Key
		oValue = oVal.Value
		IsGenerated = oValue(1)
		DrwFullname = oValue(4)
		TemplateFullname = oValue(5)

		If GenerateAllDetailedModels = True Then
			GenAllForNo = "-"
		Else
			GenAllForNo = "NO" ' optio for "NO" asking every comps  "-"  for multiple automatic gen
		End If

		If IsGenerated.ToUpper <> "YES" And IsGenerated.ToUpper <> GenAllForNo Then
			If System.IO.File.Exists(DrwFullname) Then
				i = MessageBox.Show("Replace: " & DrwFullname, "File already exists", MessageBoxButtons.YesNo)
				If i = vbYes Then
					Logger.Debug("Replacing  " & TemplateFullname & " to " & DrwFullname)
					Dim backupFolder As String = IO.Path.Combine(IO.Path.GetDirectoryName(DrwFullname), "OldVersions")
					Dim fileNameWithoutExt = IO.Path.GetFileNameWithoutExtension(DrwFullname)
					Dim fileExt = IO.Path.GetExtension(DrwFullname)
					backupFile = IO.Path.Combine(backupFolder, fileNameWithoutExt & Abs(Now.GetHashCode) & fileExt)
					System.IO.File.Copy(DrwFullname, backupFile, True)
					System.IO.File.Copy(TemplateFullname, DrwFullname, True)

					teksti += "Copying " & IO.Path.GetFileName(TemplateFullname) & " to " & IO.Path.GetFileName(DrwFullname) & " backup " & IO.Path.GetFileName(backupFile) & vbLf
					Logger.Debug("Copying " & TemplateFullname & " to " & DrwFullname & " backup " & backupFile)
				End If
			Else
				If System.IO.File.Exists(TemplateFullname) Then
					System.IO.File.Copy(TemplateFullname, DrwFullname, True)
					Logger.Debug("Copying " & TemplateFullname & " to " & DrwFullname)
					teksti += "Copying " & IO.Path.GetFileName(TemplateFullname) & " to " & IO.Path.GetFileName(DrwFullname) & vbLf
				Else
					MessageBox.Show("No template file found: " & TemplateFullname, "Error")
				End If
				Try
					attributes = System.IO.File.GetAttributes(DrwFullname)
					If (attributes And System.IO.FileAttributes.ReadOnly) = System.IO.FileAttributes.ReadOnly Then
						attributes = attributes And Not System.IO.FileAttributes.ReadOnly
						System.IO.File.SetAttributes(DrwFullname, attributes)
					End If
				Catch
				End Try
			End If
		End If
	Next

	If Len(teksti) >5 Then
		MessageBox.Show(teksti, "Drawing copy")
	End If
End Function

Function ShowEditlist(NumberSchemaDrw As Dictionary(Of String, String()))
	If NumberSchemaDrw Is Nothing Then Exit Function

	oGeneratedDetDrws = ReturnListDetDrw()
	oGeneratedDetModels = ReturnListDetmodels()
	Using mf As New MyTableForm.MyForm
		mf.Text = "Drawing Template/copy"
		Dim dgv As DataGridView = mf.dgvTable
		' Set columns
		Dim c1 As Integer = dgv.Columns.Add("MyColumn1", "Drw Number") '0
		Dim c2 As Integer = dgv.Columns.Add("MyColumn2", "Template") '1
		Dim c3 As Integer = dgv.Columns.Add("MyColumn3", "is generated") '2
		Dim c4 As Integer = dgv.Columns.Add("MyColumn4", "Modules") '3
		Dim c5 As Integer = dgv.Columns.Add("MyColumn5", "is Model") '4
		Dim c6 As Integer = dgv.Columns.Add("MyColumn6", "Drw path") '5
		Dim c7 As Integer = dgv.Columns.Add("MyColumn7", "Template path") '6
		' Add data from NumberSchemaDrw to DataGridView
		Dim n As Integer = 0

		For Each oVal In NumberSchemaDrw.Keys
			Dim riviN As Integer = dgv.Rows.Add()
			dgv.Rows(n).Cells(0).Value = oVal
			dgv.Rows(n).Cells(0).ReadOnly = True ' Key should be read-only
			oTemplate = NumberSchemaDrw(oVal)(0)
			dgv.Rows(n).Cells(1).Value = oTemplate
			ModelName = ModifyFilePath(ThisDoc.PathAndFileName(True), oVal)

			If oGeneratedDetDrws.contains(ModelName) Then
				isGenerated = "yes"
			Else
				isGenerated = "no"
			End If

			dgv.Rows(n).Cells(2).Value = isGenerated
			dgv.Rows(n).Cells(3).Value = NumberSchemaDrw(oVal)(2) : dgv.Rows(n).Cells(3).ReadOnly = True
			oModelSearch = ModelName.Replace(".idw", ".iam")
			If oGeneratedDetModels.contains(oModelSearch) Then
				isMGenerated = "yes"
			Else
				isMGenerated = "no"
			End If

			dgv.Rows(n).Cells(4).Value = isMGenerated : dgv.Rows(n).Cells(4).ReadOnly = True
			dgv.Rows(n).Cells(5).Value = ModelName : dgv.Rows(n).Cells(5).ReadOnly = True

			If oTemplate = "-" Then
				If Left(oVal, 1) = "S" Then
					oTemplateFullName = "C:\Vault_BH\Designs\Src\adet_s_v2.idw"
				ElseIf Left(oVal, 1) = "H" Then
					oTemplateFullName = "C:\Vault_BH\Designs\Src\adet_h_v2.idw"
				Else 'siis K

					'e_roof_pcs

					oTaittokulma = Parameter("e_roof_" & Mid(oVal, 2, 1) & "_fold_ang")
					If oTaittokulma < 1 Then 'obs ajon sisällä voi erilaisia, sahausmallin k- / k2-
						oTemplateFullName = "C:\Vault_BH\Designs\Src\adet_p1k_v2.idw"
					Else
						oTemplateFullName = "C:\Vault_BH\Designs\Src\adet_p1k2_v2.idw"
					End If
				End If 'tyhjä template 
			Else 'siis syötetty data
				oTemplateFullName = ModifyFilePath(ThisDoc.PathAndFileName(True), oTemplate)
			End If

			dgv.Rows(n).Cells(6).Value = oTemplateFullName : dgv.Rows(n).Cells(6).ReadOnly = True
			n += 1
		Next

		' Show the dialog
		If mf.ShowDialog() = DialogResult.OK Then
			' Update the NumberSchemaDrw dictionary with values from DataGridView
			For Each row As DataGridViewRow In dgv.Rows
				If Not Row.IsNewRow Then
					Dim key As String = Row.Cells(0).Value.ToString()
					Dim newValues As String() = {
					Row.Cells(1).Value.ToString(),
					Row.Cells(2).Value.ToString(),
					Row.Cells(3).Value.ToString(),
					Row.Cells(4).Value.ToString(),
					Row.Cells(5).Value.ToString(),
					Row.Cells(6).Value.ToString()
					}
					' Update dictionary
					If NumberSchemaDrw.ContainsKey(key) Then
						NumberSchemaDrw(key) = newValues
					End If
				End If
			Next

			If dgv.SelectedRows.Count > 0 Then
				i = MessageBox.Show("Open & InitialReset " & oOpenModel & vbLf & " No -option just for opening", "Open & InitialReset", MessageBoxButtons.YesNo)
				' Loop through selected rows
				For Each Row As DataGridViewRow In dgv.SelectedRows
					oOpenModel = dgv.Rows(Row.Index).Cells(5).Value
					isModel = dgv.Rows(Row.Index).Cells(4).Value

					If isModel = "yes" Then
						If i = vbYes Then
							openRuniLogicRule("InitialReset", oOpenModel)
						Else
							Try
								oDoc = ThisApplication.Documents.Open(oOpenModel, True)
							Catch
							End Try
						End If
					Else 'no pitäisi tehdä?
						MessageBox.Show("Model is not generated: " & oOpenModel, "Error")
						Logger.Debug("Model is not generated: " & oOpenModel)
					End If

				Next
			End If
		Else
			Exit Function
		End If 'ok
	End Using
	Return NumberSchemaDrw
End Function
Function InitialRunDrwList()

End Function
Function ModifyFilePath(inputPath As String, Optional newValue As String = Nothing) As String
	Dim fileName As String = System.IO.Path.GetFileNameWithoutExtension(inputPath) ' H11_0075-33_ADET
	Dim dirPath As String = System.IO.Path.GetDirectoryName(inputPath) ' C:\VAULT\DESIGNS\PROJ\0075-33
	Dim extension As String = System.IO.Path.GetExtension(inputPath) ' .IDW

	Dim currentPrefix As String = fileName.Split("_"c)(0) ' Extracts "H11" 
	If newValue IsNot Nothing Then ' H11_0075-33_adet.idw
		Dim newFileName As String = newValue & "_" & ProjectNum & "_adet.idw"
		Return System.IO.Path.Combine(dirPath, newFileName)
	End If
	Return currentPrefix
End Function
Function ReturnListDetDrw()
	Dim folderPath As String = ThisDoc.Path
	Dim DetDrwList As New ArrayList
	Dim fileExtension As String = "*.idw"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)
	For Each oFile As String In files
		If oFile.Contains("_adet.idw") Then
			DetDrwList.Add(oFile)
		End If
	Next
	Return DetDrwList
End Function
Function ReturnListDetmodels()
	Dim folderPath As String = ThisDoc.Path
	Dim DetModelList As New ArrayList
	Dim fileExtension As String = "*.iam"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)
	For Each oFile As String In files
		If oFile.Contains("_adet.iam") Then
			DetModelList.Add(oFile)
		End If
	Next
	Return DetModelList
End Function
Function ConvertModelListToDrwList(originalDict As Dictionary(Of String, String()))
	Dim groupedDict As New System.Collections.Generic.Dictionary(Of String, List(Of String))()

	For Each kvp In originalDict
		Dim mainKey As String = kvp.Key
		Dim subKey As String = kvp.Value(0)
		' Check if subKey already exists in groupedDict
		If Not groupedDict.ContainsKey(subKey) Then
			groupedDict(subKey) = New List(Of String)()
			' Add placeholder values "A" and "B" as an example

			oNumero = CDbl(Mid(subKey, 3, Len(subKey) -2))
			edellinenNumero = oNumero - 1

			If edellinenNumero>0
				Alku = Left(subKey, 2)
				groupedDict(subKey).Add(Alku & edellinenNumero)
			Else
				groupedDict(subKey).Add("-")
			End If
			groupedDict(subKey).Add("no")
		End If
		groupedDict(subKey).Add(mainKey)
	Next
	' Combine only the original keys into a single string
	Dim finalDict As New System.Collections.Generic.Dictionary(Of String, String())()
	For Each kvp In groupedDict
		Dim key As String = kvp.Key
		Dim values As List(Of String) = kvp.Value
		' Extract placeholders ("A", "B") and combine original keys
		Dim placeholders As List(Of String) = values.Take(2).ToList()
		Dim combinedKeys As String = String.Join("-", values.Skip(2))
		' Create the final structure with placeholders and combined keys
		Dim finalValues As String() = placeholders.Concat({combinedKeys }).ToArray()
		finalDict.Add(key, finalValues)
	Next
	Return finalDict
End Function

Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function
Sub ExportDictionaryToCSV(dictionary As Dictionary(Of String, String()), filePath As String)
	If dictionary Is Nothing Then
		Logger.Debug("Dictionary to exporty is empty")
		Exit Sub
	End If
	Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
		' Write each key-value pair to the CSV file
		For Each kvp In dictionary
			Dim key As String = kvp.Key
			Dim values As String() = DirectCast(kvp.Value, String())
			' Combine the key with the values and write them as CSV
			Dim csvLine As String = String.Join(",", New String() {key }.Concat(values))
			writer.WriteLine(csvLine)
		Next
	End Using
	'	i = MessageBox.Show("Open CSV?", "Txt dump", MessageBoxButtons.YesNo)
	'	If i = vbYes Then : ThisDoc.Launch(filePath) : End If
End Sub

Function openRuniLogicRule(RuleName As String, oFullName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Try
		oDoc = ThisApplication.Documents.Open(oFullName, True)
		Call iLogic.RunRule(oDoc, RuleName)
	Catch ex As Exception
		Logger.Debug("Unable to run the rule." & vbLf & ex.Message)
	Finally
		oDoc = Nothing
	End Try
End Function