Sub Main() ' tee sorttaus fullfile namen mukaan ja tulos G_L stock/part number desc
    Dim oDoc As Document = ThisApplication.ActiveEditDocument
    
    ' Ensure the active document is an assembly
    If oDoc.DocumentType <> kAssemblyDocumentObject Then
        MsgBox("This script works only in Assembly documents.")
        Exit Sub
    End If

    ' If nothing is pre-selected, prompt the user to select items
    If oDoc.SelectSet.Count = 0 Then
        MsgBox("No items selected. Please select occurrences or folders in the browser.")
        Exit Sub
    End If

    ' Initialize the total count
    Dim totalItems As Integer
    totalItems = 0

    ' Iterate over the selected items
    Dim oSelectedObject As Object
    For Each oSelectedObject In oDoc.SelectSet
        ' Check if the selected item is a ComponentOccurrence
        If TypeOf oSelectedObject Is ComponentOccurrence Then
            totalItems += 1 ' Count as 1 occurrence

        ' Check if the selected item is a BrowserNode (e.g., a folder in the browser)
        ElseIf TypeOf oSelectedObject Is BrowserNode Then
            Dim oBrowserNode As BrowserNode = oSelectedObject
            totalItems += CountBrowserNodes(oBrowserNode) ' Count sub-nodes recursively

        ' Handle unsupported cases
        Else
            MsgBox("Unsupported object type selected: " & TypeName(oSelectedObject))
        End If
    Next

    ' Display the result
    MsgBox("Total items in selected folder(s) and/or occurrences: " & totalItems)
End Sub

' Recursive function to count BrowserNodes
Function CountBrowserNodes(oNode As BrowserNode) As Integer
    Dim count As Integer
    count = 1 ' Count the current node itself

    ' Get child nodes
    Dim oChildNodes As BrowserNodesEnumerator
    oChildNodes = oNode.BrowserNodes

    ' Recursively count child nodes
    If Not oChildNodes Is Nothing Then
        Dim oChildNode As BrowserNode
        For Each oChildNode In oChildNodes
            count += CountBrowserNodes(oChildNode)
        Next
    End If

    CountBrowserNodes = count
End Function
