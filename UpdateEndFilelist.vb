Imports System.IO
Sub main()
Dim FileEndit As New ArrayList
FileEndit.Add(ThisDoc.Path & "\End_A_" & SharedVariable("ProjectNum") & "\End_A_" & SharedVariable("ProjectNum") & "-" & SharedVariable("ProjectNum") & ".iam")
FileEndit.Add(ThisDoc.Path & "\End_B_" & SharedVariable("ProjectNum") & "\End_B_" & SharedVariable("ProjectNum") & "-" & SharedVariable("ProjectNum") & ".iam")

iFilesEnds = FileList("End_", "*.iam")

For Each oFoundFile In iFilesEnds
	If Not FileEndit.Contains(oFoundFile)
		FileEndit.Add(oFoundFile)
	End If
Next

MultiValue.List("end_a_model") = FileEndit : end_a_model = FileEndit(0)
MultiValue.List("end_b_model") = FileEndit : end_b_model = FileEndit(1)

End Sub

Function FileList(Filter As String, fileExtension As String, Optional folderPathOpt As String = "")
	Dim ModelList As New ArrayList
	If folderPathOpt = "" Then
		folderPath = ThisDoc.Path
	Else
		folderPath = folderPathOpt
	End If
	Dim Subfiles As String() = Directory.GetFiles(folderPath, fileExtension, SearchOption.AllDirectories)
	Filter = Filter.ToLower()
	For Each File As String In Subfiles
		oFileName = System.IO.Path.GetFileName(File)
		oFileName = oFileName.ToLower()
		Dim fileDirectory As String = System.IO.Path.GetDirectoryName(File)
		If fileDirectory.Length >= folderPath.Length AndAlso Not File.Contains("OldVersions") Then ' ei taida olla samassa kansiossa, mutta otetaan mukaan?!
			If oFileName.StartsWith(Filter) And Not oFileName.Contains(".v") And Not oFileName.Contains(".idw") Then
				ModelList.Add(File)
			End If
		End If
	Next
	Return ModelList
End Function