Dim oParaList As New ArrayList : Dim oOccuList As New ArrayList


oParaList.Add("joint_crossarm_k1_1_pos")
oParaList.Add("joint_crossarm_k1_2_pos")
oParaList.Add("joint_crossarm_k2_1_pos")
oParaList.Add("joint_crossarm_k2_2_pos")

oOccuList.Add("p1k")
oOccuList.Add("p2k")

For Each oOcc In oOccuList
	
	For Each oPara In oParaList
		oParaValue = Parameter(oOcc, oPara)
		
		teksti += "Try : Parameter(""" & oOcc & """,""" & oPara & """)=" & oParaValue & " : Catch ex As Exception : logger.debug(ex.Message) : End try" & vbLf
		
	Next
	
Next

Logger.Debug(teksti)
My.Computer.Clipboard.SetText(Teksti)