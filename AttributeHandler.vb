Sub Main()'AttributeHandler

If Attribute_Cui_StoredObject = "Document" Then
	oStoreLoc = ThisApplication.ActiveDocument.ComponentDefinition
ElseIf Attribute_Cui_StoredObject = "Select" Then
	oStoreLoc = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Occurrence")
End If


If Attribute_Cui_Command = "getall" Then
	Attribute_Cui_Result = HandleObjAttribute("getall", oStoreLoc, Attribute_Cui_SetName, "")
	My.Computer.Clipboard.SetText(Attribute_Cui_Result)
ElseIf Attribute_Cui_Command = "saveparamhash"
	ParaHash = GetAllDocsParams("", "d", ThisApplication.ActiveDocument.AllReferencedDocuments, True)
	ConvertHashTable_AttributeSets(ParaHash, Attribute_Cui_SetName, oStoreLoc)
ElseIf Attribute_Cui_Command = "get"
	Attribute_Cui_Result = HandleObjAttribute("get", oStoreLoc, Attribute_Cui_SetName, Attribute_Cui_AttNameList)
ElseIf Attribute_Cui_Command = "set"
	Dim oAttValueType As ValueTypeEnum
	Try
		koe = Attribute_Cui_Result / 1
		oAttValueType = kDoubleType
	Catch
		oAttValueType = kStringType
	End Try
	Setti = HandleObjAttribute("set", oStoreLoc, Attribute_Cui_SetName, Attribute_Cui_AttNameList, Attribute_Cui_Result, oAttValueType)
ElseIf Attribute_Cui_Command = "ExportTxt"
	ExportAttributeSetsTempTxt({Attribute_Cui_SetName }, oStoreLoc)
ElseIf Attribute_Cui_Command = "ImportTxt"
	ImportAttributeSetsTempTxt(oStoreLoc)
ElseIf Attribute_Cui_Command = "del"
	i = MessageBox.Show("Delete attributeset named " & Attribute_Cui_SetName & ":" & Attribute_Cui_AttNameList & "?", "Are you sure", MessageBoxButtons.YesNo)
	If i = vbYes Then
		Attribute_Cui_Result = HandleObjAttribute("del", oStoreLoc, Attribute_Cui_SetName, Attribute_Cui_AttNameList)
	End If
ElseIf Attribute_Cui_Command = "deleteset"
	i = MessageBox.Show("Delete attributeset named " & Attribute_Cui_SetName & " ?", "Are you sure", MessageBoxButtons.YesNo)
	If i = vbYes Then
		DeleteAttributeSets({Attribute_Cui_SetName }, oStoreLoc)
	End If
End If


End Sub

Function ConvertHashTable_AttributeSets(oHashtable As Hashtable, oAttributeSetName As String, oObject As Object)
	'Usage
	'	ParaHash = GetAllDocsParams("", "d", ThisApplication.ActiveDocument.AllReferencedDocuments, True)
	'	ConvertHashTable_AttributeSets(ParaHash, "CustomParams", ItseTiedosto)
	Dim oAttValueType As ValueTypeEnum

	If oObject.AttributeSets.NameIsUsed(oAttributeSetName) = True Then
		oAttribSet = oObject.AttributeSets.Item(oAttributeSetName)
	Else
		oAttribSet = oObject.AttributeSets.Add(oAttributeSetName)
	End If

	For Each oKey In oHashtable.Keys
		oAttribute = oKey.replace("#", "Å") 'obs risuaita aiheuttaa virheen nimessä
		oAttValueST = oHashtable(oKey)

		Try
			oAttValue = CDbl(oAttValueST)
			oAttValueType = kDoubleType
		Catch
			oAttValue = oAttValueST
			oAttValueType = kStringType
		End Try

		If Len(oAttValue) >200 Then ' obs ei tarkoistus tallentaa pitkiä generointi datoja
			Continue For
		End If
		HandleObjAttribute("set", oObject, oAttributeSetName, oAttribute, oAttValue, oAttValueType)
	Next
End Function
Function ImportAttributeSetsTempTxt(oObject As Object)
	Dim tempFilePath As String = InputBox("Transfer filename", "Name", ThisDoc.Path & "\AttributeSetExport.txt")
	Dim attributesText As String = IO.File.ReadAllText(tempFilePath)
	' Split the text into lines
	Dim lines() As String = Split(attributesText, vbCrLf)
	' Extract the AttributeSet name from the first line
	Dim attSetName As String = Replace(lines(0), "AttributeSet:", "")
	' Check if the AttributeSet exists in the target document
	Dim oAttribSet As AttributeSet
	If oObject.AttributeSets.NameIsUsed(attSetName) Then
		oAttribSet = oObject.AttributeSets.Item(attSetName)
	Else
		' If the AttributeSet does not exist, create it
		oAttribSet = oObject.AttributeSets.Add(attSetName)
	End If
	' Loop through the lines and add each attribute to the AttributeSet
	For i As Integer = 1 To UBound(lines)
		If lines(i) <> "" Then
			Dim attrParts() As String = Split(lines(i), "#")
			Dim oAttributeName As String = attrParts(1)
			Dim oAttValue As String = attrParts(2)
			Dim oAttValueTypeNum As String = attrParts(3)
			Dim oAttValueType As ValueTypeEnum
			Try
				If oAttValueTypeNum = 14594 Or oAttValueTypeNum = "14594" Then 'obs välillä voi saada string/double
					oAttValueType = kDoubleType
				Else
					oAttValueType = kStringType
				End If
			Catch
			End Try
			Try
				oAttrib = oAttribSet.Add(oAttributeName, oAttValueType, oAttValue)
				Logger.Debug("Creating oAttribute " & oAttributeName)
			Catch
				Logger.Debug("Updating " & oAttSet & "\" & oAttributeName & " = " & oAttValue)
				Try : oAttrib = oAttribSet.Item(oAttributeName) : Catch : Logger.Debug("Not found attribute named : " & oAttributeName) : End Try
			oAttrib.Value = oAttValue
			End Try
		End If
	Next
	Logger.Debug("Attributes imported successfully")
End Function
Function ExportAttributeSetsTempTxt(oAttributeSetNames() As String, oObject As Object)
	'usage
	'ExportAttributeSetsTempTxt({"CustomParams" }, ItseTiedosto)
	Dim oAttrSet As AttributeSet
	For Each attSetName In oAttributeSetNames
		Try
			oAttrSet = oObject.AttributeSets.Item(attSetName)
		Catch
			Continue For
		End Try
		' Create a string to hold the attributes and their values
		Dim attributesText As String = "AttributeSet:" & attSetName & vbCrLf
		' Loop through all attributes and export their names and values
		For Each oAttr As Attribute In oAttrSet

			attributesText &= attSetName & "#" & oAttr.Name & "#" & oAttr.Value.ToString() & "#" & oAttr.ValueType & vbCrLf
		Next
		' Save the attributes to a text file (or you can store it in a global variable or clipboard)
		'Dim tempFilePath As String = ThisDoc.Path & "\AttributeSetExport.txt"
		Dim tempFilePath As String = InputBox("Transfer filename", "Name", ThisDoc.Path & "\AttributeSetExport.txt")
		IO.File.WriteAllText(tempFilePath, attributesText)
		Logger.Debug("Attributes exported to " & tempFilePath, "Export Complete")
	Next
End Function
Function HandleObjAttribute(oCommand As String, oObject As Object, oAttSet As String, oAttribute As String, Optional oAttValue As Object = "", Optional oAttValueType As ValueTypeEnum = kStringType)
	Dim oAttribSet As AttributeSet : Dim myAttNameList As New ArrayList
	Dim oAttrib As Attribute
	Dim oAttribSets As AttributeSets = oObject.AttributeSets
	Onko = oObject.AttributeSets.NameIsUsed(oAttSet)

	If oObject.AttributeSets.NameIsUsed(oAttSet) = True Then
		oAttribSet = oObject.AttributeSets.Item(oAttSet)
	Else
		oAttribSet = oObject.AttributeSets.Add(oAttSet)
	End If

	If oCommand = "set" Then 'Attribute_Cui_AttNameList tyhjentyy?!
		Try
			oAttrib = oAttribSet.Add(oAttribute, oAttValueType, oAttValue)
			Logger.Debug("Creating oAttribute " & oAttribute)
		Catch
			Logger.Debug("Updating " & oAttSet & "\" & oAttribute & " = " & oAttValue)
			Try : oAttrib = oAttribSet.Item(oAttribute) : Catch : Logger.Debug("Not found attribute named : " & oAttribute) : End Try
		oAttrib.Value = oAttValue
		End Try

		'Return "Set done"
	End If

	If oCommand = "del" Then
		
		Try : oAttrib = oAttribSets.Item(oAttSet).Item(oAttribute) : Catch : Logger.Debug("del: Not found attribute named : " & oAttribute) : End Try
		Try : oAttrib.Delete : Catch : End Try
		Return "Delete done"
	End If

	If oCommand = "get" Then
		Try : oAttrib = oAttribSets.Item(oAttSet).Item(oAttribute) : Catch : Logger.Debug("get: Not found attribute named : " & oAttribute) : End Try
		Try : oValue = oAttrib.Value : Catch : End Try : Logger.Debug("Getting oValue : " & oValue)
		Return oValue
	End If

	If oCommand = "getall" Then
		TulosteTeksti = oAttSet & vbLf

		For Each oAttr As Attribute In oAttribSet
			Dim attrName As String = oAttr.Name
			myAttNameList.Add(attrName)
			attrValue = oAttr.Value.tostring

			' Print the AttributeSet name, Attribute name, and Attribute value
			TulosteTeksti += "  " & attrName & " : " & attrValue & vbLf
		Next
	End If
	MultiValue.List("Attribute_Cui_AttNameList") = myAttNameList 'käyttöliittymään päivittyvälista atribuuttien nimiin

	Return TulosteTeksti
End Function
Function DeleteAttributeSets(oAttributeSetNames() As String, oObject As Object)
	Dim oAttributeSets As AttributeSets = oObject.AttributeSets
	' Check if the "CustomAttributes" AttributeSet exists
	For Each attSetName In oAttributeSetNames
		If oAttributeSets.NameIsUsed(attSetName) Then
			oAttributeSets.Item(attSetName).Delete()
			Logger.Debug("AttributeSet " & attSetName & " deleted successfully.")
		Else
			Logger.Debug("AttributeSet " & attSetName & " does not exist.")
		End If
	Next

End Function
Function GetAllDocsParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional paraTaulu As Boolean = False)
	Dim myArrayList As New ArrayList : Dim oRefDoc As Document : Dim ParaMeterHash As Hashtable = New Hashtable
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters    
		Pituus = Len(oFilter)
		For Each Item In UserParams
			oParaName = Item.Name
			If oParaName.contains(oFilter) And Left(oParaName, Len(oNegFilter)) <> oNegFilter Then
				'teksti += oParaName & ":" & Item.Expression & vbLf
				'myArrayList.Add(oRefDoc.DisplayName & "#" & oParaName & "#" & Item.Value)
				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				Else If Item.Units = "deg" Then
				Arvo = Item.Value * 180 / PI
			Else
				Arvo = Item.Value
			End If

			If Not ParaMeterHash.ContainsKey(oParaName) Then
				ParaMeterHash.Add(oParaName, Arvo) 'uusiarvo
			Else
				'
				If Arvo <> ParaMeterHash(oParaName) Then 'ei lisätä duplikaatteja
					ParaMeterHash.Add(oParaName & "#" & oRefDoc.DisplayName, Arvo)
				End If
			End If
			End If
		Next
	Next
	If PalautusData Then
		Return myArrayList 'array voisi tallentaa multivalue parametriin
	Else
		Return ParaMeterHash
	End If
End Function