Sub main()
Dim SearchList As New ArrayList
SearchList.Add("K1_0075-61")
SearchList.Add("BCO-0025")

For Each oSearchValue In SearchList
	List = SoveliaSearh(oSearchValue, "DocDescr,NAME,DocID,DocRev,Typetree")
	Dim TulosData() As String = List(1).Split(":"c)
	MikäsOsa = SoveliaSearh(TulosData(1), "Typetree")
Next

For Each oVal In List
	Logger.Debug("oVal:" & oVal)
Next
End Sub

Function SoveliaSearh(SearchID As String, resultCriteria As String)
	Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
	_addInInterface = addin.Automation
	Dim searchCriteria As New List(Of String)()
	Dim resultLimit As Integer
	searchCriteria.Add("DocID:" & SearchID)
	resultLimit = 2
	Dim task As System.Threading.Tasks.Task(Of String) = _addInInterface.Search(searchCriteria, resultCriteria, resultLimit)
	Dim awaiter As System.Runtime.CompilerServices.TaskAwaiter(Of String) = task.GetAwaiter()
	awaiter.GetResult()
	Dim oResult As String = awaiter.GetResult()
	'System.Windows.Forms.MessageBox.Show(oResult)
	Dim TxtData() As String = oResult.Split(";"c)
	Return TxtData
End Function
