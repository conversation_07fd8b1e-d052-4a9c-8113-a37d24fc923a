iLogicVb.RunRule("Finalize")

Try
	oDoc = ThisApplication.Documents.Open(Filelist, True)
Catch : End Try

auto = iLogicVb.Automation : Dim addIn As ApplicationAddIn
addIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
Dim iLogic As Object = addIn.Automation

Try
	Call iLogic.RunRule(oDoc, "ShowCui")
	Logger.Debug("Suppressing done")
Catch
	Logger.Debug("Suppress rule missing")
End Try