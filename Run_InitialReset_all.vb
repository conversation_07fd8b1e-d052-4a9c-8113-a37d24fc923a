Imports System.IO
Sub main()

StartTime = Now

Try
	OnkoAjettu = SharedVariable("ProjectNum")
Catch
	iLogicVb.RunRule("SharedUpdate")
End Try


InitialReset2All("InitialReset")

Logger.Debug("InitialReset : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))

End Sub

Function InitialReset2All(ORuleName As String)
	Dim folderPath As String = ThisDoc.Path
	Dim DetModelList As New ArrayList
	Dim fileExtension As String = "*.iam"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)
	For Each oFile As String In files
		If oFile.Contains("_adet") Then
			DetModelList.Add(oFile)
		End If
	Next

	If DetModelList.Count = 0 Then
		Logger.Debug("No detailed models in folder, exiting...")
		Exit Function
	End If

	For Each oModelName In DetModelList
		openFileRunrule(oModelName, "InitialReset")
	Next
End Function

Function openFileRunrule(oOpenFilu As String, oRuleName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Try
		oDoc = ThisApplication.Documents.Open(oOpenFilu)
		Call iLogic.RunRule(oDoc, oRuleName)
	Catch ex As Exception
		Logger.Debug("Error in Opening :" & vbLf & ex.Message)
	Finally
		oDoc = Nothing
		ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
	End Try
End Function
