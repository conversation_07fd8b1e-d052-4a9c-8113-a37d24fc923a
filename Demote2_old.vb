Sub main()
oAllOccur = GetAllOccurances()

Occ1 = GetOccByName("Reinforcement_tube_assy_s_1", "", oAllOccur)
Occ2 = GetOccByName("f1", "S11_0075-92_adet:1", oAllOccur)


GetMinimumDistanceByOcc(Occ1, Occ2)


End Sub

'S11_0075-92_adet:1 : S11_0075-92_adet:1 \ f1


Function GetMinimumDistanceByOcc(oComp1 As ComponentOccurrence, oComp2 As ComponentOccurrence)
	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = Round(pos1.X - pos2.X,1)
		Dim deltaY As Double = Round(pos1.Y - pos2.Y,1)
		Dim deltaZ As Double = Round(pos1.Z - pos2.Z,1)
		Dim distance As Double = Round(Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ),1)
		' Print or use the distance value as needed
		MsgBox("Distance between " & oComp1.Name & " And " & oComp2.Name  & " " & distance & "[x:" & deltaX & " y:" & deltaY & " z:" & deltaZ & "]")
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function

Function GetOccByName(filter1 As String, filter2 As String, oAllOccur As ObjectCollection)
	For Each oOcc As ComponentOccurrence In oAllOccur
		oOccName = oOcc.Name
		oOccPathName1 = oOcc.OccurrencePath.Item(1).Name
		Try
			oOccPathName2 = oOcc.OccurrencePath.Item(2).Name
		Catch
			oOccPathName2 = ""
		End Try

		Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
		If oOccPathName1 = filter1 Then
			Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
			Return oOcc
		End If
	Next
End Function



Function GetAllOccurances(Optional debugMode As Boolean = False) As ObjectCollection
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Try
		' Top-level occurrences
		For Each oOcc As ComponentOccurrence In oDoc.ComponentDefinition.Occurrences
			ProcessOccurrence(oOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Log or handle the exception
		If debugMode Then Logger.Error("Error in GetAllOccurances: " & ex.Message)
	End Try
	Return oAllOccur
End Function
Sub ProcessOccurrence(ByVal oOcc As ComponentOccurrence, ByRef oAllOccur As ObjectCollection, Optional debugMode As Boolean = False)
	Try
		' Check for invalid or unresolved references
		If oOcc.Definition Is Nothing Then
			If debugMode Then Logger.Warn("Skipping occurrence due to invalid definition: " & oOcc.Name)
			Exit Sub
		End If
		' Skip Virtual and Weld Component Definitions
		If TypeOf oOcc.Definition Is VirtualComponentDefinition OrElse TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If debugMode Then Logger.Info("Skipping virtual or weld component: " & oOcc.Name)
			Exit Sub
		End If
		' Add the occurrence to the collection
		oAllOccur.Add(oOcc)
		If debugMode Then Logger.Debug("Added occurrence: " & oOcc.Name)
		' Process sub-occurrences recursively
		For Each oSubOcc As ComponentOccurrence In oOcc.SubOccurrences
			ProcessOccurrence(oSubOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Handle errors gracefully
		If debugMode Then Logger.Error("Error processing occurrence '" & oOcc.Name & "': " & ex.Message)
	End Try
End Sub

Function GetMinimumDistance(Occ1 As String, Occ2 As String, DirectionFilter As String)
	Dim oDoc As Document = ThisApplication.ActiveDocument
	Dim oComp1 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ1)
	Dim oComp2 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ2)

	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = pos1.X - pos2.X
		Dim deltaY As Double = pos1.Y - pos2.Y
		Dim deltaZ As Double = pos1.Z - pos2.Z
		Dim distance As Double = Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)
		' Print or use the distance value as needed
		MsgBox("Distance between Component1 and Component2: " & distance)
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function