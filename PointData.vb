Dim oAsmDoc As AssemblyDocument
oAsmDoc = ThisApplication.ActiveDocument
Dim oAsmDef As AssemblyComponentDefinition
oAsmDef = oAsmDoc.ComponentDefinition

sWPFilter = "joint_"
oDataTeksti = ""


For i = 1 To oAsmDef.Occurrences.Count
	thisOcc = oAsmDef.Occurrences(i)
	'Debug.Print thisOcc.Name
	'skip suppressed components
	If Not thisOcc.Suppressed Then
		'sub IAM or IPT: loop through all WP
		If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
			Dim currentWP As WorkPoint
			For Each currentWP In thisOcc.Definition.WorkPoints
				'check if pointname contains underscore
				If InStr(1, currentWP.Name, sWPFilter) Then
					Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
					X = Round(oAsmPoint.point.X*10,1)
					Y = Round(oAsmPoint.point.Y*10,1)
					Z = Round(oAsmPoint.point.Z*10,1)
					Logger.Debug(thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
					'colCSVList.Add GetCSVLine(uom, oAsmPoint, currentWP.Name, thisOcc.Name, "", "")
					oDataTeksti+=thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
				End If
			Next
		End If
		'sub IAM: loop through all suboccurences
		If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
			For j = 1 To thisOcc.SubOccurrences.Count

				thisSubOcc = thisOcc.SubOccurrences(j)
				If Not thisSubOcc.Suppressed Then

					If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
						Dim currentSubWP As WorkPoint
						For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
							'check if pointname contains underscore
							If InStr(1, currentSubWP.Name, sWPFilter) Then
								Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
								X = Round(oAsmPoint.point.X)
								Y = Round(oAsmPoint.point.Y)
								Z = Round(oAsmPoint.point.Z)

								Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
								oDataTeksti+=thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
							End If
						Next
					End If
					'subsub IAM: loop through all subsuboccurences
					If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
						For k = 1 To thisSubOcc.SubOccurrences.Count
							thisSubSubOcc = thisSubOcc.SubOccurrences(k)
							If Not thisSubSubOcc.Suppressed Then
								If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
									Dim currentSubSubWP As WorkPoint
									For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
										'check if pointname contains underscore
										If InStr(1, currentSubSubWP.Name, sWPFilter) Then
											Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
											X = Round(oAsmPoint.point.X)
											Y = Round(oAsmPoint.point.Y)
											Z = Round(oAsmPoint.point.Z)

											Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
											oDataTeksti+=thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
										End If
									Next
								End If
							End If
						Next
					End If
					'END subsub IAM
				End If
			Next
		End If
		'END sub IAM
	End If
Next

CoordDataa = oDataTeksti
My.Computer.Clipboard.SetText(oDataTeksti)