Sub main() 'OpenRunVirtOcca 
Try
	NumberListEdit = SharedVariable("NumberListEdit")
	openRuniLogicRule("CreateDetailOcc", {"f" })
Catch
	Logger.Debug("Command needs pre-information of modules, run List of Generated models [EditList] before this command")
End Try
End Sub

Function openRuniLogicRule(RuleName As String, Filtteri() As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Dim myArrayList As New ArrayList
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		oFullName = oOccurrence.ReferencedFileDescriptor.FullFileName
		For Each oFiltteri In Filtteri
			If Not myArrayList.Contains(oFullName) And Left(oOccurrence.Name, Len(oFiltteri)) = oFiltteri Then
				myArrayList.Add(oFullName)
				oDoc = ThisApplication.Documents.Open(oFullName, True)
				Try
					Call iLogic.RunRule(oDoc, RuleName)
				Catch
					Logger.Debug("Unable to run the rule")
				Finally
					oDoc = Nothing
				End Try
				ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
			End If
		Next
	Next
End Function

