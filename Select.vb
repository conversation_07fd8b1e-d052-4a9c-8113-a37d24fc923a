Sub main()
IsHighlightSetOn = True 'False/True hidastaa tietysti isoilla malleilla

Dim oAsmDoc As Document = ThisApplication.ActiveDocument
'[Select Pre&Pick
Dim oObjectCollection As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
Dim oSelectSet As SelectSet = oAsmDoc.SelectSet

Dim preSelectedCount As Integer = 0
If oSelectSet.Count > 0 Then
	For Each selectedItem As Object In oSelectSet
		If TypeOf selectedItem Is ComponentOccurrence Then
			Dim oOcc As ComponentOccurrence = CType(selectedItem, ComponentOccurrence)
			' Check if it's already in our collection (shouldn't be at this stage, but good practice)
			Dim alreadyExists As Boolean = False
			For Each itemInCollection As Object In oObjectCollection
				If itemInCollection Is oOcc Then
					alreadyExists = True
					Exit For
				End If
			Next
			If Not alreadyExists Then
				oObjectCollection.Add(oOcc)
				preSelectedCount += 1
			End If
		End If
	Next
	If preSelectedCount > 0 Then
		Logger.Debug(preSelectedCount & " item(s) were pre-selected and added to the list.")
	End If
End If

Dim selectedOccurrence As ComponentOccurrence
Dim continueSelection As Boolean = True
Dim pickPrompt As String

Do While continueSelection
	If oObjectCollection.Count > 0 Then
		pickPrompt = "Select an additional occurrence or press ESC to process " & oObjectCollection.Count & " selected item(s)."
	Else
		pickPrompt = "Select an occurrence (Press ESC to cancel if no items are selected)."
	End If
	Try
		selectedOccurrence = ThisApplication.CommandManager.Pick( _
		SelectionFilterEnum.kAssemblyOccurrenceFilter, _
		pickPrompt)
		' Check if Pick returned Nothing (e.g., user pressed ESC immediately or after a selection)
		If selectedOccurrence Is Nothing Then
			continueSelection = False ' Exit loop if ESC was pressed
		Else
			' Check if the newly picked item already exists in the collection
			Dim alreadyExists As Boolean = False
			For Each item As Object In oObjectCollection
				If item Is selectedOccurrence Then ' Use "Is" for object reference comparison
					alreadyExists = True
					MessageBox.Show("'" & selectedOccurrence.Name & "' is already in the selection list.", "Already Selected", MessageBoxButtons.OK, MessageBoxIcon.Information)
					Exit For
				End If
			Next
			If Not alreadyExists Then
				oObjectCollection.Add(selectedOccurrence)
				Logger.Debug("Added via pick: " & selectedOccurrence.Name & ". Total selected: " & oObjectCollection.Count)
			End If
		End If
	Catch ex As Exception
		Logger.Debug("Exception during Pick command (likely ESC): " & ex.Message)
		continueSelection = False
	End Try
Loop
Try
	oSavedSet = SharedVariable("oSHSelectionSet")
	i = MessageBox.Show("Found saved set Yes for adding No for delete set", "Saved Selection", MessageBoxButtons.YesNo)
	If i = vbYes Then
		For Each oSelectOcc In oSavedSet
			oObjectCollection.Add(oSelectOcc)
		Next
	Else
		SharedVariable.Remove("oSHSelectionSet")
		Logger.Debug("Saved selection set removed")
	End If
Catch
End Try
' --- STEP 3: Process the Combined Selection ---
If oObjectCollection.Count = 0 Then
	MsgBox("No occurrences were selected. Exiting rule.", vbInformation, "Selection Empty")
	Exit Sub
End If
Logger.Debug("Proceeding to process " & oObjectCollection.Count & " occurrence(s).")
']	

Dim selectedFileNames As New List(Of String)
If IsHighlightSetOn Then
	For Each occ As ComponentOccurrence In oObjectCollection
		Dim doc As Document = occ.Definition.Document
		If Not selectedFileNames.Contains(doc.FullFileName) Then
			selectedFileNames.Add(doc.FullFileName)
		End If
	Next
	' Clear current selection set
	oAsmDoc.SelectSet.Clear()
	Dim oHS1 As HighlightSet
	oHS1 = oAsmDoc.CreateHighlightSet
	oHS1.Color = ThisApplication.TransientObjects.CreateColor(255, 0, 0)
	AllRefs = oAsmDoc.ComponentDefinition.Occurrences.AllLeafOccurrences
	For Each occ In AllRefs
		If selectedFileNames.Contains(occ.Definition.Document.FullFileName) Then
			oAsmDoc.SelectSet.Select(occ)
			oHS1.AddItem(occ)
		End If
	Next
End If

'[Kysely
Dim oList As New List(Of String) From {"LY08=0", "LY08=1", "Move", "Change G_L=xXx", "Save selection set", "Delete", "Cancel" }
Dim Selected As IEnumerable(Of Object) = MultiSelectListBox("Select Some", oList, Nothing, "Multi-Select InputListBox", "My List")
If Selected.Count = 0 Or Selected.Contains("Cancel") Then
	Logger.Debug("Nothing was selected from the list.", "None Selected")
	Exit Sub
End If
']



Dim TableHash As Hashtable = New Hashtable
QueryCount = 0
QueryCount2 = 0
If Selected.Contains("LY08=0") Or Selected.Contains("LY08=1") Or Selected.Contains("Change G_L=xXx") Then
	oTotCount = oSelectSet.Count
	oSelCount = oObjectCollection.Count

	If oTotCount>oSelCount Then
		i = MessageBox.Show("This operation effects to " & oTotCount & " components and selected only " & oSelCount & " occurences" & vbLf & "Yes to copy&replace selected set" & vbLf & "No to make modifation to all occurances", "Obs Modification", MessageBoxButtons.YesNo)
		If i = vbYes Then
			Try : SrcFilu = oObjectCollection(1).ReferencedFileDescriptor.FullFileName : Catch : SrcFilu = "" : End Try
			NewFilu = InputBox("Give a new file name", "New file name", SrcFilu)
			oCopyFName = SaveFilu(SrcFilu, NewFilu)
			For Each oReplaceOcc In oObjectCollection
				Component.Replace(oReplaceOcc.name, NewFilu, False)
				oReplaceResultTxt += "Replacing " & oReplaceOcc.name & " with " & NewFilu & vbLf
			Next
			MessageBox.Show(oReplaceResultTxt, "Replaced")
			Logger.Debug(oReplaceResultTxt)
		End If
	End If

	For Each oVal In oObjectCollection
		Try : oFullName = oVal.ReferencedFileDescriptor.FullFileName : Catch : oFullName = "" : End Try
		If Not TableHash.ContainsKey(oFullName) Then
			TableHash.Add(oFullName, oVal)
		End If
	Next
	
	For Each oVal In TableHash.Values
		If Selected.Contains("LY08=0") Then
			Try : Parameter(oVal.Name, "LY08") = 0 : Catch ex As Exception : Logger.Debug(ex.Message) : End Try
		End If

		If Selected.Contains("LY08=1") Then
			Try : Parameter(oVal.Name, "LY08") = 1 : Catch ex As Exception : Logger.Debug(ex.Message) : End Try
		End If

		If Selected.Contains("Change G_L=xXx") Then
			If QueryCount = 0 Then
				Size = InputBox("G_L for all selection?", "Value to Send", 3000) : QueryCount += 1
			End If
			Try : Parameter(oVal.Name, "G_L") = Size : Catch ex As Exception : Logger.Debug(ex.Message) : End Try
		End If
	Next
	
End If ' kaikille tehtävä
	
	For Each oVal In oObjectCollection
		If Selected.Contains("Delete") Then
			Try : Component.InventorComponent(oVal.Name).Delete() : Catch : End Try
		End If
		If Selected.Contains("Save selection set") Then
			SharedVariable("oSHSelectionSet") = oObjectCollection
		End If
		If Selected.Contains("Move") Then
			
			Try : oCategory = iProperties.Value(oVal.Name, "Summary", "Category") : Catch : oCategory = "" : End Try

			If QueryCount2 = 0 Then
				oMoveY = InputBox("Change Y direction value?" & vbLf & "(Mirrored components are not changed)", "Constrains modification", 3000) : QueryCount2 += 1
			End If
			If oCategory = "purlin" Then
				oCons = oVal.Constraints
				For Each oConstraint In oCons
					oConName = oConstraint.Name
					If oConName.contains("_XZ") Then
						oConOffset = oConstraint.Offset
						OldValueOffset = oConOffset.Value
						oConOffset.Value = oMoveY / 10 + OldValueOffset
					End If
				Next
			End If
		End If
	Next
'End If 'type selection


iLogicVb.UpdateWhenDone = True 'onko oikea paikka
End Sub
Function SaveFilu(SrcFilu As String, NewFilu As String) 'lisää taulu ettei duplikaatteja
	oDoc = ThisApplication.Documents.Open(SrcFilu, False)
	If IO.File.Exists(NewFilu) Then
		Logger.Debug("File already exists : " & NewFilu)
	Else
		Logger.Debug("New part File being created : " & NewFilu)
		oDoc.SaveAs(NewFilu, True)
	End If
	Return NewFilu
End Function
Function MultiSelectListBox(Optional Instructions As String = vbNullString, Optional Items As IEnumerable = Nothing, Optional DefaultValue As Object = Nothing, Optional Title As String = vbNullString, Optional ListName As String = vbNullString) As IEnumerable(Of Object)
	Using oILBD As New Autodesk.iLogic.Runtime.InputListBoxDialog(Title, ListName, Instructions, Items, DefaultValue)
		Dim oLB As System.Windows.Forms.ListBox = oILBD.Controls.Item(0).Controls.Item(2)
		oLB.SelectionMode = System.Windows.Forms.SelectionMode.MultiSimple
		Dim oDlgResult As System.Windows.Forms.DialogResult = oILBD.ShowDialog()
		Dim oSelected As IEnumerable(Of Object) = oLB.SelectedItems.Cast(Of Object)
		Return oSelected
	End Using
End Function
