'openAndRegenAll
Dim oLeafOccs As ComponentOccurrencesEnumerator = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AllLeafOccurrences
Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
Dim iLogic As Object = addIn.Automation
Dim myArrayList As New ArrayList


For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
	oFullName = oRefDoc.FullFileName
	If Not myArrayList.Contains(oFullName)
		myArrayList.Add(oFullName)
		oDoc = ThisApplication.Documents.Open(oFullName, True)
		Try
			Call iLogic.RunRule(oDoc, "FinalizeEnd")
		Catch
			Logger.Debug("Unable to run the rule")
		Finally
			oDoc = Nothing
		End Try
		ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
	End If
Next
iLogicVb.UpdateWhenDone = True
