Sub main()
GetParamsTxt(RidgeTubeA_txt & ";" & WindBracesA_txt)
End Sub

Function GetParamsTxt(textParameter As String)
	Dim pairs() As String
	pairs = Split(textParameter, ";")
	Dim paramName As String: Dim paramValue As String
	For Each pair In pairs
		' Split the pair into parameter name and value using the equals sign
		Dim parts() As String
		parts = Split(pair, "=")
		If UBound(parts) = 1 Then  ' Ensure we got two parts (name and value)
			paramName = Trim(parts(0)) ' Remove leading/trailing spaces
			paramValue = Trim(parts(1)) ' Remove leading/trailing spaces
			' Update assembly parameters based on the extracted name
		End If
		Try
			Parameter(paramName) = paramValue
			Logger.Debug("Chaging parameter called " & paramName & " to " & paramValue)
		Catch
			'Logger.Debug("No parameter called " & paramName)
		End Try
	Next
End Function