Sub main() 'cui_commands


oCommandOption = RuleArguments(“myArg”)

If oCommandOption = "SelectOcc" Then
	SelectOccu()
ElseIf oCommandOption = "ReadOneLine" Then
	ReadOneLine(Lines)
End If

'ChangeOccu(True)
'ReadOneLine(Lines)
'Dim txtData As String=Lines.ToString
'PlaceOneTxtLine(Lines)
'koe = GetConsInfo("")


End Sub
Function GetConsInfo(TxtLine As String)
	Dim oDict As New Dictionary(Of String, Object) : laskuri = 0

	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences

		If Left(oOccurrence.Name, 1) = "e" Then

			For Each oConstraint In oOccurrence.Constraints
				oConstName = oConstraint.name.replace("_point", "").replace("_dir", "").replace("_ang", "")

				oData = oConstName.Split(New Char() {"#"c })
				EkaNode = oData(1)

				Try
					oDict.Add(EkaNode, laskuri)
					laskuri += 1
				Catch
					End Try

				Try 'riippuen rajoitteista
					TokaNode = oData(2)
					oDict.Add(TokaNode, laskuri)
					laskuri += 1
				Catch
				End Try
				'EkaNodeData = EkaNode.Split(New Char() {"/"c })
				'TokaNodeData = TokaNode.Split(New Char() {"/"c })

			Next
		End If
	Next
	Return oDict
End Function
Function PlaceOneTxtLine(TxtLine As String)
	'miten cui muutoshallitaan
	If TxtLine.Contains("joint_purlin_") Then
		PlacePurlinCompoByTxtPara(TxtLine)
	ElseIf TxtLine.Contains("_crossarm_") Then
		PlaceCrossarmCompoByTxtPara(TxtLine)
	ElseIf TxtLine.Contains("joint_brace_") Then
		PlaceBraceCompoByTxtPara(TxtLine)
	ElseIf TxtLine.Contains("wind_brace_purlin_") Then
		PlaceWindBraceCompoByTxtPara(TxtLine)
	End If
End Function
Function ReadOneLine(TxtPara As String)
	Dim TxtData() As String = TxtPara.Split(";"c)
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
			CUI_oOccname = oOccName
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			If oParameterName = "SDA_from" Then
				oPointDataArraysis = oDatumSis(1)
				CUI_Cons1_name = ""
				CUI_Node1_name = oPointDataArraysis
				oPointDataArray = oPointDataArraysis.Split(New Char() {"/"c })
				oSDA_from = oDatumSis(1)
				oSDA_from_wp = oPointDataArray(2)
			ElseIf oParameterName = "SDA_to" Then
				oSDA_to = oDatumSis(1)
				CUI_Cons2_name = ""
				CUI_Node2_name = oSDA_to
				oPointDataArraysis_to = oDatumSis(1)
				oPointDataArray_to = oPointDataArraysis_to.Split(New Char() {"/"c })
				oSDA_to_wp = oPointDataArray_to(2)
			ElseIf oParameterName = "SDA_from_mir" Then
				oPointDataArraysis_mir = oDatumSis(1)
				oPointDataArray_mir = oPointDataArraysis_mir.Split(New Char() {"/"c })
				oSDA_from_mir = oDatumSis(1)
				oSDA_from_wp_mir = oPointDataArray_mir(2)
			ElseIf oParameterName = "SDA_to_mir" Then
				oSDA_to_mir = oDatumSis(1)
				oPointDataArraysis_to_mir = oDatumSis(1)
				oPointDataArray_to_mir = oPointDataArraysis_to_mir.Split(New Char() {"/"c })
				oSDA_to_wp_mir = oPointDataArray_to_mir(2)
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			End If
		End If
	Next
End Function
Function ChangeOccu(MakeNewFilu As Boolean)
	oEditingComp = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Pick an editing component")
	CUI_oOccname = oEditingComp.name : oCUI_oOccFullName = oEditingComp.ReferencedFileDescriptor.FullFileName

	Dim PurlinOpt() As String = {"76.1 purlin tube", "88.9 purlin tube", "76.1 purlin tube with LY08", "88.9 purlin tube with LY08", "80x80 profile tube" }

	If oCUI_oOccFullName.Contains("purlin") Then
		Result1 = InputListBox("Please select", PurlinOpt, PurlinOpt(4), Title := "Change tyoe", ListName := "List")

		Select Case Result1
			Case PurlinOpt(0) 'miten paikoituksen muutos
				oFileName = "purlin_tube.ipt"
				oSize = 76.1
			Case PurlinOpt(1)
				oFileName = "purlin_tube.ipt"
				oSize = 88.9
			Case PurlinOpt(2)
				oFileName = "purlin_tube_LY08.ipt"
				oSize = 76.1
			Case PurlinOpt(3)
				oFileName = "purlin_tube_LY08.ipt"
				oSize = 88.9
			Case PurlinOpt(4)
				oFileName = "purlin_profile2.ipt" 'mallina ko osa!
				oSize = 80
		End Select

		oSuffix = Abs(Now.GetHashCode)
		Component.Replace(oEditingComp.name, SaveFilu(oFileName, "purlin_" & oSuffix), False)

	End If



	'	oConstraints = oEditingComp.Constraints
	'	CUI_occTranMatrix = GetSelectedoccTransMatrix(oEditingComp)

	'	Try
	'		EkaConstraint = oConstraints(1)
	'		CUI_Cons1_name = EkaConstraint.name
	'		Logger.Debug("1 st Constraint:" & CUI_Cons1_name)
	'		occ1 = EkaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
	'		Try : occ1_2 = "/#" & EkaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
	'	Logger.Debug("OccurrenceOne 1:" & occ1 & occ1_2)

	'	occ2 = EkaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
	'	Try : occ2_2 = "/#" & EkaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
	'	Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)
	'	Logger.Debug("EntityOne:" & EkaConstraint.EntityOne.name)
	'	Logger.Debug("EntityTwo:" & EkaConstraint.EntityTwo.name)
	'	Catch
	'		Logger.Debug("Error 1st constraint")
	'	End Try

	'	Try
	'		TokaConstraint = oConstraints(2)
	'		CUI_Cons2_name = TokaConstraint.name
	'		Logger.Debug("2 nd const:" & CUI_Cons2_name)

	'		occ1 = TokaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
	'		Try : occ1_2 = "/" & TokaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
	'	Logger.Debug("OccurrenceOne 2:" & occ1 & occ1_2)

	'	occ2 = TokaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
	'	koe = TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name
	'	Try : occ2_2 = "/" & TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
	'	Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)

	'	Logger.Debug("EntityOne:" & TokaConstraint.EntityOne.name)
	'	Logger.Debug("EntityTwo:" & TokaConstraint.EntityTwo.name)
	'	Catch
	'		Logger.Debug("Error 2nd constraint")
	'	End Try

	'	Try
	'		KolmasConstraint = oConstraints(3)
	'		Logger.Debug("2 nd const:" & KolmasConstraint.name)
	'		Logger.Debug("AffectedOccurrenceOne:" & KolmasConstraint.AffectedOccurrenceOne.name)
	'		Logger.Debug("AffectedOccurrenceTwo:" & KolmasConstraint.AffectedOccurrenceTwo.name)
	'		Logger.Debug("EntityOne:" & KolmasConstraint.EntityOne.name)
	'		Logger.Debug("EntityTwo:" & KolmasConstraint.EntityTwo.name)
	'	Catch
	'		Logger.Debug("Error 3 rd constraint")
	'	End Try
End Function
Function SaveFilu(SrcFilu As String, NewFilu As String)
	oSrcPath = "C:\Vault_BH\Designs\Src\" : oDoc = ThisApplication.Documents.Open(oSrcPath & SrcFilu, False)
	oNewname = ThisDoc.Path & "\" & NewFilu & ".ipt"
	If IO.File.Exists(oNewname) Then
		Logger.Debug("File already exists : " & oNewname)
	Else
		Logger.Debug("New part File being created : " & oNewname)
		oDoc.SaveAs(oNewname, True)
	End If
	Return oNewname
End Function
Function GetUcsByNames(UCSinfo As String)
	'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
	'testi = GetUcsByNames("ex1/UCS2")
	oData = UCSinfo.Split(New Char() {"/"c })
	If oData.Length = 3 Then
		Dim oName As String() = {oData(0), oData(1) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(2))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
		End Try
		Exit Function
	ElseIf oData.Length = 2 Then
		Dim oName As String() = {oData(0) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(1))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try
	End If
	Return oUcs
End Function
Function SelectOccu()
	oEditingComp = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Pick an editing component")
	CUI_oOccname = oEditingComp.name : CUI_oOccFullName = oEditingComp.ReferencedFileDescriptor.FullFileName
	oConstraints = oEditingComp.Constraints
	CUI_occTranMatrix = GetSelectedoccTransMatrix(oEditingComp)

	Try
		EkaConstraint = oConstraints(1)
		CUI_Cons1_name = EkaConstraint.name
		Logger.Debug("1 st Constraint:" & CUI_Cons1_name)
		occ1 = EkaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
		Try : occ1_2 = "/#" & EkaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
	Logger.Debug("OccurrenceOne 1:" & occ1 & occ1_2)

	occ2 = EkaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
	Try : occ2_2 = "/#" & EkaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
	Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)
	Logger.Debug("EntityOne:" & EkaConstraint.EntityOne.name)
	Logger.Debug("EntityTwo:" & EkaConstraint.EntityTwo.name)
	Catch
		Logger.Debug("Error 1st constraint")
	End Try

	Try
		TokaConstraint = oConstraints(2)
		CUI_Cons2_name = TokaConstraint.name
		Logger.Debug("2 nd const:" & CUI_Cons2_name)

		occ1 = TokaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
		Try : occ1_2 = "/" & TokaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
	Logger.Debug("OccurrenceOne 2:" & occ1 & occ1_2)

	occ2 = TokaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
	koe = TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name
	Try : occ2_2 = "/" & TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
	Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)

	Logger.Debug("EntityOne:" & TokaConstraint.EntityOne.name)
	Logger.Debug("EntityTwo:" & TokaConstraint.EntityTwo.name)
	Catch
		Logger.Debug("Error 2nd constraint")
	End Try

	Try
		KolmasConstraint = oConstraints(3)
		Logger.Debug("2 nd const:" & KolmasConstraint.name)
		Logger.Debug("AffectedOccurrenceOne:" & KolmasConstraint.AffectedOccurrenceOne.name)
		Logger.Debug("AffectedOccurrenceTwo:" & KolmasConstraint.AffectedOccurrenceTwo.name)
		Logger.Debug("EntityOne:" & KolmasConstraint.EntityOne.name)
		Logger.Debug("EntityTwo:" & KolmasConstraint.EntityTwo.name)
	Catch
		Logger.Debug("Error 3 rd constraint")
	End Try
End Function
Function GetSelectedoccTransMatrix(Occu As ComponentOccurrence)
	showText = ""
	Dim oMatrix As Matrix = Occu.Transformation
	Dim i As Integer
	For i = 1 To 4
		Logger.Debug( _
		Round(oMatrix.Cell(i, 1), 1) & ", " & _
		Round(oMatrix.Cell(i, 2), 1) & ", " & _
		Round(oMatrix.Cell(i, 3), 1) & ", " & _
		Round(oMatrix.Cell(i, 4), 1))

		showText += Round(oMatrix.Cell(i, 1), 1) & ", "
		showText += Round(oMatrix.Cell(i, 2), 1) & ", "
		showText += Round(oMatrix.Cell(i, 3), 1) & ", "
		showText += Round(oMatrix.Cell(i, 4), 1) & ", " & vbLf
	Next
	Return showText
End Function
Function PlacePurlinCompoByTxtPara(TxtPara As String, Optional RoofNume As Integer = 1, Optional NodeNum As Integer = 1, Optional oNewOccName As String = "", Optional oFileName As String = "")
	'[listan käsittely
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String
	Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			If oParameterName = "SDA_from" Then
				If NodeNum = 1 Then
					oPointDataArraysis = oDatumSis(1)
				Else
					oPointDataArraysis = oDatumSis(1).Replace("_1", "_" & NodeNum) 'obs nimeäminen pisteille joint_purlin_k1_1
				End If
				oPointDataArray = oPointDataArraysis.Split(New Char() {"/"c })
				oSDA_from = oDatumSis(1)
			ElseIf oParameterName = "SDA_to" Then
				If NodeNum = 1 Then '/joint_purlin_k1_1
					oSDA_to = oDatumSis(1)
				Else
					oSDA_to = oDatumSis(1).Replace("_1", "_" & NodeNum)
				End If
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			End If
		End If
	Next
	']
	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If oFileName = "" Then
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End If
	If oNewOccName <> "" Then
		uusioOccName = oOccName & "_" & oNewOccName
		Components.Add(uusioOccName, oFullFileName)
		oOccName = uusioOccName
	End If
	If oNewOccName = "" Then
		PointtiHash = PointCoorHash("joint_")
		oPointsDistance = CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash)
		If Abs(oPointsDistance - oSDA_length) >100 Then
			Parameter(oOccName, "G_L") = oPointsDistance
			Logger.Debug("Changing G_L to :" & Round(oPointsDistance, 1) & " in " & oOccName)
		End If
	End If
	n = 0 ' obs tarkista assy level
	oConsName = oOccName & "#" & oPointDataArraysis
	Dim CompoArgu1 As ComponentArgument = {oPointDataArray(n), oPointDataArray(n + 1) }
	Dim CompoArgu2 As ComponentArgument = oOccName.ToString
	Try
		Constraints.AddUcsToUcs(oConsName, CompoArgu1, oPointDataArray(n + 2), CompoArgu2, "UCS", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara)
	End Try
End Function
Function PlaceBraceCompoByTxtPara(TxtPara As String, Optional oNewOccName As String = "", Optional oFileName As String = "") 'obs sama kuin addCompos
	'[listan käsittely
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String
	Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			If oParameterName = "SDA_from" Then
				oPointDataArraysis = oDatumSis(1)
				oPointDataArray = oPointDataArraysis.Split(New Char() {"/"c })
				oSDA_from = oDatumSis(1)
				oSDA_from_wp = oPointDataArray(2)
			ElseIf oParameterName = "SDA_to" Then
				oSDA_to = oDatumSis(1)
				oPointDataArraysis_to = oDatumSis(1)
				oPointDataArray_to = oPointDataArraysis_to.Split(New Char() {"/"c })
				Try : oSDA_to_wp = oPointDataArray_to(2) : Catch : Logger.Debug("Error oSDA_to_wp(2):") : End Try
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			End If
		End If
	Next
	']
	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If oFileName = "" Then
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End If
	If oNewOccName <> "" Then
		Components.Add(oNewOccName, oFullFileName)
		oOccName = oNewOccName
	End If
	PointtiHash = PointCoorHash("joint_")
	oPointsDistance = CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash)
	If oSDA_length = "" Then
		oSDA_length = 0
	End If
	If Abs(oPointsDistance - oSDA_length) >100 Then
		Parameter(oOccName, "G_L") = oPointsDistance
		Logger.Debug("Changing G_L to :" & Round(oPointsDistance, 1) & " in " & oOccName)
	End If
	n = 0 ' obs tarkista assy level
	oConsName = oOccName & "#" & oPointDataArraysis
	Dim CompoArguStartPoint As ComponentArgument = {oPointDataArray(n), oPointDataArray(n + 1) }
	Dim CompoArguEndPoint As ComponentArgument = {oPointDataArray_to(n), oPointDataArray_to(n + 1) }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
	Try
		Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start: Center Point", CompoArguStartPoint, oSDA_from_wp & ": Center Point")
		Constraints.AddMate(oConsName & "dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, oSDA_to_wp & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch
		Logger.Debug("Error in :" & TxtPara)
	End Try
End Function
Function PlaceWindBraceCompoByTxtPara(TxtPara As String, Optional oNewOccName As String = "", Optional oFileName As String = "")
	'[listan käsittely
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String
	Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			If oParameterName = "SDA_from" Then
				oPointDataArraysis = oDatumSis(1)
				oPointDataArray = oPointDataArraysis.Split(New Char() {"/"c })
				oSDA_from = oDatumSis(1)
				oSDA_from_wp = oPointDataArray(2)
			ElseIf oParameterName = "SDA_to" Then
				oSDA_to = oDatumSis(1)
				oPointDataArraysis_to = oDatumSis(1)
				oPointDataArray_to = oPointDataArraysis_to.Split(New Char() {"/"c })
				oSDA_to_wp = oPointDataArray_to(2)
			ElseIf oParameterName = "SDA_from_mir" Then
				oPointDataArraysis_mir = oDatumSis(1)
				oPointDataArray_mir = oPointDataArraysis_mir.Split(New Char() {"/"c })
				oSDA_from_mir = oDatumSis(1)
				oSDA_from_wp_mir = oPointDataArray_mir(2)
			ElseIf oParameterName = "SDA_to_mir" Then
				oSDA_to_mir = oDatumSis(1)
				oPointDataArraysis_to_mir = oDatumSis(1)
				oPointDataArray_to_mir = oPointDataArraysis_to_mir.Split(New Char() {"/"c })
				oSDA_to_wp_mir = oPointDataArray_to_mir(2)
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			End If
		End If
	Next
	']
	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If 'virheenkorjausta tyypitykseen
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If oSDA_length = "" Then
		oSDA_length = 0
	End If
	If oFileName = "" Then
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End If
	If oNewOccName <> "" Then
		Components.Add(oNewOccName, oFullFileName)
		oOccName = oNewOccName
	End If
	oOccName_mir = oOccName & "_mir" 'aina peilaus
	Components.Add(oOccName_mir, oFullFileName)
	PointtiHash = PointCoorHash("wind_brace_purlin_")
	oPointsDistance = CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash)
	If Abs(oPointsDistance - oSDA_length) >100 Then
		Parameter(oOccName, "G_L") = oPointsDistance
		Logger.Debug("Changing G_L to :" & Round(oPointsDistance, 1) & " in " & oOccName)
	End If
	n = 0 ' obs tarkista assy level
	oConsName = oOccName & "#" & oPointDataArraysis
	Dim CompoArguStartPoint As ComponentArgument = {oPointDataArray(n), oPointDataArray(n + 1) }
	Dim CompoArguEndPoint As ComponentArgument = {oPointDataArray_to(n), oPointDataArray_to(n + 1) }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
	Dim CompoArguStartPoint_mir As ComponentArgument = {oPointDataArray_mir(n), oPointDataArray_mir(n + 1) }
	Dim CompoArguEndPoint_mir As ComponentArgument = {oPointDataArray_to_mir(n), oPointDataArray_to_mir(n + 1) }
	Dim CompoArguJointOcc_mir As ComponentArgument = oOccName_mir.ToString
	Try
		Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start: Center Point", CompoArguStartPoint, oSDA_from_wp & ": Center Point")
		Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, oSDA_to_wp & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch
		Logger.Debug("Error in :" & TxtPara)
	End Try
	Try
		Constraints.AddMate(oConsName & "_point_mir", CompoArguJointOcc_mir, "ucs_start: Center Point", CompoArguStartPoint_mir, oSDA_from_wp_mir & ": Center Point")
		Constraints.AddMate(oConsName & "_dir_mir", CompoArguJointOcc_mir, "ucs_start: Y Axis", CompoArguEndPoint_mir, oSDA_to_wp_mir & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch
		Logger.Debug("Error in mir:" & TxtPara)
	End Try
	If End2Generate Then
	End If
End Function
Function PlaceCrossarmCompoByTxtPara(TxtPara As String, Optional RoofNum As Integer = 1, Optional NodeNum As Integer = 1, Optional oNewOccName As String = "", Optional Flippaus As Boolean = False, Optional oFileName As String = "")
	'[listan käsittely
	Dim AddedBraces As New ArrayList
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String
	Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			If oParameterName = "SDA_from" Then
				If NodeNum = 1 Then 'entä K2 & Node1
					oPointDataArraysis = oDatumSis(1)
				Else
					oPointDataArraysis = oDatumSis(1).Replace("_1", "_" & NodeNum) 'obs nimeäminen pisteille f3/p1k/joint_crossarm_k1_1
				End If
				oPointDataArray = oPointDataArraysis.Split(New Char() {"/"c })
				oSDA_from = oPointDataArraysis
				oSDA_from_wp = oPointDataArray(2)
			ElseIf oParameterName = "SDA_to" Then
				If NodeNum = 1 Then
					oSDA_to = oDatumSis(1)
					oPointDataArraysis_to = oDatumSis(1)
				Else
					oSDA_to = oDatumSis(1).Replace("_1", "_" & NodeNum)
					oPointDataArraysis_to = oDatumSis(1).Replace("_1", "_" & NodeNum) 'obs nimeäminen pisteille f3/p1k/joint_crossarm_k1_1
				End If
				oPointDataArray_to = oPointDataArraysis_to.Split(New Char() {"/"c })
				oSDA_to_wp = oPointDataArray_to(2)
			ElseIf oParameterName = "SDA_pcs" Then
				SDA_pcs = CInt(oDatumSis(1))
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			End If
		End If
	Next
	If SDA_pcs>0 Then 'ylös/alas brace -tilanne
		If oSDA_from.contains("_up_") Then
			oSDA_to = oSDA_from.replace("_up_", "_")
		Else
			oSDA_to = Left(oSDA_from, InStrRev(oSDA_from, "_", -1)) & "up_" & Right(oSDA_from, Len(oSDA_from) -InStrRev(oSDA_from, "_", -1))
		End If
		oPointDataArray_to = oSDA_to.Split(New Char() {"/"c })
		oSDA_to_wp = oPointDataArray_to(2)
	End If
	']
	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If oSDA_length = "" Then : oSDA_length = 0 : End If
	If oFileName = "" Then
		Try
			oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		Catch
			SaveFilu("purlin_tube_LY08.ipt", oOccName) 'kysely ehkä tähän
		End Try
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End If
	If oNewOccName <> "" Then
		uusioOccName = oOccName & "_" & oNewOccName
		Components.Add(uusioOccName, oFullFileName)
		oOccName = uusioOccName
	End If
	If SDA_pcs = 0 Then
		n = 0 ' obs tarkista assy level
		oConsName = oOccName & "#" & oPointDataArraysis
		Dim CompoArguStartPoint As ComponentArgument = {oPointDataArray(n), oPointDataArray(n + 1) }
		Dim CompoArguEndPoint As ComponentArgument = {oPointDataArray_to(n), oPointDataArray_to(n + 1) }
		Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
		Try
			Constraints.AddUcsToUcs(oConsName, CompoArguStartPoint, oSDA_from_wp, CompoArguJointOcc, "ucs_start", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
		Catch ex As Exception
			Logger.Debug("Error in :" & TxtPara)
		End Try
	End If
	laskuri = 0 : StoredoOccName = oOccName
	If SDA_pcs>0 Then 'Or oSDA_to.contains("_up_") oSDA_from.contains("_up_") 
		For l = 1 To SDA_pcs 'RoofNum
			StartFrameoNum = oPointDataArray(n).Replace("f", "") + laskuri
			EndFrameoNum = StartFrameoNum + 1
			If RoofNum>1 Then
				oRoofName = oPointDataArray(n + 1).Replace("p1", "p" & RoofNum)
			Else
				oRoofName = oPointDataArray(n + 1)
			End If
			oConsName = oOccName & "#" & oPointDataArraysis & "#" & StartFrameoNum & "_" & EndFrameoNum
			If l>1 Then 'ei ekaan
				uusioOccName = StoredoOccName & "_n" & l
				Components.Add(uusioOccName, oFullFileName)
				oOccName = uusioOccName
			End If
			Dim CompoArguStartPoint As ComponentArgument = {"f" & StartFrameoNum, oRoofName }
			Dim CompoArguEndPoint As ComponentArgument = {"f" & EndFrameoNum, oRoofName }
			Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
			oSDA_from_wp = oSDA_from.Split(New Char() {"/"c })(2)
			oSDA_to_wp = oSDA_to.Split(New Char() {"/"c })(2)
			If oNewOccName = "" And laskuri = 0 Then 'pituuden laskenta vain kerran
				PointtiHash = PointCoorHash("joint_")
				oPointsDistance = CalcPointsDistance("f" & StartFrameoNum & "/" & oRoofName & "/" & oSDA_from_wp, "f" & EndFrameoNum & "/" & oRoofName & "/" & oSDA_to_wp, PointtiHash)
				If Abs(oPointsDistance - oSDA_length) >100 Then
					Parameter(oOccName, "G_L") = oPointsDistance
					Logger.Debug("Changing G_L to :" & Round(oPointsDistance, 1) & " in " & oOccName)
				End If
			End If
			If laskuri Mod 2 = 0 And Flippaus Then 'parillinen ja kääntö 
				lahtoPiste = oSDA_to_wp
				loppuPiste = oSDA_from_wp
			ElseIf laskuri Mod 2 = 1 And Flippaus = False '(=pariton ei kääntöä siis myös)
				lahtoPiste = oSDA_to_wp
				loppuPiste = oSDA_from_wp
			Else
				lahtoPiste = oSDA_from_wp
				loppuPiste = oSDA_to_wp
			End If
			'			Logger.Debug("oSDA_from_wp:" & "f" & StartFrameoNum & "/" & oRoofName & "/" & lahtoPiste) : Logger.Debug("oSDA_to_wp:" & "f" & EndFrameoNum & "/" & oRoofName & "/" & loppuPiste)
			Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch : Logger.Debug("Error:" & testi) : End Try
			Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch : Logger.Debug("Error:" & testi) : End Try
			Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch : Logger.Debug("Error:" & testi) : End Try
			laskuri += 1
		Next
	End If
End Function
Function PointCoorHash(sWPFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument
	oAsmDoc = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition
	oAsmDef = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'Debug.Print thisOcc.Name
		'skip suppressed components
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					If InStr(1, currentWP.Name, sWPFilter) Then
						Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
						X = Round(oAsmPoint.point.X * 10, 1)
						Y = Round(oAsmPoint.point.Y * 10, 1)
						Z = Round(oAsmPoint.point.Z * 10, 1)
						If Debuggaus Then
							Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
						End If
						oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
					End If
				Next
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								'check if pointname contains underscore
								If InStr(1, currentSubWP.Name, sWPFilter) Then
									Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
									X = oAsmPoint.point.X * 10
									Y = oAsmPoint.point.Y * 10
									Z = oAsmPoint.point.Z * 10
									If Debuggaus Then
										Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
										oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
									End If
									oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
								End If
							Next
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = oAsmPoint.point.X * 10
												Y = oAsmPoint.point.Y * 10
												Z = oAsmPoint.point.Z * 10

												If Debuggaus Then
													Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
													oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												End If
												oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, {X, Y, Z })
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next
	If Debuggaus Then
		CoordDataa = oDataTeksti
		My.Computer.Clipboard.SetText(oDataTeksti)
	End If
	Return oHashtable
End Function
Function CalcPointsDistance(StartPoint As String, EndPoint As String, PointtiHash As Hashtable, Optional PointSuffix As String = ": Center Point", Optional Debuggaus As Boolean = False)
	'PointtiHash = PointCoorHash("joint_")
	'koe = CalcPointsDistance("f1/p1k/joint_purlin_k1_1", "f3/p1k/joint_purlin_k1_1", PointtiHash)
	oStartPoint = PointtiHash(StartPoint & PointSuffix) ' ucs default center point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & StartPoint & " using zero point instead")
		oStartPoint = {0, 0, 0 }
	End If
	oEndPoint = PointtiHash(EndPoint & PointSuffix)
	If oEndPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & oEndPoint & " using zero point instead")
		oEndPoint = {0, 0, 0 }
	End If
	CalcPointsDistance = ((oEndPoint(0) -oStartPoint(0)) ^ 2 + (oEndPoint(1) -oStartPoint(1)) ^ 2 + (oEndPoint(2) -oStartPoint(2)) ^ 2) ^ 0.5
End Function