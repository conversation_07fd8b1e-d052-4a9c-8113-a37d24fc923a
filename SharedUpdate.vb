Sub main()
SharedVariable("PreventPartAccCalc") = 1
wfBoolean = InputRadioBox("Update parameters", "Update ALL", "Update without Sovelia search", True, "Needed Shared Variables")
'wfBoolean = True 'override vähän turha, mutta ei saa vastausta aina?!

StartTime = Now
If wfBoolean = True Then 'alias Reset
	FName = ThisDoc.FileName(False)
	ProjectNumCal = Left(FName, InStrRev(FName, "_") -1)
	SharedVariable("ProjectNum") = ProjectNumCal
	ProjectNum = ProjectNumCal
	Try
		List = SoveliaSearh(ProjectNum, "SHORTDESCRIPTION") 'tarkista missä vaiheessa kaikki laskettu
		Dim TulosData() As String = List(0).Split(":"c) 'ekassa varmaan tieto testaa
		ProjectShortDescription = TulosData(1)
		SharedVariable("ProjectShortDescription") = ProjectShortDescription
	Catch
		Logger.Debug("No sovelia connection")
		ProjectShortDescription = "WOIK_"
	End Try
Else 'update
	Try
		ProjectNum = SharedVariable("ProjectNum")
		iProperties.Value("Project", "Project") = SharedVariable("ProjectNum") ' tämä muuttuja laskettu pääätasolla&tallennettu istunnon ajaksi shared var
	Catch
		Logger.Debug("No shared variable for project number, use reset option instead..")
	End Try
	Try : ProjectShortDescription = SharedVariable("ProjectShortDescription") : Catch : End Try
End If

iProperties.Value("Project", "Project") = ProjectNum
iProperties.Value("Custom", "ProjectShortDescription") = ProjectShortDescription
iLogicVb.RunRule("ParametrinHaku")
iLogicVb.RunRule("UcsOccu")
openRuniLogicRuleByOccName("CalHeight", {"f1" })
iLogicVb.UpdateWhenDone = True

'iLogicVb.RunRule("CoordListDrw")
Logger.Debug("SharedUpdate tot : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function SoveliaSearh(SearchID As String, resultCriteria As String)
	Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
	_addInInterface = addin.Automation
	Dim searchCriteria As New List(Of String)()
	Dim resultLimit As Integer
	searchCriteria.Add("DocID:" & SearchID)
	resultLimit = 2
	Dim task As System.Threading.Tasks.Task(Of String)
	Try
		task = _addInInterface.Search(searchCriteria, resultCriteria, resultLimit)
	Catch
		Logger.Debug("No sovelia connection")
		Exit Function
	End Try
	Dim timeout As Integer = 15000

	Dim awaiter As System.Runtime.CompilerServices.TaskAwaiter(Of String) = task.GetAwaiter()
	awaiter.GetResult()
	Dim oResult As String = awaiter.GetResult()
	Try
		If task.Wait(timeout) Then
			oResult = task.Result
		Else
			Logger.Debug("The search operation timed out.")
		End If
	Catch
		Logger.Debug("Timeloop error " & SearchID)
	End Try

	Dim TxtData() As String = oResult.Split(";"c)
	Return TxtData
End Function

Function openRuniLogicRuleByOccName(RuleName As String, Filtteri() As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Dim myArrayList As New ArrayList
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		For Each oFiltteri In Filtteri
			If oOccurrence.Name = oFiltteri Then
				oFullName = oOccurrence.ReferencedFileDescriptor.FullFileName
				oDoc = ThisApplication.Documents.Open(oFullName, True)
				Try
					Call iLogic.RunRule(oDoc, RuleName)
				Catch
					Logger.Debug("Unable to run the rule")
				Finally
					oDoc = Nothing
				End Try
				ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
				Exit Function
			End If
		Next
	Next
End Function