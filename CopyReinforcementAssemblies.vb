Sub Main()
Try : oTest = SharedVariable("ProjectNum") : Catch : Logger.Debug("Shared variable ProjectNum is missing. Start Get Data command before") : Exit Sub : End Try

Dim oDimDictWall As New Dictionary(Of String, Object) From {
{"reinforcement_tube_dist_cl", reinforcement_tube_dist_cl },
{"Type", "Wall" }
}
Dim oDimDictRoof As New Dictionary(Of String, Object) From {
{"reinforcement_tube_dist_cl", reinforcement_tube_dist_cl },
{"Type", "Roof" },
{"RoofNum", "1" }
}

Dim oDimDictRidge As New Dictionary(Of String, Object) From {
{"reinforcement_tube_dist_cl", reinforcement_tube_dist_cl },
{"Type", "Ridge" }
}


MainAssemblyMir = "C:\Vault_BH\Designs\Src\gable_end_pillar\Reinforcement_tube_double_mir.iam"
sModelMir = CopyFiles(MainAssemblyMir, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"s", ProjectNum }, oDimDictWall)

MainAssembly = "C:\Vault_BH\Designs\Src\gable_end_pillar\Reinforcement_tube_double.iam"
sModel = CopyFiles(MainAssembly, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"s", ProjectNum }, oDimDictWall)

Dim myWallList As New List(Of String)

myWallList.Add(sModelMir)
myWallList.Add(sModel)

Dim myRoofList As New List(Of String)

For i = 1 To e_roof_pcs
	If Parameter("e_roof_" & i & "_fold_ang") = 0
		RoofK2 = False ' suora katto
	Else
		RoofK2 = True
	End If

	If RoofK2 Then
		k1ModelMir = CopyFiles(MainAssemblyMir, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"k" & i, ProjectNum }, oDimDictRoof)
		k1Model = CopyFiles(MainAssembly, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"k" & i, ProjectNum }, oDimDictRoof)

	Else 'suora
		MainAssembly = "C:\Vault_BH\Designs\Src\gable_end_pillar\Reinforcement_tube_direct.iam"
		CopyFiles(MainAssembly, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"k" & i, ProjectNum }, oDimDictRoof)
		CopyFiles(MainAssembly, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"k" & i, ProjectNum }, oDimDictRoof) ' tarvitaanko?!
	End If
	myRoofList.Add(k1ModelMir)
	myRoofList.Add(k1Model)
Next

Dim myRidgeList As New List(Of String)

If e_ridge_width>100 Then
	MainAssembly = "C:\Vault_BH\Designs\Src\gable_end_pillar\Reinforcement_tube_doubles.iam"
	hModel = CopyFiles(MainAssembly, {"Reinforcement_tube.ipt", "Reinforcement_tube2.ipt", "Reinforcement_aux_tube.ipt", "Reinforcement_aux_tube_2.ipt" }, {"h", ProjectNum }, oDimDictWall)
	myRidgeList.Add(hModel)
End If

PlaceContrains("wall", myWallList)
PlaceContrains("roof", myRoofList)

If e_ridge_width>100 Then
	PlaceContrains("ridge", myRidgeList)
End If

End Sub

Function PlaceContrains(oType As String, oFiles As List(Of String))
	LastFrameNum = totalFramePcs
	If oType = "wall" Then
		PeiliAssy = oFiles(0) : NormAssy = oFiles(1)
		Dim Reinforcement_tube_1 = Components.Add("Reinforcement_tube_assy_s_1", NormAssy)
		Constraints.AddUcsToUcs("1", {"f1", "s" }, "InsertionPoint", Reinforcement_tube_1.Name, "InsertionPoint_wall")

		Dim Reinforcement_tube_mir_1 = Components.Add("Reinforcement_tube_assy_s_1_mir", PeiliAssy)
		Constraints.AddUcsToUcs("1_mir", {"f1", "sMir" }, "InsertionPoint", Reinforcement_tube_mir_1.Name, "InsertionPoint_wall")
		If isEndSame = False Then
			Dim Reinforcement_tube_mir_n = Components.Add("Reinforcement_tube_assy_s_n_mir", PeiliAssy)
			Constraints.AddUcsToUcs("n_mir", {"f" & LastFrameNum, "s" }, "InsertionPoint", Reinforcement_tube_mir_n.Name, "InsertionPoint_wall")

			Dim Reinforcement_tube_n = Components.Add("Reinforcement_tube_assy_s_n", NormAssy)
			Constraints.AddUcsToUcs("n", {"f" & LastFrameNum, "sMir" }, "InsertionPoint", Reinforcement_tube_n.Name, "InsertionPoint_wall")
		End If

	ElseIf oType = "roof" Then

		For j = 1 To oFiles.Count / 2
			'			PeiliAssy = oFiles(j - 1) : NormAssy = oFiles(j)
			PeiliAssy = oFiles((j - 1) * 2) : NormAssy = oFiles(2 * j - 1)

			Dim Reinforcement_tube_dPos = ThisAssembly.Geometry.Point(0, 0, 0)

			Dim Reinforcement_tube_k = Components.Add("Reinforcement_tube_assy_k_" & j, NormAssy, position := Reinforcement_tube_dPos)
			Constraints.AddUcsToUcs("k" & j, {"f1", "p" & j & "k" }, "InsertionPoint", Reinforcement_tube_k.Name, "InsertionPoint_roof")

			Dim Reinforcement_tube_k_mir = Components.Add("Reinforcement_tube_assy_k_mir_" & j, PeiliAssy, position := Reinforcement_tube_dPos)
			Constraints.AddUcsToUcs("kMir" & j, {"f1", "p" & j & "kMir" }, "InsertionPoint", Reinforcement_tube_k_mir.Name, "InsertionPoint_roof")
			If isEndSame = False Then
				Dim Reinforcement_tube_k_mir_n = Components.Add("Reinforcement_tube_assy_k_mir_n" & j, PeiliAssy)
				Constraints.AddUcsToUcs("k_n_mir" & j, {"f" & LastFrameNum, "p" & j & "k" }, "InsertionPoint", Reinforcement_tube_k_mir_n.Name, "InsertionPoint_roof")

				Dim Reinforcement_tube_k_n = Components.Add("Reinforcement_tube_assy_k_n" & j, NormAssy)
				Constraints.AddUcsToUcs("k_n" & j, {"f" & LastFrameNum, "p" & j & "kMir" }, "InsertionPoint", Reinforcement_tube_k_n.Name, "InsertionPoint_roof")
			End If
		Next

	ElseIf oType = "ridge" Then
		Dim Reinforcement_tube_h_1 = Components.Add("Reinforcement_tube_assy_h_1", oFiles(0))
		Constraints.AddUcsToUcs("h_1", {"f1", "h" }, "InsertionPoint", Reinforcement_tube_h_1.Name, "InsertionPoint_ridge")
		If isEndSame = False Then
			Dim Reinforcement_tube_h_n = Components.Add("Reinforcement_tube_assy_h_n", oFiles(0))
			Constraints.AddFlush("h_n", {"f" & LastFrameNum, "h" }, "InsertionPoint: XY Plane", Reinforcement_tube_h_n.Name, "InsertionPoint_ridge: XY Plane")
			Constraints.AddMate("h_n_XZ", {"f" & LastFrameNum, "h" }, "InsertionPoint: XZ Plane", Reinforcement_tube_h_n.Name, "InsertionPoint_ridge: XZ Plane")
			Constraints.AddMate("h_n_YZ", {"f" & LastFrameNum, "h" }, "InsertionPoint: YZ Plane", Reinforcement_tube_h_n.Name, "InsertionPoint_ridge: YZ Plane")
		End If
	End If
End Function

Function ConvertStr_CArgu(OccName As String) As ComponentArgument
	ConvertStr_CArgu = OccName
End Function

Function CopyFiles(Assy2Copy As String, oFiles As String(), LoppuJutut As String(), DimDict As Dictionary(Of String, Object))
	Dim myHashtable As New Dictionary(Of String, String)
	Dim CopyDoc As Document
	oPathMain = Left(Assy2Copy, InStrRev(Assy2Copy, "\", -1) + 1 - 2)
	If oFiles(0) = "" Then
		Try
			CopyDoc = ThisApplication.Documents.Open(Assy2Copy, False)
		Catch
		End Try
		AllRefDocs = CopyDoc.AllReferencedDocuments
		For Each oRefDoc In AllRefDocs
			Try
				myHashtable.Add(oRefDoc.FullFileName, oNewFileNameCalc(oRefDoc.FullFileName, LoppuJutut)) ': Logger.Debug(oRefDoc.FullFileName)
			Catch
			End Try
		Next
	Else
		For Each oVal In oFiles
			Try
				myHashtable.Add(oPathMain & "\" & oVal, oNewFileNameCalc(oPathMain & "\" & oVal, LoppuJutut)) ' oletuksena että samassa polussa 
			Catch
			End Try
		Next
	End If

	oNewMainAssy = oNewFileNameCalc(Assy2Copy, LoppuJutut)
	IOCopy(Assy2Copy, oNewMainAssy)
	For Each oKey In myHashtable.Keys
		IOCopy(oKey, myHashTable(oKey))
	Next
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation : RuleName = "ReplaceRefsByList"
	Try
		CopyDoc = ThisApplication.Documents.Open(oNewMainAssy, True)
	Catch
		Logger.Debug("Error not able to open (Cache?!) :" & oNewMainAssy)
		Exit Function
	End Try
	Dim oCompDef As AssemblyComponentDefinition = CopyDoc.ComponentDefinition
	Dim oParameters As Parameters = oCompDef.Parameters
	Dim paramName As String = "Parts2replace" : Dim paramName2 As String = "Dims2model"
	Dim paramValue As String = String.Join(";", myHashtable.Select(Function(kvp) kvp.Key & "=" & kvp.Value))
	Dim paramValue2 As String = String.Join(";", DimDict.Select(Function(kvp) kvp.Key & "=" & kvp.Value))

	Dim oParameter As Parameter = Nothing: Dim oParameter2 As Parameter = Nothing
	Dim paramExists As Boolean = False: Dim paramExists2 As Boolean = False
	For Each param In oParameters
		If param.Name = paramName Then
			oParameter = param
			paramExists = True
			Continue For
		End If
		If param.Name = paramName2 Then
			oParameter2 = param
			paramExists2 = True
			Continue For
		End If
	Next

	'    Try
	If paramExists Then
		oParameter.Value = paramValue
	Else ' Add a new text parameter
		oParameter = oParameters.UserParameters.AddByValue(paramName, paramValue, UnitsTypeEnum.kTextUnits)
	End If
	If paramExists2 Then
		oParameter2.Value = paramValue2
	Else ' Add a new text parameter
		oParameter2 = oParameters.UserParameters.AddByValue(paramName2, paramValue2, UnitsTypeEnum.kTextUnits)
		'oParameter2 = oParameters.UserParameters.AddByValue(paramName2, """" & paramValue2 & """", UnitsTypeEnum.kTextUnits)
	End If
	Try
		Call iLogic.RunRule(CopyDoc, RuleName)
	Catch
		Logger.Debug("Unable to run the rule")
	Finally
		oDoc = Nothing
	End Try
	Return oNewMainAssy
End Function

Function oNewFileNameCalc(oSrcFilu As String, oSuffis As String())
	If oSrcFilu Is Nothing Then
		Exit Function
	End If
	FNamePos = InStrRev(oSrcFilu, "\", -1) + 1
	oName = Mid(oSrcFilu, FNamePos, Len(oSrcFilu) -FNamePos - 3)
	For Each oSuffix In oSuffis
		oTxtSuffix += "_" & oSuffix
	Next
	oNewFileNameCalc = ThisDoc.Path & "\" & oName & oTxtSuffix & Right(oSrcFilu, 4)
End Function
Function IOCopy(oSrcFilu As String, newFilu As String)
	Try
		System.IO.File.Copy(oSrcFilu, newFilu, True)
		attributes = System.IO.File.GetAttributes(newFilu) 'LISÄÄ PIIRUSTUS
		If (attributes And System.IO.FileAttributes.ReadOnly) = System.IO.FileAttributes.ReadOnly Then
			attributes = attributes And Not System.IO.FileAttributes.ReadOnly
			System.IO.File.SetAttributes(newFilu, attributes)
		End If
	Catch ex As Exception
		Logger.Debug(ex.Message)
	End Try
End Function
Function GetAllOccurance(oDoc As AssemblyDocument, Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	'Dim oDoc As Document = ThisApplication.Documents.Open(MainAssembly, False)
	'oAllOccur = GetAllOccurance(oDoc)
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Dim oOcc As ComponentOccurrence : Dim TunnisteTaulu As Hashtable = New Hashtable
	For Each oOcc In oDoc.ComponentDefinition.Occurrences
		If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If oOcc.SubOccurrences.Count = 0 Then
				oAllOccur.Add(oOcc)
			Else
				oAllOccur.Add(oOcc)
				processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
			End If
		End If
	Next
	Return oAllOccur
End Function
Function processAllSubOcc(ByVal oOcc As ComponentOccurrence, oAllOccur As ObjectCollection, Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oSubCompOcc As ComponentOccurrence
	For Each oSubCompOcc In oOcc.SubOccurrences
		If oSubCompOcc.SubOccurrences.Count = 0 Then
			oAllOccur.Add(oSubCompOcc)
		Else
			oAllOccur.Add(oSubCompOcc)
			processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
		End If
	Next
End Function