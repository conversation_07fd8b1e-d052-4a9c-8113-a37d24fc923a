Imports System.IO
Sub main()
'koe = Dialog("ipt")

'FileEndit = FileList("*.i*", "C:\Vault_BH\Designs\Proj\5414-10")

'MultiValue.List("end_a_sketch") = FileEndit
'MultiValue.List("end_b_sketch") = FileEndit
'MultiValue.List("end_a_model") = FileEndit
'MultiValue.List("end_b_model") = FileEndit

Dim A_B_Filters As String() = {"XJ_PNum_calc", "XO_PNum_calc","PO_PNum_calc","DIA_PNum_calc","RP_PNum_calc","OP_PNum_calc","PP_PNum_calc"}
CopyParams(A_B_Filters, end_a_model, end_b_model)


End Sub

Function CopyParams(oFilters As String(), Optional Sorsa As String = "", Optional Kohde As String = "")
		If Sorsa = "" Then
		Dim openTopLevelDocuments As New ArrayList
		' Loop through all open documents in Inventor
		For Each doc As Document In ThisApplication.Documents
			Dim isReferenced As Boolean = False
			' Loop through other open documents to check if the current document is referenced
			For Each otherDoc As Document In ThisApplication.Documents
				If otherDoc IsNot doc Then
					For Each ref In otherDoc.ReferencedDocuments
						If ref Is doc Then
							isReferenced = True
							Exit For
						End If
					Next
				End If
				If isReferenced Then Exit For
			Next
			' If the document is not referenced by any other document, add it to the top-level list
			If Not isReferenced Then
				openTopLevelDocuments.Add(doc.DisplayName)
			End If
		Next
		sourceDocName = InputListBox("Select one.", openTopLevelDocuments, openTopLevelDocuments.Item(0), "Source Document", "Copy params values")
		targetDocName = InputListBox("Select one.", openTopLevelDocuments, openTopLevelDocuments.Item(0), "Target Document", "Copy params values")
	Else
		sourceDocName = Sorsa
		targetDocName = Kohde
	End If

	Dim sourceDoc As Document = ThisApplication.Documents.ItemByName(sourceDocName)
	Dim targetDoc As Document = ThisApplication.Documents.ItemByName(targetDocName)

	Dim sourceParams As Parameters = sourceDoc.ComponentDefinition.Parameters
	Dim targetParams As Parameters = targetDoc.ComponentDefinition.Parameters
	Dim targetParam As Parameter

	' Loop through each parameter in the source document and copy it to the target document
	For Each param As Parameter In sourceParams
		For Each oFilter In oFilters
			If param.Name = oFilter Then
				Try
					' Check if the parameter exists in the target document
					targetParam = targetParams.Item(param.Name)
					' Copy the value from the source parameter to the target parameter
					targetParam.Value = param.Value
				Catch ex As Exception
					' If the parameter does not exist in the target, ignore it or handle it as needed
					Logger.Debug("Parameter " & param.Name & " does not exist in the target document.")
				End Try
			End If
		Next
	Next

	MessageBox.Show("Parameter values copied successfully.")
End Function


Function Dialog(oFilter As String)
	Dim oFileDlg As Inventor.FileDialog = Nothing
	InventorVb.Application.CreateFileDialog(oFileDlg)
	oDisplayTitle = oFilter.ToUpper
	oFileDlg.Filter = oDisplayTitle & " (*." & oFilter & ")|*." & oFilter
	oFileDlg.DialogTitle = oDisplayTitle & " File"
	oFileDlg.InitialDirectory = ThisDoc.Path
	oFileDlg.CancelError = True
	On Error Resume Next
	oFileDlg.ShowOpen()
	If Err.Number <> 0 Then
		MessageBox.Show("Not valid file", "Dialog Cancellation")
	ElseIf oFileDlg.FileName <> "" Then
		selectedfile = oFileDlg.FileName
	End If
	Return selectedfile
End Function

Function FileList(fileExtension As String, Optional folderPathOpt As String = "")
	Dim ModelList As New ArrayList

	If folderPathOpt = "" Then
		folderPath = ThisDoc.Path
	Else
		folderPath = folderPathOpt
	End If

	Dim Subfiles As String() = Directory.GetFiles(folderPath, fileExtension, SearchOption.AllDirectories)


	For Each File As String In Subfiles
		oFileName = System.IO.Path.GetFileName(File)
		Dim fileDirectory As String = System.IO.Path.GetDirectoryName(File)
		If fileDirectory.Length > folderPath.Length AndAlso Not File.Contains("OldVersions") Then

			If oFileName.contains("End_") and not oFileName.contains(".v") and not oFileName.contains(".idw") Then
				ModelList.Add(File)
			End If



		End If
	Next
	Return ModelList
End Function