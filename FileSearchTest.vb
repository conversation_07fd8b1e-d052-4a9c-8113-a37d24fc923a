Imports System.IO
Sub main()
Dim oFilters As String() = {"*.ipt", ".iam" }
oFiles = SearchAndAddToListWithFilter(ThisDoc.Path, oFilters, True)

For Each oVal In oFiles
	Logger.Debug("oVal:" & oVal.FullName)
Next
End Sub

Function SearchAndAddToListWithFilter(ByVal path As String, ByVal filters As String(), ByVal searchSubFolders As Boolean) As List(Of IO.FileInfo)

	If Not IO.Directory.Exists(path) Then
		Throw New Exception("Path not found")
	End If

	Dim searchOptions As IO.SearchOption
	If searchSubFolders Then
		searchOptions = IO.SearchOption.AllDirectories
	Else
		searchOptions = IO.SearchOption.TopDirectoryOnly
	End If

	Return filters.SelectMany(Function(filter) New IO.DirectoryInfo(path).GetFiles(filter, searchOptions)).ToList
End Function
	