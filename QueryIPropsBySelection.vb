Imports System.Collections ' Required for ArrayList
Imports System.Text.RegularExpressions ' Optional but good for pattern matching
Sub Main()
Dim oOcc As ComponentOccurrence = Nothing
' --- 1. Select Component ---
Try
	oOcc = ThisApplication.CommandManager.Pick( _
	SelectionFilterEnum.kAssemblyLeafOccurrenceFilter, _
	"Select a component to view its properties")
Catch ' Handle user cancelling the pick
	Return ' Exit the rule quietly
End Try
If oOcc Is Nothing Then
	MessageBox.Show("No component selected.", "Selection Info")
	Return ' Exit the rule
End If
' --- 2. Prepare Data List ---
Dim propList As New ArrayList
propList.Add("--- Component: " & oOcc.Name & " ---") ' Header for the component name
Try : oFullName = oOcc.ReferencedFileDescriptor.FullFileName : Catch : End Try
propList.Add(oFullName)
' --- 3. Get Specific Custom iProperties ---
Dim PartNumber As String = GetiProp(oOcc.Name, "Project", "Part Number")
Dim Description As String = GetiProp(oOcc.Name, "Project", "Description")
Dim StockNumber As String = GetiProp(oOcc.Name, "Project", "Stock Number")
Dim Category As String = GetiProp(oOcc.Name, "Summary", "Category")

Dim SDA_RAWMATERIAL As String = GetiProp(oOcc.Name, "Custom", "SDA_RAWMATERIAL")
Dim SDA_BASECODE As String = GetiProp(oOcc.Name, "Custom", "SDA_BASECODE")
Dim PHANTOM_ASSY_GEN As String = GetiProp(oOcc.Name, "Custom", "PHANTOM_ASSY_GEN")
Dim Weight As String = GetiProp(oOcc.Name, "Custom", "Weight")

propList.Add(" ") ' Add a blank line for spacing
propList.Add("--- iProperties ---")
propList.Add("Part Number : " & PartNumber)
propList.Add("Description : " & Description)
propList.Add("Stock Number : " & StockNumber)
propList.Add("Category : " & Category)
propList.Add("SDA_RAWMATERIAL : " & SDA_RAWMATERIAL)
propList.Add("SDA_BASECODE : " & SDA_BASECODE)
propList.Add("PHANTOM_ASSY_GEN : " & PHANTOM_ASSY_GEN)
propList.Add("Mass (INV) : " & Round(iProperties.MassOfComponent(oOcc.Name), 2))
propList.Add("Weight : " & Weight)

propList.Add(" ") ' Add a blank line for spacing

' --- 4. Get Parameters from the Component's Definition Document ---
Dim oDefDoc As Document = Nothing
Dim foundUserParams As Boolean = False
Dim foundModelParams As Boolean = False ' Flag for tracking filtered model params

Try
	' Access the document that defines this component occurrence
	oDefDoc = oOcc.Definition.Document

	If oDefDoc IsNot Nothing Then
		Dim oParams As Parameters = oDefDoc.ComponentDefinition.Parameters
		' --- 4a. Get User Parameters ---
		propList.Add("--- User Parameters (from Definition) ---")
		Dim oUserParams As UserParameters = oParams.UserParameters
		If oUserParams.Count > 0 Then
			For Each oUserParam As UserParameter In oUserParams
				Try
					Dim paramInfo As String = FormatParameter(oUserParam)
					propList.Add(paramInfo)
					foundUserParams = True
				Catch exParam As Exception
					propList.Add("  Error reading user parameter '" & oUserParam.Name & "': " & exParam.Message)
				End Try
			Next
		End If
		If Not foundUserParams Then
			propList.Add("<No user parameters found>")
		End If
		propList.Add(" ") ' Add a blank line for spacing
		' --- 4b. Get Filtered Model Parameters ---
		propList.Add("--- Renamed/Custom Model Parameters (Not d#) ---")
		Dim oModelParams As ModelParameters = oParams.ModelParameters
		If oModelParams.Count > 0 Then
			For Each oModelParam As ModelParameter In oModelParams
				Try
					' --- Filter Logic ---
					' Check if the name starts with 'd' (case-insensitive) 
					' AND the rest of the string after 'd' is purely numeric.
					Dim isDefaultPattern As Boolean = False
					If oModelParam.Name.StartsWith("d", StringComparison.OrdinalIgnoreCase) AndAlso oModelParam.Name.Length > 1 Then
						If IsNumeric(oModelParam.Name.Substring(1)) Then
							isDefaultPattern = True
						End If
					End If
					' --- Alternative using Regular Expression (more robust) ---
					' Uncomment the next line and comment out the block above for Regex
					' Dim isDefaultPattern As Boolean = Regex.IsMatch(oModelParam.Name, "^d\d+$", RegexOptions.IgnoreCase) 

					' --- Add to list if NOT the default pattern ---
					If Not isDefaultPattern Then
						Dim paramInfo As String = FormatParameter(oModelParam) ' Use helper function
						propList.Add(paramInfo)
						foundModelParams = True
					End If
				Catch exParam As Exception
					propList.Add("  Error reading model parameter '" & oModelParam.Name & "': " & exParam.Message)
				End Try
			Next
		End If
		If Not foundModelParams Then
			propList.Add("<No renamed/custom model parameters found>")
		End If
		propList.Add(" ") ' Add a blank line for spacing
	Else
		propList.Add("<Could not access definition document for parameters>")
	End If
Catch exDoc As Exception
	propList.Add("Error accessing component definition or parameters: " & exDoc.Message)
End Try
' --- 5. Display Data using InputListBox ---
Dim selectedItem As Object ' Variable to hold the return value (not used here)
selectedItem = InputListBox("Component Properties: " & oOcc.Name, propList, propList(0), _
"Component Data Viewer", "Properties & Parameters List")


If selectedItem Is Nothing Then Exit Sub

If selectedItem.contains(".ipt") Or selectedItem.contains(".iam") Then
	Try : oDoc = ThisApplication.Documents.Open(selectedItem, True) : Catch : End Try
ElseIf selectedItem.contains(" : ") Then
	Dim splitParts() As String = selectedItem.Split(New String() {" : " }, StringSplitOptions.None)
	oPropertyName = splitParts(0)
	oPropertyValue = splitParts(1)
	oSamePropFileList = GetAllSameProps(oFullName, oPropertyName, oPropertyValue)
	PropsQuestion(oSamePropFileList, oPropertyName, oPropertyValue)
End If
Dim clipboardText As String = String.Join(System.Environment.NewLine, propList.ToArray())
My.Computer.Clipboard.SetText(clipboardText)
End Sub

Function PropsQuestion(SamePropFileList As ArrayList, PropertyName As String, PropertyValue As String)
	QuestionOpen = InputListBox("Select: ", SamePropFileList, SamePropFileList(0), PropertyName & " : " & PropertyValue, "Same properties")
	If QuestionOpen.contains(".ipt") Or QuestionOpen.contains(".iam") Then
		Try : oDoc = ThisApplication.Documents.Open(QuestionOpen, True) : Catch : End Try
	ElseIf QuestionOpen.contains("Open all") Then
		For Each oFile In SamePropFileList
			Try : oDoc = ThisApplication.Documents.Open(oFile, True) : Catch : End Try
		Next
	End If
End Function
Function GetAllSameProps(oFileSearch As String, ParameterName As String, ParameterValue As String)
	'List= GetAllSameProps("", "") : 
	Dim myArrayList As New ArrayList
	myArrayList.Add("Close dialog")
	For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
		Dim sNamePart As String = oRefDoc.FullFileName
		If ParameterName = "Part Number" Or ParameterName = "Description" Or ParameterName = "Stock Number" Then
			Setti = "Design Tracking Properties"
		ElseIf ParameterName = "Category" Then
			Setti = "Inventor Document Summary Information"
		Else
			Setti = "Inventor User Defined Properties"
		End If
		Try
			SelectResult = oRefDoc.PropertySets(Setti)(ParameterName).Value
		Catch
			SelectResult = "<not found>"

		End Try

		If ParameterValue = "<empty>" Then
			VertailuArvo = ""
		Else
			VertailuArvo = ParameterValue
		End If

		Try
			If SelectResult = VertailuArvo Then
				myArrayList.Add(sNamePart)
			End If
		Catch
			Logger.Debug("Type mismatch :" & SelectResult & " vs " & VertailuArvo)
		End Try
	Next
	myArrayList.Add("Open all")
	Return myArrayList
End Function

Function GetiProp(compName As String, propSet As String, propName As String) As String
	Dim value As String = ""
	Try
		value = iProperties.Value(compName, propSet, propName)
		If String.IsNullOrEmpty(value) Then
			Return "<empty>" ' Indicate if the property exists but is empty
		Else
			Return value
		End If
	Catch
		Return "<not found>" ' Indicate if the property doesn't exist
	End Try
End Function

' --- Helper Function to Format Parameter Output ---
Function FormatParameter(oParam As Parameter) As String
	If oParam.Units = "mm" Then
		arvo = Round(oParam.Value * 10, 2)
	Else If oParam.Units = "deg" Then
	arvo = Round(oParam.Value * 180 / PI)
	Else
		arvo = oParam.Value
	End If
	Dim paramInfo As String = oParam.Name & " = " & CStr(arvo) ' Start with name and value
	If Not String.IsNullOrEmpty(oParam.Units) AndAlso oParam.Units <> "ul" Then ' Add units if not unitless ("ul")
		paramInfo &= " '[" & oParam.Units & "]"
	End If
	' Add Comment if it exists
	If Not String.IsNullOrEmpty(oParam.Comment) Then
		paramInfo &= " '" & oParam.Comment & "'" ' Add comment in single quotes
	End If
	paramInfo &= " '# exp: (" & oParam.Expression & ")" ' Add expression
	Return paramInfo
End Function