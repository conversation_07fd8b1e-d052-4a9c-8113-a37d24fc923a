Sub main() 'ParametrinHaku
StartTime = Now
SharedVariable("PreventPartAccCalc") = 1 'esto yksinkertaisen mallin laskennalle 

Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments
Dim oRefDoc As Document
'ShowAllParams("", "d", oRefDocs, True)

GetParamsTxt(RidgeTubeA_txt & ";" & WindBracesA_txt & ";" & WallBracesA_txt & ";" & PurlinsA_txt)
SendParamsPart()
e_k_brace_up_code = GetParameterByNameOfRefDocs("e_k_brace_up_code", oRefDocs)
e_k_buckling_code = GetParameterByNameOfRefDocs("e_k_buckling_code", oRefDocs)
e_k_purlin_code = GetParameterByNameOfRefDocs("e_k_purlin_code", oRefDocs)
AllParamsDict = ParamsDict("", "d", oRefDocs)
SharedVariable("AllParamsDict") = AllParamsDict


GetParamsTxt(PurlinsA_txt) 'lähinnä kattojen määrä


If e_roof_pcs = 0 Then e_roof_pcs = ParaValue("e_roof_pcs", AllParamsDict, 0)


'ParaValue("e_ridge_ang", AllParamsDict)
YläPaarre = ParaValue("e_k1_yp_G_B", AllParamsDict)

RefNamebyOccName = RefDocVsOcc()
SharedVariable("RefNamebyOccName") = RefNamebyOccName


e_hall_tot_width = ParaValue("e_hall_tot_width", AllParamsDict, 0)




If e_roof_pcs = 0 Then 'obs ei aliosilla lasketaan konffan siirtodatasta mutta joskus työkierto 'väärä'
	e_roof_pcs = 1' ParaValue("e_roof_pcs", AllParamsDict) ei taulussa
End If

r1_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p1k"))
Logger.Debug("r1_k1_purlin_pcs:" & r1_k1_purlin_pcs)
Try
	r1_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p1k")) 'toimiiko jos yksi katto?!
Catch
	r1_k2_purlin_pcs = 0
End Try
Logger.Debug("r1_k2_purlin_pcs:" & r1_k2_purlin_pcs)

If e_roof_pcs>1 Then
	Try
		r2_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p2k"))
		Logger.Debug("r2_k1_purlin_pcs:" & r2_k1_purlin_pcs)
	Catch
		r2_k1_purlin_pcs = 0
	End Try
	Try
		r2_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p2k"))
	Catch
		r2_k2_purlin_pcs = 0
	End Try
	Logger.Debug("r2_k2_purlin_pcs:" & r2_k2_purlin_pcs)
Else
	r2_k1_purlin_pcs = 0
	r2_k2_purlin_pcs = 0
End If

If e_roof_pcs>2 Then
	r3_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p3k"))
	Logger.Debug("r3_k1_purlin_pcs:" & r3_k1_purlin_pcs)
	Try
		r3_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p3k"))
		Logger.Debug("r3_k2_purlin_pcs:" & r3_k2_purlin_pcs)
	Catch
		r3_k2_purlin_pcs = 0
	End Try
Else
	r3_k1_purlin_pcs = 0
	r3_k2_purlin_pcs = 0
End If

If e_roof_pcs>3 Then
	r4_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p4k"))
	Logger.Debug("r4_k1_purlin_pcs:" & r4_k1_purlin_pcs)
	Try
		r4_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p4k"))
		Logger.Debug("r4_k1_purlin_pcs:" & r4_k1_purlin_pcs)
	Catch
		r4_k2_purlin_pcs = 0
	End Try
Else
	r4_k1_purlin_pcs = 0
	r4_k2_purlin_pcs = 0
End If

If e_roof_pcs>4 Then
	r5_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p5k"))
	Logger.Debug("r5_k1_purlin_pcs:" & r5_k1_purlin_pcs)
	Try
		r5_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p5k"))
		Logger.Debug("r5_k1_purlin_pcs:" & r5_k1_purlin_pcs)
	Catch
		r5_k2_purlin_pcs = 0
	End Try
Else
	r5_k1_purlin_pcs = 0
	r5_k2_purlin_pcs = 0
End If

If e_roof_pcs>5 Then
	r6_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p6k"))
	Logger.Debug("r6_k1_purlin_pcs:" & r6_k1_purlin_pcs)
	Try
		r6_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p6k"))
		Logger.Debug("r6_k1_purlin_pcs:" & r6_k1_purlin_pcs)
	Catch
		r6_k2_purlin_pcs = 0
	End Try
Else
	r6_k1_purlin_pcs = 0
	r6_k2_purlin_pcs = 0
End If

If e_roof_pcs>6 Then
	r7_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p7k"))
	Logger.Debug("r7_k1_purlin_pcs:" & r7_k1_purlin_pcs)
	Try
		r7_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p7k"))
		Logger.Debug("r7_k1_purlin_pcs:" & r7_k1_purlin_pcs)
	Catch
		r7_k2_purlin_pcs = 0
	End Try
Else
	r7_k1_purlin_pcs = 0
	r7_k2_purlin_pcs = 0
End If

If e_roof_pcs>7 Then
	r8_k1_purlin_pcs = AllParamsDict("e_k1_purlin_pcs#" & RefNamebyOccName("p8k"))
	Logger.Debug("r8_k1_purlin_pcs:" & r8_k1_purlin_pcs)
	Try
		r8_k2_purlin_pcs = AllParamsDict("e_k2_purlin_pcs#" & RefNamebyOccName("p8k"))
		Logger.Debug("r8_k1_purlin_pcs:" & r8_k1_purlin_pcs)
	Catch
		r8_k2_purlin_pcs = 0
	End Try
Else
	r8_k1_purlin_pcs = 0
	r8_k2_purlin_pcs = 0
End If

r1_k1_brace_pcs = AllParamsDict("e_k1_brace_pcs#" & RefNamebyOccName("p1k"))
Logger.Debug("r1_k1_brace_pcs:" & r1_k1_brace_pcs)
Try
	r1_k2_brace_pcs = AllParamsDict("e_k2_brace_pcs#" & RefNamebyOccName("p1k")) 'toimiiko jos yksi katto?!
Catch
	r1_k2_brace_pcs = 0
End Try
Logger.Debug("r1_k2_brace_pcs:" & r1_k2_brace_pcs)
If e_roof_pcs>1 Then
	Try
		r2_k1_brace_pcs = AllParamsDict("e_k1_brace_pcs#" & RefNamebyOccName("p2k"))
		Logger.Debug("r2_k1_brace_pcs:" & r2_k1_brace_pcs)
	Catch
		r2_k1_brace_pcs = 0
	End Try
	Try
		r2_k2_brace_pcs = AllParamsDict("e_k2_brace_pcs#" & RefNamebyOccName("p2k"))
	Catch
		r2_k2_brace_pcs = 0
	End Try
	Logger.Debug("r2_k2_brace_pcs:" & r2_k2_brace_pcs)
End If
If e_roof_pcs>2 Then
	r3_k1_brace_pcs = AllParamsDict("e_k1_brace_pcs#" & RefNamebyOccName("p3k"))
	Logger.Debug("r3_k1_brace_pcs:" & r3_k1_brace_pcs)
	Try
		r3_k2_brace_pcs = AllParamsDict("e_k2_brace_pcs#" & RefNamebyOccName("p3k")) 'toimiiko jos yksi katto?!
	Catch
		r3_k2_brace_pcs = 0
	End Try
	Logger.Debug("r3_k2_brace_pcs:" & r3_k2_brace_pcs) 'obs lisää 8 slottiin
End If

e_ridge_width = ParaValue("e_ridge_width", AllParamsDict, 0)
Logger.Debug("e_ridge_width:" & e_ridge_width)

e_roof_1_fold_ang = ParaValue("e_roof_1_fold_ang", AllParamsDict, 0)
e_roof_2_fold_ang = ParaValue("e_roof_2_fold_ang", AllParamsDict, 0)
e_roof_3_fold_ang = ParaValue("e_roof_3_fold_ang", AllParamsDict, 0)
e_roof_4_fold_ang = ParaValue("e_roof_4_fold_ang", AllParamsDict, 0)
e_roof_5_fold_ang = ParaValue("e_roof_5_fold_ang", AllParamsDict, 0)
e_roof_6_fold_ang = ParaValue("e_roof_6_fold_ang", AllParamsDict, 0)
e_roof_7_fold_ang = ParaValue("e_roof_7_fold_ang", AllParamsDict, 0)
e_roof_8_fold_ang = ParaValue("e_roof_8_fold_ang", AllParamsDict, 0)

e_wall_buckling = ParaValue("e_wall_buckling", AllParamsDict, 0)

logger.debug("Getting sub part parameters : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function SendParamsPart()
	StartTime = Now
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim asmParams As Parameters = oAsmDoc.ComponentDefinition.Parameters
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim automationManager As Object = addIn.Automation
	' Define allowed parameter names (only these will be synced)
	Dim allowedParams As New List(Of String) From {"ridge_tube_code", "ridge_frame_code", "e_wind_brace_wa_code_end", "e_wind_brace_code_st", "e_wind_brace_code_end_r", "e_wind_brace_wa_code_st", "e_wind_brace_code_end", "e_wab_brace_code_st", "e_wab_brace_code_end" }
	' Define allowed part names (only these parts will be processed)
	Dim allowedPartNames As New List(Of String) From {"_s", "_h", "_p1", "_p2" } ' Add part name keywords to include

	For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
		' Check if the document is a part
		If TypeOf oRefDoc Is PartDocument Then
			Dim partDoc As PartDocument = oRefDoc
			Dim partParams As Parameters = partDoc.ComponentDefinition.Parameters

			' Get part name
			Dim partName As String = System.IO.Path.GetFileNameWithoutExtension(partDoc.FullDocumentName)

			' Skip parts that do NOT match the allowed list
			If Not allowedPartNames.Any(Function(x) partName.Contains(x)) Then Continue For

			updatePart = True 'False
			For Each paramName As String In allowedParams
				' Check if the parameter exists in both assembly and part
				Dim asmParam As Parameter = Nothing
				Dim partParam As Parameter = Nothing

				Try
					asmParam = asmParams.Item(paramName)
					partParam = partParams.Item(paramName)
				Catch
					' Skip if the parameter does not exist in either document
					Continue For
				End Try

				' If values are different, update the part parameter
				If asmParam.Value <> partParam.Value And Len(asmParam.Value) >1 Then
					updatePart = True
					Logger.Debug(partName & " changing parameter: " & paramName & " to " & asmParam.Value)
					partParam.Value = asmParam.Value
				End If

				If updatePart Then
					Try
						automationManager.RunRule(partDoc, "FinalizeEnd")
					Catch
						Logger.Debug("Error in FinalizeEnd " & partDoc.DisplayName)
					End Try
				End If
			Next

		End If
	Next
	Logger.Debug("SendParamsPart : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Function

Function RefDocVsOcc(Optional oFilter As String = "")
	Dim oDict As New Dictionary(Of String, String)
	For Each oOcc In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AllLeafOccurrences
		Try
			oDefOccu = oOcc.Definition
		Catch
			Continue For
		End Try

		If Not TypeOf oDefOccu Is VirtualComponentDefinition And Not TypeOf oDefOccu Is WeldsComponentDefinition Then
			If Left(oOcc.name, oFilter.Length) = oFilter
				If Not oDict.ContainsKey(oOcc.name) Then
					oDict.Add(oOcc.name, oOcc.ReferencedFileDescriptor.displayName)
				End If
			End If
		End If 'virtuaaliosat pois
	Next
	Return oDict
End Function

Function ParaValue(oParaName, oParaDict, Optional oIns = 0)
	Dim myArrayList As New ArrayList
	Dim duplicateList As New ArrayList
	Dim ParaKey() As String

	For Each oEntry As KeyValuePair(Of String, Object) In oParaDict
		ParaKey = oEntry.Key.Split(New Char() {"#"c })
		ParaKey0 = ParaKey(0)
		If ParaKey0.Contains(oParaName) Then
			'If Not myArrayList.Contains(oEntry.Value) Then
			myArrayList.Add(oEntry.Value)
			duplicateList.Add(ParaKey(1))
			'End If
		End If
		'Logger.Debug(oEntry.Value)
	Next

	oCount = myArrayList.Count

	If oCount = 1 Then
		arvo = myArrayList(0)
	Else
		Try
			arvo = myArrayList(oIns)
		Catch
			arvo = 0 '"N/A"
		End Try
	End If

	Return arvo
End Function



Function ParamsDict(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator)
	Dim oADoc As AssemblyDocument = TryCast(ThisDoc.Document, Inventor.AssemblyDocument)
	Dim oOccs As ComponentOccurrences = oADoc.ComponentDefinition.Occurrences
	Dim oDict As New Dictionary(Of String, Object)

	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			ItemName = Item.Name
			If ItemName.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				ElseIf Item.Units = "deg" Then
					Arvo = Item.Value * 180 / PI
				Else
					Arvo = Item.Value
				End If
				Try
					oDict.Add(Item.Name & "#" & oRefDoc.displayname, Arvo)
				Catch
				End Try
			End If
		Next
	Next

	For Each Item In ThisApplication.ActiveDocument.ComponentDefinition.Parameters.UserParameters 'päätason parametrit mukaan -onhan päivittynyt tässä vaiheessa
		ItemName = Item.Name
		If ItemName.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
			If Item.Units = "mm" Then 'yksiköille korjaus cm/rad
				Arvo = Item.Value * 10
			ElseIf Item.Units = "deg" Then
				Arvo = Item.Value * 180 / PI
			Else
				Arvo = Item.Value
			End If

			Try
				oDict.Add(Item.Name & "#" & "000", Arvo)
			Catch
			End Try

		End If
	Next

	Return oDict
End Function


Function ShowAllParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional paraTaulu As Boolean = False)
	For Each oRefDoc In oRefDocs
		Logger.Debug("###" & oRefDoc.FullFileName)
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters    
		Pituus = Len(oFilter)

		For Each Item In UserParams
			ItemName = Item.Name
			If ItemName.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If paraTaulu Then
					Logger.Debug("ParaTaulu.Add(""" & Item.Name & """, " & Item.Expression & ")")
				Else
					Logger.Debug(Item.Name & " = " & Item.Value & " (expression : " & Item.Expression & ")")
				End If
				teksti += Item.Name & ":" & Item.Expression & vbLf
			End If
		Next
	Next
End Function


Function GetParamsTxt(textParameter As String)
	Dim pairs() As String
	pairs = Split(textParameter, ";")
	Dim paramName As String: Dim paramValue As String

	For Each pair In pairs
		' Split the pair into parameter name and value using the equals sign
		Dim parts() As String
		parts = Split(pair, "=")
		If UBound(parts) = 1 Then  ' Ensure we got two parts (name and value)
			paramName = Trim(parts(0)) ' Remove leading/trailing spaces
			paramValue = Trim(parts(1)) ' Remove leading/trailing spaces
			' Update assembly parameters based on the extracted name
		End If
		Try
			Parameter(paramName) = paramValue
			Logger.Debug("Chaging parameter called " & paramName & " to " & paramValue)
		Catch
			'Logger.Debug("No parameter called " & paramName)
		End Try
	Next
End Function

Function GetParameterByNameOfRefDocs(oFilter As String, oRefDocs As DocumentsEnumerator, Optional copyToClipboard As Boolean = False) As String
	Dim clipboardText As String = ""
	Dim duplicateParams As New Dictionary(Of String, List(Of String))
	Dim bestValue As String = ""
	Dim bestNumericValue As Double = Double.NaN

	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)

		For Each Item In UserParams
			ItemName = Item.Name
			If ItemName = oFilter Then
				' Check if parameter exists in dictionary
				If Not duplicateParams.ContainsKey(ItemName) Then
					duplicateParams(ItemName) = New List(Of String)
				End If
				duplicateParams(ItemName).Add(oRefDoc.FullFileName & ":" & Item.value)
			End If
		Next
	Next
	For Each param In duplicateParams
		If param.Value.Count > 1 Then
			Dim validValues As New List(Of String)
			Dim numericValues As New List(Of Double)
			clipboardText += param.Key & " has multiple entries:" & vbLf

			For Each entry In param.Value
				Dim expression As String = entry.Split(":"c)(2)
				clipboardText += "  - " & entry & vbLf
				If Not String.IsNullOrWhiteSpace(expression) AndAlso expression.Length >= 3 Then
					validValues.Add(expression)
					Dim numValue As Double
					If Double.TryParse(expression, numValue) Then
						' Convert cm to mm or rad to deg based on parameter name
						If oFilter.Contains("_angle_") OrElse oFilter.EndsWith("_a") Then
							numValue = numValue * (180 / Math.PI)  ' rad to deg
						Else
							numValue = numValue * 10  ' cm to mm
						End If
						numericValues.Add(numValue)
					End If
				End If
			Next
			clipboardText += vbLf
			If numericValues.Count > 0 Then
				bestNumericValue = numericValues.Max()
				bestValue = bestNumericValue.ToString()
			ElseIf validValues.Count > 0 Then
				bestValue = validValues.OrderByDescending(Function(x) x.Length).First()
			End If
		ElseIf param.Value.Count = 1 Then
			Dim expression As String = param.Value(0).Split(":"c)(2)
			Dim numValue As Double
			If Double.TryParse(expression, numValue) Then
				' Convert cm to mm or rad to deg based on parameter name
				If oFilter.Contains("_angle_") OrElse oFilter.EndsWith("_a") Then
					numValue = numValue * (180 / Math.PI)  ' rad to deg
				Else
					numValue = numValue * 10  ' cm to mm
				End If
				bestNumericValue = numValue
				bestValue = numValue.ToString()
			Else
				bestValue = expression
			End If
			clipboardText += param.Key & " single entry:" & vbLf
			clipboardText += "  - " & param.Value(0) & vbLf & vbLf
		End If
	Next
	If copyToClipboard Then
		Try
			My.Computer.Clipboard.SetText(clipboardText)
		Catch
			Logger.Debug("Failed to copy to clipboard")
		End Try
	End If
	Return bestValue
End Function