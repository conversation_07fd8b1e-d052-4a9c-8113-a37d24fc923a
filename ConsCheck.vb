Imports System.Collections
Imports System
Sub Main() 'cui_commands
oWpFilters = {"windbrace", "wallbrace", "gableendbrace" } 'purlin -> tuulisideorsi
ConsList = GetConsInfo(oWpFilters)

'[cons -puoli
ConsCollection = AnalyzeConsDict(ConsList, "result")
ResultFR = AnalyzeConsDict(ConsList, "")
UniqueFramesList = UniqueFrame(ResultFR)
MultiValue.List("UniqueFrames") = UniqueFramesList
']

'[pointti -puoli
PointtiHash = PointHash({"wind_brace_", "joint_brace" }, "Mir") 'vain peilauksen data / testaa kaikki pisteillä
JointtiHash = JointHash()
PointCollection = ComparePointHash(JointtiHash, PointtiHash)
']

'f1_s = PrintResult(ConsCollection, "f1/s")
'f6_s = PrintResult(ConsCollection, "f6/s")
'f1_s_mir = PrintResult(PointCollection, "f1/s")
'f6_s_mir = PrintResult(PointCollection, "f6/s")

CompValueCons_Result_FRCui = PrintResult(ConsCollection, CompValueCons_FRCui)
CompValuePoint_Result_FRCui = PrintResult(PointCollection, CompValuePoint_FRCui) 'mirror puoli

Dim result As String = FindSimilarKeys(ConsCollection, PointCollection)
SimilarKeyTxt = result

' Output the result text
PlainModulesCons = PlainResult(ConsCollection)
MultiValue.List("PlainCompsCons_Result_FRCui") = PlainModulesCons
PlainModulesPoints = PlainResult(PointCollection)
MultiValue.List("PlainCompsPoint_Result_FRCui") = PlainModulesPoints

MessageBox.Show("Keys with similar values:" & vbCrLf & result)
' resultti, pitää laskea siis kehät jotka uniikkeja kuinka peilaus -voi olla 
End Sub

Function PlainResult(inputList)
	Dim keyList As New List(Of String)
	For Each tuple In inputList
		Dim key As String = Tuple.Item1
		keyList.Add(key)
	Next

	Dim numberOfObjects As Integer = e_roof_pcs
	Dim requiredObjects As New List(Of String)
	' Add pXk (from p1k to pNk) based on the number of objects
	For i = 1 To numberOfObjects
		requiredObjects.Add("p" & i & "k")
	Next

	' Add "s" as it's also required
	requiredObjects.Add("s")

	' Create a list of expected f values (f1 to f200)
	Dim fValues As New List(Of String)
	For i = 1 To totalFramePcs
		fValues.Add("f" & i)
	Next

	' Now, we will analyze and find missing p1k, p2k, etc. for each f-value
	Dim missingItems As New List(Of String)

	' Loop through the fValues list to check for missing objects (p1k, p2k, p3k, etc. and s)
	For Each fValue As String In fValues
		' Dictionary to track presence of each object
		Dim foundObjects As New Dictionary(Of String, Boolean)
		' Initialize all required objects to False (meaning missing by default)
		For Each obj As String In requiredObjects
			foundObjects(obj) = False
		Next

		For Each entry As String In keyList
			For Each obj As String In requiredObjects
				If entry = fValue & "/" & obj Then
					foundObjects(obj) = True
				End If
			Next
		Next

		' Now check if any object is still False (meaning missing)
		For Each obj As String In requiredObjects
			If Not foundObjects(obj) Then
				missingItems.Add(fValue & "/" & obj)
			End If
		Next
	Next
	' Output the missing items
	If missingItems.Count = 0 Then
		Logger.Debug("No plain modules found!")
	Else
		'    For Each item As String In missingItems
		'        MessageBox.Show(item)
		'    Next
	End If
	Return missingItems
End Function

Function FindSimilarKeys(collection1 As List(Of Tuple(Of String, List(Of String))), collection2 As List(Of Tuple(Of String, List(Of String)))) As String
	Dim similarKeys As New List(Of String)
	' Compare each tuple from collection1 with each tuple from collection2
	For Each tuple1 In collection1
		For Each tuple2 In collection2
			' Compare the values (Item2) of both tuples
			If CompareValues(tuple1.Item2, tuple2.Item2) Then
				similarKeys.Add(tuple1.Item1 & ", mir : " & tuple2.Item1)
			End If
		Next
	Next

	' Return the result as a formatted string
	If similarKeys.Count > 0 Then
		Return String.Join(vbCrLf, similarKeys)
	Else
		Return "No similar keys found."
	End If
End Function
' Function to compare two lists (ignoring duplicates and order)
Function CompareValues(list1 As List(Of String), list2 As List(Of String)) As Boolean
	' Convert both lists to sets to remove duplicates and ignore order
	Dim set1 As HashSet(Of String) = New HashSet(Of String)(list1)
	Dim set2 As HashSet(Of String) = New HashSet(Of String)(list2)
	' Compare the sets
	Return set1.SetEquals(set2)
End Function
Function Compare2(data, oHaku)
	For Each item In data
		oKey = item.Item1
		If oKey.contains(oHaku) Then
			combinedString += item.Item1
			For Each oItem2 In item.Item2
				combinedString += "|" & oItem2
			Next
			combinedString += vbLf 'rivinvaihto
		End If
	Next
	Logger.Debug(combinedString)
	My.Computer.Clipboard.SetText(combinedString)
	Return combinedString
End Function


Function PrintResult(data, oHaku)
	For Each item In data
		oKey = item.Item1
		If oKey.contains(oHaku) Then
			combinedString += item.Item1
			For Each oItem2 In item.Item2
				combinedString += "|" & oItem2
			Next
			combinedString += vbLf 'rivinvaihto
		End If
	Next
	Logger.Debug(combinedString)
	Try : My.Computer.Clipboard.SetText(combinedString) : Catch : Logger.Debug("Error nothing in reselt list:") : End Try
	Return combinedString
End Function


Function UniqueFrame(oResult)
	Dim myArrayList As New ArrayList

	For Each oVal In oResult
		KeySplit = oVal.Split(New Char() {"/"c })
		If Not myArrayList.Contains(KeySplit(0)) Then
			myArrayList.Add(KeySplit(0))
		End If
	Next
	myArrayList.sort
	Logger.Debug("Total frames:" & myArrayList.Count)
	Return myArrayList
End Function
Function AnalyzeConsDict(oDict As Dictionary(Of String, Object), oResultType As String)


	Dim arrayList As New ArrayList()
	For Each oVal In oDict.Values
		Cons1 = oVal(1).Split(New Char() {"/"c })
		Cons2 = oVal(2).Split(New Char() {"/"c })
		'Logger.Debug(oVal(1) & " : " & oVal(2))
		Dim Kentta1 As String = Cons1(0) & "/" & Cons1(1)
		Dim Kentta2 As String = Cons2(0) & "/" & Cons2(1)
		Dim Arvo1 As String = Cons1(2)
		Dim Arvo2 As String = Cons2(2)
		arrayList.Add(Tuple.Create(Kentta1, Arvo1))
		arrayList.Add(Tuple.Create(Kentta2, Arvo2))

		If Cons1(0) = "f1" Or Cons1(0) = "f" & totalFramePcs Then
			Arvo1 = "jotain" 'tarkkenus voihan olla erilainen
			arrayList.Add(Tuple.Create(Kentta1, Arvo1))
		End If

	Next

	' Step 2: Group items by the first column
	Dim groupedData As New Dictionary(Of String, List(Of String))()
	' Populate the dictionary
	For Each item As Tuple(Of String, String) In arrayList
		Dim key As String = item.Item1
		Dim value As String = item.Item2

		If Not groupedData.ContainsKey(key) Then
			groupedData(key) = New List(Of String)()
		End If
		' Add the value to the list associated with the key
		groupedData(key).Add(value)
	Next
	' Step 3: Display the grouped result
	Dim resultList As New List(Of Tuple(Of String, List(Of String)))()

	For Each kvp As KeyValuePair(Of String, List(Of String)) In groupedData
		Dim key As String = kvp.Key
		Dim values As List(Of String) = kvp.Value
		resultList.Add(Tuple.Create(key, values))
		'MessageBox.Show("Key: " & key & ", Values: " & String.Join(", ", values))
	Next

	' Step 4: Check differences between groups
	Dim uniqueKeys As New List(Of String)()
	Dim duplicateGroups As New List(Of String)()

	For i As Integer = 0 To resultList.Count - 1
		Dim firstGroup = resultList(i)
		Dim isUnique = True

		For j As Integer = i + 1 To resultList.Count - 1
			Dim secondGroup = resultList(j)

			If firstGroup.Item2.SequenceEqual(secondGroup.Item2) Then
				duplicateGroups.Add(String.Format("{0} and {1} are same", firstGroup.Item1, secondGroup.Item1))
				isUnique = False
			End If
		Next

		If isUnique Then
			uniqueKeys.Add(firstGroup.Item1)
		End If
	Next

	If oResultType = "result" Then
		resultList.Sort
		Return resultList
	Else
		uniqueKeys.Sort
		Return uniqueKeys
	End If
End Function
Function GetConsInfo(Filtteri() As String)
	Dim oDictConsName As New Dictionary(Of String, Object)
	Dim oDict As New Dictionary(Of String, Object) : laskuri = 0
	Dim oDictoOcc As New Dictionary(Of String, Object)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences

		oFullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
		oCategory = iProperties.Value(oOccurrence.Name, "Summary", "category")

		For Each oFilter In Filtteri
			If oCategory = oFilter Then
				laskuri += 1
				For Each oConstraint In oOccurrence.Constraints
					oConstName = oConstraint.name
					oConstNameCal = oConstName.replace("_point", "").replace("_dir", "").replace("_ang", "").replace("_mir", "")
					oData = oConstNameCal.Split(New Char() {"#"c })

					Try
						oDictoOcc.Add(laskuri, oData)
					Catch ex As Exception
						oExMessage = ex.Message
					End Try

				Next 'cons
			End If
		Next 'filtteri
	Next

	Return oDictoOcc
End Function
Function ComparePointHash(JointHash As Hashtable, PointHash As Hashtable, Optional Resultti As String = "")
	Dim threshold As Double = 5.0
	Laskuri0 = 0 : Laskuri1 = 0
	Dim arrayList As New ArrayList()

	For Each key1 In JointHash.Keys
		wp1 = JointHash(key1)
		' Get the coordinates of the first work point
		Dim coord1 As Point = wp1.Point
		' Inner loop to compare with every other work point
		For Each key2 In PointHash.Keys
			wp2 = PointHash(key2)
			' Get the coordinates of the second work point
			Dim coord2 As Point = wp2.Point
			' Calculate the distance between the two work points
			distance = coord1.DistanceTo(coord2)
			' Check if the distance is within the threshold
			If distance <= threshold Then
				ResulttiTest += key1 & " vs " & key2 & " dist: " & distance
				Logger.Debug("WorkPoint 1: " & key1 & vbLf & " and WorkPoint " & key2 & vbLf & "Distance: " & distance)
				Laskuri0 += 1
				If key1.contains(": Center Point") Then
					arvo = key1.Replace(": Center Point", "")
					Cons1 = arvo.Split(New Char() {"/"c })
				Else
					arvo = key2.Replace(": Center Point", "")
					Cons1 = arvo.Split(New Char() {"/"c })
				End If
				Dim Kentta1 As String = Cons1(0) & "/" & Cons1(1)
				Dim Arvo1 As String = Cons1(2)
				arrayList.Add(Tuple.Create(Kentta1, Arvo1))
			Else
				'Logger.Debug("WorkPoint " & key1 & " and WorkPoint " & key2 & " are not near each other. Distance: " & distance)
				Laskuri1 += 1
			End If
		Next
	Next

	Dim groupedData As New Dictionary(Of String, List(Of String))()
	For Each item As Tuple(Of String, String) In arrayList
		Dim key As String = item.Item1
		Dim value As String = item.Item2

		If Not groupedData.ContainsKey(key) Then
			groupedData(key) = New List(Of String)()
		End If
		' Add the value to the list associated with the key
		groupedData(key).Add(value)
	Next

	Dim resultList As New List(Of Tuple(Of String, List(Of String)))()

	For Each kvp As KeyValuePair(Of String, List(Of String)) In groupedData
		Dim key As String = kvp.Key.Replace("Mir", "")
		Dim values As List(Of String) = kvp.Value
		If key.Contains("f1") Or key.Contains("f" & totalFramePcs) Then

			values.Add("Jotain") 'obs voi olla jotain muuta
		End If
		resultList.Add(Tuple.Create(key, values))
	Next

	If Resultti = "text" Then
		Return ResulttiTest
	Else
		resultList.Sort
		Return resultList
	End If
End Function

Function PointHash(sWPFilter As String(), oOccFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument
	oAsmDoc = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition
	oAsmDef = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'Debug.Print thisOcc.Name
		'skip suppressed components
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint

				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					For Each oFilter As String In sWPFilter
						If InStr(1, currentWP.Name, oFilter) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)
							If Debuggaus Then
								'Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
								oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
							End If
							oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, oAsmPoint)
						End If
					Next
				Next
			End If
			'sub IAM: loop through all suboccurences päätaso
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count
					thisSubOcc = thisOcc.SubOccurrences(j)

					If Not thisSubOcc.Suppressed And thisSubOcc.Name.Contains(oOccFilter) And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								For Each oFilter As String In sWPFilter
									If InStr(1, currentSubWP.Name, oFilter) Then
										Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
										X = oAsmPoint.point.X * 10
										Y = oAsmPoint.point.Y * 10
										Z = oAsmPoint.point.Z * 10
										If Debuggaus Then
											Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
											oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
										End If
										oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, oAsmPoint)
									End If
								Next
							Next
						End If
						'subsub IAM: loop through all subsuboccurences tokataso
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then

							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								'Logger.Debug("thisSubSubOcc.Name:" & thisSubSubOcc.Name)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											For Each oFilter As String In sWPFilter
												If InStr(1, currentSubSubWP.Name, oFilter) Then
													Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
													X = oAsmPoint.point.X * 10
													Y = oAsmPoint.point.Y * 10
													Z = oAsmPoint.point.Z * 10

													If Debuggaus Then
														Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
														oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
													End If
													oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, oAsmPoint)
												End If
											Next
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next

	Return oHashtable
End Function
Function JointHash()
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	'Dim BraceHash As Hashtable = New Hashtable : Dim CrossarmHash As Hashtable = New Hashtable
	Dim JointPartHash As Hashtable = New Hashtable : Dim FrameHash As Hashtable = New Hashtable
	Dim threshold As Double = 1.0
	oCounterBrace = 0 : oCountercrossarm = 0
	sWPFilter = {"Start", "End" }
	oDataTeksti = ""
	CounterCrossarm2 = 0 : CounterCrossarm3 = 0
	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'skip suppressed components
		If Not thisOcc.Suppressed And thisOcc.Constraints.Count = 0 Then
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					Try : oCategory = iProperties.Value(thisOcc.Name, "Summary", "category") : Catch : oCategory = "" : End Try
					If Len(oCategory) > 2 Then

						If currentWP.Name = sWPFilter(0) Or currentWP.Name = sWPFilter(1) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)
							JointPartHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterBrace, oAsmPoint)
							oCounterBrace += 1
						End If
						oDataTeksti += thisOcc.Name & "\" & currentWP.Name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
					End If
				Next
			End If 'defType
		End If 'suppressed
	Next
	Return JointPartHash
End Function