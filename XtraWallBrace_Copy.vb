Sub main() 'WallBrace
StartTime = Now

TextLine_WBCui = "e4;SDA_size=40;SDA_end2generate=1;wind_br_start_purlin=2;SDA_length=;totalpcs=35;SDA_from=f1/s/wind_brace_corner;SDA_to_mir=f1/p1k/wind_brace_k1_2;SDA_zoffset=;SDA_to=f3/p1k/wind_brace_k1_2;SDA_xoffset=;SDA_yoffset=;br_zigzag_pcs=3;SDA_from_mir=f3/s/wind_brace_corner;SDA_gentype=windbrace_auto"
TextLine = TextLine_WBCui 'mahdollinen override

ModLista = ReturnListausXtraWindBraceSingle(TextLine, StartFrame_WBCui, EndFrame_WBCui, StartPurlin_WBCui, ZigZagPcs_WBCui)

MultiValue.List("ResultList_WBCui") = ModLista


'If Generate_WBCui Then
'	For Each oVal In ModLista
'		PlaceWindBraceCompoXtra(oVal)
'	Next
'End If
Logger.Debug("Wallbrace insert : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))

End Sub

Function ReturnListausXtraWallBraceSingle(TextLine As String)
	If TextLine Is Nothing Then : Logger.Debug("txt input not valid:" & TextLine) : Exit Function : End If

	Dim oDict As New Dictionary(Of String, Object) : Dim WallBlist As New ArrayList
	laskuri = 0 : Dim TxtData() As String = TextLine.Split(";"c)

	For Each oVal In TxtData
		Try
			If laskuri>0 'occ eka
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				Try
					oArvo = ParaData(1).Split(New Char() {"/"c })
				Catch
					oArvo = oVal 'yksittäinen tietue
				End Try
				oDict.Add(ParaData(0), oArvo)
			End If
		Catch
			'oDict.Add(TxtData(0), TxtData.Count) 'aina occ nimi ilman on -merkkiä
		End Try
		laskuri += 1
	Next

	Pos_ka = (CInt(oDict("SDA_from")(0).replace("f", "")) + CInt(oDict("SDA_to")(0).replace("f", ""))) / 2


	If Pos_ka > cui_jp_range_start And Pos_ka < cui_jp_range_end Then
		WallBlist.Add(TextLine)
	End If


	oPcs = CInt(oDict("br_zigzag_pcs")(0))

	If oPcs>1 And Pos_ka > cui_jp_range_start And Pos_ka < cui_jp_range_end Then
		For i = 2 To oPcs
			RoofRivi = TxtData(0) & ";" 'täällä ei muuteta occ -koska siitä lasketaan fullname

			For Each oEntry As KeyValuePair(Of String, Object) In oDict
				RoofRivi += oEntry.Key & "="

				If oEntry.Value.length = 1 Then
					RoofRivi += oEntry.Value(0) & ";"
				ElseIf oEntry.Value.length = 3 Then
					Frame_to = oDict("SDA_to")(0)
					Frame_from = oDict("SDA_from")(0)
					oModuuli = oDict("SDA_to")(1) 'miten useampi katto vaihto 
					oSDA_to_node = oDict("SDA_to")(2)
					oNode = Left(oSDA_to_node, Len(oSDA_to_node) -1) 'miten k2 vaihto

					If i Mod 2 = 0 Then 'parillinen -> flippaus
						oFromF = Frame_to
						oToF = Frame_from
					Else
						oToF = Frame_to
						oFromF = Frame_from
					End If

					If oEntry.Key = "SDA_from"

						RoofRivi += oFromF & "/" & oModuuli & "/" & oNode & i & ";"
					ElseIf oEntry.Key = "SDA_to"
						RoofRivi += oToF & "/" & oModuuli & "/" & oNode & i + 1 & ";"
					Else
						RoofRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
					End If

				End If
			Next
			WallBlist.Add(Left(RoofRivi, Len(RoofRivi) -1))
		Next
	Else
		Frame_to = oDict("SDA_to")(0)
		Frame_from = oDict("SDA_from")(0)
	End If


	Return WallBlist
End Function


Function PlaceWindBraceCompoXtra(TxtPara As String)
	Dim PituusTaulu As Hashtable = New Hashtable : laskuriOccPcs = 1
	oOccName = ReturnTXT(TxtPara, "oOccName")
	oSDA_from = ReturnTXT(TxtPara, "SDA_from") 'oPointDataArraysis
	Dim oData() As String = oSDA_from.Split("/"c)

	oSDA_to = ReturnTXT(TxtPara, "SDA_to")
	If oSDA_to Is Nothing Then : Exit Function : End If
	Dim oDataTo() As String = oSDA_to.Split("/"c)

	oFrame = oData(0)
	oFrameTo = oDataTo(0)

	oVrt1 = CDbl(oFrame.Replace("f", ""))
	oVrt2 = CDbl(oFrameTo.Replace("f", ""))
	If oVrt2 > oVrt1 Then
		LisäysFrom = ""
		LisäysTo = "-"
	Else
		LisäysFrom = "-"
		LisäysTo = ""
	End If

	oModule = oData(1)
	oSDA_from_wp = oData(2) & LisäysFrom
	oSDA_from_wp_end2 = oData(2) & LisäysTo 'toisessa päädyssä toisin päin

	oModuleTo = oDataTo(1)
	oSDA_to_wp = oDataTo(2) & LisäysTo
	oSDA_to_wp_end2 = oDataTo(2) & LisäysFrom  'toisessa päädyssä toisin päin

	oSDA_from_mir = ReturnTXT(TxtPara, "SDA_from_mir")
	Dim oDataMir() As String = oSDA_from_mir.Split("/"c)
	oFrameMir = oDataMir(0)
	oModuleMir = oDataMir(1)
	oSDA_from_wp_mir = oDataMir(2) & LisäysTo 'obs toisin päin aina
	oSDA_from_wp_mir_end2 = oDataMir(2) & LisäysFrom

	oSDA_to_mir = ReturnTXT(TxtPara, "SDA_to_mir")
	Dim oDataToMir() As String = oSDA_to_mir.Split("/"c)
	oFrameToMir = oDataToMir(0)
	oModuleToMir = oDataToMir(1)
	oSDA_to_wp_mir = oDataToMir(2) & LisäysFrom 'obs toisin päin aina
	oSDA_to_wp_mir_end2 = oDataToMir(2) & LisäysTo 'obs toisin päin aina

	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")

	oSDA_end2generate = ReturnTXT(TxtPara, "SDA_end2generate")
	ototalpcs = ReturnTXT(TxtPara, "totalpcs")

	PointtiHash = PointCoorHash("wind_brace_") 'obs muissa 

	'[initial position calculation
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	oStartPoint = oTG.CreatePoint(0, 0, 0)
	Dim oMatrix As Matrix = oTG.CreateMatrix
	Dim oXAxis As Vector
	Dim oYAxis As Vector
	Dim oZAxis As Vector
	oXAxis = oTG.CreateVector(0, 0, 1)
	oYAxis = oTG.CreateVector(1, 0, 0)
	oZAxis = oTG.CreateVector(0, 1, 0)

	oMatrix.SetCoordinateSystem(oStartPoint, oXAxis, oYAxis, oZAxis)
	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)
	Dim Pos = ThisAssembly.Geometry.Matrix(cells(0), cells(1), cells(2), oStartPoint.X * 10,
	cells(4), cells(5), cells(6), oStartPoint.Y * 10,
	cells(8), cells(9), cells(10), oStartPoint.Z * 10,
	0, 0, 0, 1)

	Try
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	Catch
		Dim ArrayOcc() As String = oOccName.Split(New Char() {"_"c })
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(ArrayOcc(0))
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End Try

	oPointsDistance = Round(CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash))

	oDeltaZ = DeltaZ(oSDA_from, oSDA_to, PointtiHash) ': Logger.Debug("oDeltaZ:" & oDeltaZ)


	uusiName = oOccName

	If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
		UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
		SaveFilu(oFullFileName, UusiFullFileName)
		PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)
		If oDeltaZ<-1000 Then
			Components.Add(uusiName, UusiFullFileName, position := Pos)
		Else
			Components.Add(uusiName, UusiFullFileName, position := Pos)
		End If
		If oPointsDistance>500 Then
			Parameter(uusiName, "G_L") = oPointsDistance
		Else
			Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
		End If
		'CompoArguJointOcc = uusiName.ToString
	Else 'käytetään vanhaa
		If oDeltaZ<-1000 Then
			Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
		Else
			Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
		End If
	End If

	oOccName = uusiName


	oOccName_mir = oOccName & "_mir" 'aina peilaus


	Components.Add(oOccName_mir, PituusTaulu(oPointsDistance), position := Pos)


	oPointsDistance = CalcPointsDistance(oSDA_from & LisäysFrom, oSDA_to & LisäysTo, PointtiHash)
	If Abs(oPointsDistance - oSDA_length) >100 Then
		Parameter(oOccName, "G_L") = oPointsDistance
		Logger.Debug("Changing G_L to :" & oPointsDistance & " in " & oOccName)
	End If
	n = 0 ' obs tarkista assy level

	oConsName = oOccName & "#" & oSDA_from & LisäysFrom & "#" & oSDA_to & LisäysTo

	Dim CompoArguStartPoint As ComponentArgument = {oFrame, oModule }
	Dim CompoArguEndPoint As ComponentArgument = {oFrameTo, oModuleTo }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
	Dim CompoArguStartPoint_mir As ComponentArgument = {oFrameMir, oModuleMir }
	Dim CompoArguEndPoint_mir As ComponentArgument = {oFrameToMir, oModuleToMir }
	Dim CompoArguJointOcc_mir As ComponentArgument = oOccName_mir.ToString

	If oDeltaZ<-1000 Then
		sijoitusPiste = "ucs_start2: Center Point"
	Else
		sijoitusPiste = "ucs_start: Center Point"
	End If


	Try
		Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, sijoitusPiste, CompoArguStartPoint, oSDA_from_wp & ": Center Point")
		Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, oSDA_to_wp & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara & vbLf & ex.Message)
	End Try

	Try
		Constraints.AddMate(oConsName & "_point_mir", CompoArguJointOcc_mir, sijoitusPiste, CompoArguStartPoint_mir, oSDA_from_wp_mir & ": Center Point")
		Constraints.AddMate(oConsName & "_dir_mir", CompoArguJointOcc_mir, "ucs_start: Y Axis", CompoArguEndPoint_mir, oSDA_to_wp_mir & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch ex As Exception
		Logger.Debug("Error in mir :" & TxtPara & vbLf & ex.Message)
	End Try


End Function
'gen codes
Function ReturnTXT(TxtPara As String, Optional returnValue As String = "")
	If TxtPara Is Nothing Then : Logger.Debug("Error txt input not valid ReturnTXT :") : Exit Function : End If
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String: Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			oPointDataArraysis = oDatumSis(1)
			If oParameterName = "SDA_from" Then
				oSDA_from = oDatumSis(1)
			ElseIf oParameterName = "SDA_to" Then
				oSDA_to = oDatumSis(1)
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			ElseIf oParameterName = "totalpcs" Then
				ototalpcs = oDatumSis(1)
			ElseIf oParameterName = "SDA_from_mir" Then
				oSDA_from_mir = oDatumSis(1)
			ElseIf oParameterName = "SDA_to_mir" Then
				oSDA_to_mir = oDatumSis(1)
			ElseIf oParameterName = "SDA_end2generate" Then
				oSDA_end2generate = oDatumSis(1)
			ElseIf oParameterName = "e_roof_pcs" Then
				oe_roof_pcs = oDatumSis(1)
			ElseIf oParameterName = "SDA_size" Then
				oSDA_size = oDatumSis(1)
			End If
		End If
	Next

	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If ototalpcs = "" Then : ototalpcs = 0 : End If
	If oSDA_end2generate = "" Then : oSDA_end2generate = 0 : End If

	If returnValue = "SDA_from" Then
		Return oSDA_from
	ElseIf returnValue = "oOccName" Then
		Return oOccName
	ElseIf returnValue = "SDA_xoffset" Then
		Return oSDA_xoffset
	ElseIf returnValue = "SDA_yoffset" Then
		Return oSDA_yoffset
	ElseIf returnValue = "SDA_zoffset" Then
		Return oSDA_zoffset
	ElseIf returnValue = "totalpcs" Then
		Return ototalpcs
	ElseIf returnValue = "SDA_to" Then
		Return oSDA_to
	ElseIf returnValue = "SDA_from_mir" Then
		Return oSDA_from_mir
	ElseIf returnValue = "SDA_to_mir" Then
		Return oSDA_to_mir
	ElseIf returnValue = "SDA_end2generate" Then
		Return oSDA_end2generate
	ElseIf returnValue = "e_roof_pcs" Then
		Return oe_roof_pcs
	ElseIf returnValue = "SDA_size" Then
		Return oSDA_size
	End If
End Function
Function PointCoorHash(sWPFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument
	oAsmDoc = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition
	oAsmDef = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'Debug.Print thisOcc.Name
		'skip suppressed components
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					If InStr(1, currentWP.Name, sWPFilter) Then
						Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
						X = Round(oAsmPoint.point.X * 10, 1)
						Y = Round(oAsmPoint.point.Y * 10, 1)
						Z = Round(oAsmPoint.point.Z * 10, 1)
						If Debuggaus Then
							'Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
						End If
						oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
					End If
				Next
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								'check if pointname contains underscore
								If InStr(1, currentSubWP.Name, sWPFilter) Then
									Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
									X = oAsmPoint.point.X * 10
									Y = oAsmPoint.point.Y * 10
									Z = oAsmPoint.point.Z * 10
									If Debuggaus Then
										Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
										oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
									End If
									oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
								End If
							Next
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = oAsmPoint.point.X * 10
												Y = oAsmPoint.point.Y * 10
												Z = oAsmPoint.point.Z * 10

												If Debuggaus Then
													Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
													oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												End If
												oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, {X, Y, Z })
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next
	If Debuggaus Then
		CoordDataa = oDataTeksti
		My.Computer.Clipboard.SetText(oDataTeksti)
	End If
	Return oHashtable
End Function
Function CalcPointsDistance(StartPoint As String, EndPoint As String, PointtiHash As Hashtable, Optional PointSuffix As String = ": Center Point", Optional Debuggaus As Boolean = False)
	oStartPoint = PointtiHash(StartPoint & PointSuffix) ' ucs default center point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found StartPoint:" & StartPoint & " using zero point instead")
		oStartPoint = {0, 0, 0 }
	End If
	oEndPoint = PointtiHash(EndPoint & PointSuffix)
	If oEndPoint Is Nothing Then 'virheen korjaus 

		Logger.Debug("Error not found oEndPoint:" & oEndPoint & " using zero point instead")
		oEndPoint = {0, 0, 0 }
	End If
	CalcPointsDistance = ((oEndPoint(0) -oStartPoint(0)) ^ 2 + (oEndPoint(1) -oStartPoint(1)) ^ 2 + (oEndPoint(2) -oStartPoint(2)) ^ 2) ^ 0.5
	'Logger.Debug(StartPoint & " to " & EndPoint & " L:" & CalcPointsDistance)
End Function
Function DeltaZ(StartPoint As String, EndPoint As String, PointtiHash As Hashtable, Optional PointSuffix As String = ": Center Point", Optional Debuggaus As Boolean = False)
	oStartPoint = PointtiHash(StartPoint & PointSuffix) ' ucs default center point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & StartPoint & " using zero point instead")
		oStartPoint = {0, 0, 0 }
	End If
	oEndPoint = PointtiHash(EndPoint & PointSuffix)
	If oEndPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & oEndPoint & " using zero point instead")
		oEndPoint = {0, 0, 0 }
	End If
	DeltaZ = oEndPoint(2) -oStartPoint(2)
End Function
Function SaveFilu(SrcFilu As String, NewFilu As String)
	Try
		oDoc = ThisApplication.Documents.Open(SrcFilu, False)
	Catch
		Logger.Debug("Error not found base part :" & SrcFilu)
	End Try
	If oDoc Is Nothing Then
		Exit Function
	End If
	If IO.File.Exists(NewFilu) Then
		Logger.Debug("File already exists : " & NewFilu)
	Else
		Logger.Debug("New part File being created : " & NewFilu)
		Try : oDoc.SaveAs(NewFilu, True) : Catch : End Try 'cache voi aiheuttaa virheen?
	End If
End Function