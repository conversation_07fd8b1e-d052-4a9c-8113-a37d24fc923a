Imports System.Windows.Forms

Sub Main()
Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument 'ShowAllParams
Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments
Dim oRefDoc As Document

'ShowAllParams("", "d", oRefDocs, True)

Dim ParaTaulu As Hashtable = New Hashtable
ParaTaulu.Add("e_k_kat_ap", 6555 mm)
'ParaTaulu.Add("e_s_UP_G_H", 80 mm)
'ParaTaulu.Add("e_s_SP_G_H", 90 mm)
'ParaTaulu.Add("e_h_Harjakul<PERSON>", 11 deg)
'ParaTaulu.Add("e_Hallin_leveys", 25000 mm)
'ParaTaulu.Add("e_Pienin_korkeus", 7500 mm)
'ParaTaulu.Add("e_s_Räystäsputki", 89 mm)
'ParaTaulu.Add("e_s_Jalkalevyn_leveys", 230 mm)
'ParaTaulu.Add("e_s_J<PERSON>kot<PERSON>", 112 mm)
'ParaTaulu.Add("e_s_SUP_taite", 0 mm)
'ParaTaulu.Add("e_k2_Kattokulma", 4 deg)
'ParaTaulu.Add("e_k1_Kattokulma", 0 deg)
'ParaTaulu.Add("e_s_Seinäkulma", 4 deg)
'ParaTaulu.Add("e_h_HAP_pituus", 3026 mm)
'ParaTaulu.Add("e_k2_KAT2_AP", 8781 mm)
'ParaTaulu.Add("e_k1_KAT1_AP", 0.399110073421163 mm)

'ShowByFilter(ParaTaulu, oRefDocs)

'ChangeParams(ParaTaulu, oRefDocs)

'ChangeCreateParams2ActiveDocument(ParaTaulu)

ShowStructure(ThisApplication.ActiveDocument.ComponentDefinition.Occurrences, 1)

'WallJoint = Parameter("s:1", "e_s_joint_code_1") 'returnOcc
'Roof1Joint_lo = Parameter("p1k:1", "e_k_joint_code_u_1")
'Roof1Joint_up = Parameter("p1k:1", "e_k_joint_code_u_1")

'RoofJoint2_lo = Parameter("p2k:1", "e_k_joint_code_u_1")
'RoofJoint2_up = Parameter("p2k:1", "e_k_joint_code_u_1")
'RidgeJoint = Parameter("s:1", "e_s_joint_code_1")


End Sub


Function ChangeCreateParams2ActiveDocument(oParaTaulu As Hashtable)
	oMyParameter = ThisApplication.ActiveDocument.ComponentDefinition.Parameters.UserParameters
	Try
		For Each oParaName In oParaTaulu.Keys
			Try
				Parameter(oParaName) = oParaTaulu(oParaName)
			Catch
				If oParaName.toupper.contains("KULMA") Then
					Tyyppi = "deg"
				Else
					Tyyppi = "mm"
				End If
				Logger.Debug("Creating " & oParaName & " " & oParaTaulu(oParaName) & " " & Tyyppi)
				oParameter = oMyParameter.AddByExpression(oParaName, oParaTaulu(oParaName), Tyyppi)
				Parameter(oParaName) = oParaTaulu(oParaName)
			End Try
		Next
	Catch
	End Try
End Function

Function ShowByFilter(ParaTaulu As Hashtable, oRefDocs As DocumentsEnumerator)
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters            
		For Each Item In UserParams
			n = 0
			For Each oFilter In ParaTaulu.Keys

				If Item.Name = oFilter Then
					n = n + 1
					If n = 1 Then
						Logger.Debug("###" & oRefDoc.FullFileName)
					End If
					Logger.Debug(Item.Name & " = " & Item.Value & " (expression : " & Item.Expression & ")")
				End If
			Next
		Next
	Next
End Function

Function ChangeParams(ParaTaulu As Hashtable, oRefDocs As DocumentsEnumerator)
	For Each oRefDoc In oRefDocs
		'Logger.Debug("###" & oRefDoc.FullFileName)
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters            
		For Each Item In UserParams
			For Each oFilter In ParaTaulu.Keys
				If Item.Name = oFilter Then
					Logger.Debug(oRefDoc.displayname & " Changing para " & Item.Name & " = " & Item.Expression & " to " & ParaTaulu(oFilter))
					Item.Expression = ParaTaulu(oFilter)
				End If
			Next
		Next
	Next
End Function

Function ShowAllParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional paraTaulu As Boolean = False)
	For Each oRefDoc In oRefDocs
		Logger.Debug("###" & oRefDoc.FullFileName)
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters    
		Pituus = Len(oFilter)
		'ParaTaulu.Add("e_Rakennekorkeus", 1415)
		For Each Item In UserParams
			If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If paraTaulu Then
					Logger.Debug("ParaTaulu.Add(""" & Item.Name & """, " & Item.Expression & ")")
				Else
					Logger.Debug(Item.Name & " = " & Item.Value & " (expression : " & Item.Expression & ")")
				End If
				teksti += Item.Name & ":" & Item.Expression & vbLf
			End If
		Next
	Next

	My.Computer.Clipboard.SetText(teksti)
End Function

Function ShowStructure(Occurrences As ComponentOccurrences, Level As Integer)
	Dim oOcc As ComponentOccurrence

	For Each oOcc In Occurrences
		If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			Try : Pnumber = iProperties.Value(oOcc.Name, "Project", "Part Number") : Catch : Pnumber = "" : End Try
			oFullFileName = oOcc.ReferencedFileDescriptor.FullFileName : oResult = ""



			If oOcc.Name = "e_xt_1_0075-86_f1_f2" Then
				
			End If

			If oFullFileName.contains(ProjectNum) Then
				If Pnumber.contains(ProjectNum) Then
					oResult = ""
				Else
					oResult = " Obs Check part number"
				End If

			End If

			Logger.Debug(Pnumber & oResult & ":" & Level & " " & Space(Level * 10) & oOcc.Name & " [" & oFullFileName & "]")

			If oOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				ShowStructure(oOcc.SubOccurrences, Level + 1)
			End If
		End If
	Next
End Function

Private Sub processAllSubOcc(ByVal oOcc As ComponentOccurrence, oAllOccur As ObjectCollection)
	Dim oDoc As AssemblyDocument
	oDoc = ThisApplication.ActiveDocument
	Dim oSubCompOcc As ComponentOccurrence
	For Each oSubCompOcc In oOcc.SubOccurrences
		' Check if it's child occurrence (leaf node)
		If oSubCompOcc.SubOccurrences.Count = 0 Then
			'MessageBox.Show ("its a part")
			oAllOccur.Add(oSubCompOcc)
		Else
			'MessageBox.Show ("its an assembly")
			oAllOccur.Add(oSubCompOcc)
			Call processAllSubOcc(oSubCompOcc, oAllOccur)
		End If
	Next
End Sub

