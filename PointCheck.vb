Sub main()
StartTime = Now
PointtiHash = PointHash("joint_")
Logger.Debug("Frame hash " & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
JointtiHash = JointHash()

koe = CompareHash(JointtiHash, PointtiHash)

Logger.Debug("Cal time tot: " & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub
Function CompareHash(JointHash As Hashtable, PointHash As Hashtable)

Laskuri0 = 0 : Laskuri1 = 0
For Each key1 In JointHash.Keys
	wp1 = JointHash(key1)
	' Get the coordinates of the first work point
	Dim coord1 As Point = wp1.Point
	' Inner loop to compare with every other work point
	For Each key2 In PointHash.Keys
		wp2 = PointHash(key2)
		' Get the coordinates of the second work point
		Dim coord2 As Point = wp2.Point
		' Calculate the distance between the two work points
		distance = coord1.DistanceTo(coord2)
		' Check if the distance is within the threshold
		If distance <= threshold Then
		ResulttiTest += key1 & " vs " & key2 & " dist: " & distance
		'Logger.Debug("WorkPoint 1: " & key1 & vbLf & " and WorkPoint " & key2 & vbLf & "Distance: " & distance)
		Laskuri0 += 1
		Else
		Logger.Debug("WorkPoint " & key1 & " and WorkPoint " & key2 & " are not near each other. Distance: " & distance)
		Laskuri1 += 1
		End If
	Next
Next

Return ResulttiTest
End Function

Function PointHash(sWPFilter As String, Optional Debuggaus As Boolean = False)
Dim oHashtable As Hashtable = New Hashtable
Dim oAsmDoc As AssemblyDocument
oAsmDoc = ThisApplication.ActiveDocument
Dim oAsmDef As AssemblyComponentDefinition
oAsmDef = oAsmDoc.ComponentDefinition
oDataTeksti = ""

For i = 1 To oAsmDef.Occurrences.Count
	thisOcc = oAsmDef.Occurrences(i)
	'Debug.Print thisOcc.Name
	'skip suppressed components
	If Not thisOcc.Suppressed Then
	'sub IAM or IPT: loop through all WP
	If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
	Dim currentWP As WorkPoint
	For Each currentWP In thisOcc.Definition.WorkPoints
		'check if pointname contains underscore
		If InStr(1, currentWP.Name, sWPFilter) Then
		Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
		X = Round(oAsmPoint.point.X * 10, 1)
		Y = Round(oAsmPoint.point.Y * 10, 1)
		Z = Round(oAsmPoint.point.Z * 10, 1)
		If Debuggaus Then
		'Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
		oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
		End If
		oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, oAsmPoint)
		End If
	Next
	End If
	'sub IAM: loop through all suboccurences
	If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
	For j = 1 To thisOcc.SubOccurrences.Count

		thisSubOcc = thisOcc.SubOccurrences(j)
		If Not thisSubOcc.Suppressed And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
		If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
		Dim currentSubWP As WorkPoint
		For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
			'check if pointname contains underscore
			If InStr(1, currentSubWP.Name, sWPFilter) Then
			Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
			X = oAsmPoint.point.X * 10
			Y = oAsmPoint.point.Y * 10
			Z = oAsmPoint.point.Z * 10
			If Debuggaus Then
			Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
			oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
			End If
			oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, oAsmPoint)
			End If
		Next
		End If
		'subsub IAM: loop through all subsuboccurences
		If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
		For k = 1 To thisSubOcc.SubOccurrences.Count
			thisSubSubOcc = thisSubOcc.SubOccurrences(k)
			If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
			If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
			Dim currentSubSubWP As WorkPoint
			For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
				'check if pointname contains underscore
				If InStr(1, currentSubSubWP.Name, sWPFilter) Then
				Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
				X = oAsmPoint.point.X * 10
				Y = oAsmPoint.point.Y * 10
				Z = oAsmPoint.point.Z * 10

				If Debuggaus Then
				Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
				oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
				End If
				oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, oAsmPoint)
				End If
			Next
			End If
			End If
		Next
		End If
		'END subsub IAM
		End If
	Next
	End If
	'END sub IAM
	End If
Next

Return oHashtable
End Function
Function JointHash()
Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
'Dim BraceHash As Hashtable = New Hashtable : Dim CrossarmHash As Hashtable = New Hashtable
Dim JointPartHash As Hashtable = New Hashtable : Dim FrameHash As Hashtable = New Hashtable
Dim threshold As Double = 1.0
oCounterBrace = 0 : oCountercrossarm = 0


sWPFilter = {"Start", "End" }
oDataTeksti = ""

CounterCrossarm2 = 0 : CounterCrossarm3 = 0
For i = 1 To oAsmDef.Occurrences.Count
	thisOcc = oAsmDef.Occurrences(i)
	'skip suppressed components
	If Not thisOcc.Suppressed Then
	'sub IAM or IPT: loop through all WP
	If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
	Dim currentWP As WorkPoint
	For Each currentWP In thisOcc.Definition.WorkPoints
		'check if pointname contains underscore
		Try : oCategory = iProperties.Value(thisOcc.Name, "Summary", "category") : Catch : oCategory = "" : End Try
		If Len(oCategory) > 2 Then

		If currentWP.Name = sWPFilter(0) Or currentWP.Name = sWPFilter(1) Then
		Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
		X = Round(oAsmPoint.point.X * 10, 1)
		Y = Round(oAsmPoint.point.Y * 10, 1)
		Z = Round(oAsmPoint.point.Z * 10, 1)
		JointPartHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterBrace, oAsmPoint)
		oCounterBrace += 1
		End If

		oDataTeksti += thisOcc.Name & "\" & currentWP.Name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
		End If
	Next
	End If 'defType
	End If 'suppressed
Next

Return JointPartHash




End Function
