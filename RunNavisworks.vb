Imports System.Runtime.InteropServices
Sub Main()
    ' Define the file to open
    Dim fileToOpen As String = "C:\Vault_BH\Designs\Src\Navis.nwf"

    ' Check if the file exists
    If Not System.IO.File.Exists(fileToOpen) Then
        MessageBox.Show("The specified file does not exist: " & fileToO<PERSON>, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Return
    End If
    Try
        ' Attempt to get a running instance of Navisworks
        Dim navisApp As Object = Nothing
        navisApp = Marshal.GetActiveObject("Navisworks.Application")
        ' If Navisworks is running, open the file
        If navisApp IsNot Nothing Then
            navisApp.ActiveDocument.OpenFile(fileToOpen)
            MessageBox.Show("File opened in the existing Navisworks session: " & fileToOpen, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    Catch ex As COMException
        ' If no running instance is found, start a new instance of Navisworks
        Dim navisworksPath As String = "C:\Program Files\Autodesk\Navisworks Manage 2024\Roamer.exe"
        If Not System.IO.File.Exists(navisworksPath) Then
            MessageBox.Show("Navisworks executable not found: " & navisworksPath, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If
        ' Start a new Navisworks process and open the file
        Dim processInfo As New System.Diagnostics.ProcessStartInfo()
        processInfo.FileName = navisworksPath
        processInfo.Arguments = """" & fileToOpen & """"
        System.Diagnostics.Process.Start(processInfo)
        MessageBox.Show("New Navisworks session started with the file: " & fileToOpen & " (run import command from Navi's add-inns tab)", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
    Catch ex As Exception
        ' Handle other errors
        MessageBox.Show("An error occurred: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub