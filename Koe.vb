Sub Main() 'miten unique laskettaisiin
Dim oDoc As Document = ThisApplication.ActiveDocument

Dim separator As String = "__"
Dim ooSDA_UNIQUEID() As String
Dim customPropertyName As String = "QTY / Project"
Dim oAsmDoc As AssemblyDocument = oDoc
Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
Dim oOccs As ComponentOccurrences = oAsmDef.Occurrences
Dim oDict As New Dictionary(Of String, Integer)
Dim oDict2 As New Dictionary(Of String, String)

For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
	Dim sFullName As String = oRefDoc.FullFileName
	Dim sNamePart As String = IO.Path.GetFileName(oRefDoc.FullFileName)

	If sFullName.Contains("\Designs\Proj\") And Right(sFullName,5)="a.iam" Then
		Try
			oooSDA_UNIQUEID = oRefDoc.PropertySets("Inventor User Defined Properties")("SDA_UNIQUEID").Value
			ooSDA_UNIQUEID = Split(oooSDA_UNIQUEID, separator)
			oSDA_UNIQUEID=ooSDA_UNIQUEID(0)
		Catch
			Logger.Debug("No SDA_UNIQUEID data :" & oRefDoc.DisplayName)
			Continue For
		End Try
		
		Logger.Debug(sFullName & " SDA ID " & oSDA_UNIQUEID)
		
		Dim iCount As Integer = oOccs.AllReferencedOccurrences(oRefDoc).Count

		If oDict.ContainsKey(oSDA_UNIQUEID) Then
			oDict(oSDA_UNIQUEID) = oDict(oSDA_UNIQUEID) + iCount
		Else
			oDict.Add(oSDA_UNIQUEID, iCount)
		End If 
	End If
Next



'	For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
'		If Not oRefDoc.IsModifiable Then Continue For
''		Dim sNamePart As String = IO.Path.GetFileName(oRefDoc.FullFileName)
'		Dim sPartNumb As String = oRefDoc.PropertySets("Design Tracking Properties")("Part Number").Value
'		Dim iCount As Integer = oDict(sPartNumb)
'		If iCount = 0 Then Continue For
'		Dim oCustom As PropertySet = oRefDoc.PropertySets("Inventor User Defined Properties")
'		Try : oCustom(customPropertyName).Value = iCount
'		Catch : oCustom.Add(iCount, customPropertyName) : End Try
'	Next

End Sub