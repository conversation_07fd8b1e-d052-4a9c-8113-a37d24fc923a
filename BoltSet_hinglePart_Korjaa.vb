Sub main() 'BoltSetCalculation
	
Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument 'ShowAllParams
Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments : Dim oRefDoc As Document
Dim KatonAlaSaranat As New ArrayList : <PERSON><PERSON>laSaranat As New ArrayList
Dim SaranoidenBoltSet As New ArrayList : Dim oSumOfConnectionParts As Hashtable = New Hashtable


LaskeSaranat = True: LaskeLevyLiitos = True
KatonAlaSaranat = ParamsValuToArray("E=e_k_joint_code_lo_1", "d", oRefDocs, True)
KatonYlaSaranat = ParamsValuToArray("e_k_joint_code_u_1", "d", oRefDocs, True)
YlaLkm = KatonYlaSaranat.Count
KatonAlaSaranat.Add(KatonYlaSaranat(YlaLkm - 1)) 'obs miten harja suhtautuu?!? ja entä tarkka mallimukana


If LaskeSaranat Then
	Hashi1 = ItemCodeCalculator(KatonAlaSaranat, oSumOfConnectionParts) 'saranat
End If

KatonAlaliistosLevyt = ParamsValuToArray("E=e_k_joint_code_lo_2", "d", oRefDocs, True)
KatonYlemmatAlaliistosLevyt = ParamsValuToArray("e_k_joint_code_l_1", "d", oRefDocs, True)
YlaAlaLevyLkm = KatonYlemmatAlaliistosLevyt.Count
KatonAlaliistosLevyt.Add(KatonYlemmatAlaliistosLevyt(YlaAlaLevyLkm - 1)) 'obs miten harja suhtautuu?!? ja entä tarkka mallimukana



If LaskeLevyLiitos Then
	Hashi2 = ItemCodeCalculator(KatonAlaliistosLevyt, oSumOfConnectionParts)
Else
	Hashi2 = Hashi1
End If

OnkoHarja = 0

If Not Hashi2 Is Nothing Then
	For Each oVal In Hashi2.keys
'		If OnkoHarja = 0 Then
'			lkm = Hashi2(oVal) * 2 - 1
'		Else
'			lkm = Hashi2(oVal) * 2
'		End If
		'CreateByIDdb(oVal, 1, Hashi2(oVal) ) ' tuplana saranat paitsi jos harja...sama vika levyissä saa 2 kahden liitoksen
		Logger.Debug(oVal & " arvo " & Hashi2(oVal))
	Next
End If
End Sub

Function ItemCodeCalculator(oSearchList As ArrayList, myHashtable As Hashtable)
	koe = myHashtable
	Dim myArrayList As New ArrayList

	For Each oSearch As String In oSearchList
		If oSearch = "XS06" Then
			myArrayList.Add("RS65") 'adet005 M24x140
			myArrayList.Add("RA61")  'aluslevy M24 x 2
			myArrayList.Add("RA61")
			myArrayList.Add("RT60") 'mutteri M24
		ElseIf oSearch = "XS03" Then
			myArrayList.Add("RS65") 'adet002 M24x100 obs tarkista koodi
			myArrayList.Add("RA61")  'aluslevy M24 x 2
			myArrayList.Add("RA61")
			myArrayList.Add("RT60") 'mutteri M24
		ElseIf oSearch = "XS11" Then
			myArrayList.Add("RS65") 'adet008 M24x100 obs tarkista koodi samakuin 03
			myArrayList.Add("RA61")  'aluslevy M24 x 2
			myArrayList.Add("RA61")
			myArrayList.Add("RT60") 'mutteri M24
		End If

		If Left(oSearch, 2) = "LJ" Then
			Dim oSearchListaus As String() = oSearch.Split(New Char() {"-"c })
			oSize = oSearchListaus(1).Split(New Char() {"x"c })
			oThickness = oSize(1)
			If oThickness>9 And oThickness<21 Then
				For i = 1 To 2 'pulttisettien määrä per levy
					myArrayList.Add("RS61") 'adet102&103 M24x80
					myArrayList.Add("RA61")  'aluslevy M24 x 2
					myArrayList.Add("RA61")
					myArrayList.Add("RT60") 'mutteri M24
				Next
			End If
		End If
	Next

	For Each oVal In myArrayList
		If Not myHashtable.ContainsKey(oVal) Then
			myHashtable.Add(oVal, 1)
		Else
			OldPcsValue = myHashtable(oVal)
			myHashtable.Remove(oval)
			myHashtable.Add(oVal, OldPcsValue + 1)
		End If
	Next

	Return myHashtable
End Function
Function CombineHash(ht1 As Hashtable, ht2 As Hashtable)

	Dim ht3 As Hashtable = New Hashtable
	For Each key In ht1.Keys
		If ht2.ContainsKey(key) Then
			ht3.Add(key, ht1(key) + ht2(key))
		Else
			ht3.Add(key, ht1(key))
		End If
	Next key
	For Each key In ht2.Keys
		If Not ht1.ContainsKey(key) Then
			ht3.Add(key, ht2(key))
		End If
	Next key
	Return ht3
End Function
Function ParamsValuToArray(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional ReturnArray As Boolean = False)
	Dim myArrayList As New ArrayList

	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			If Left(oFilter, 2) = "E=" Then
				If Item.Name = Right(oFilter, Len(oFilter) -2) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			Else
				If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			End If
		Next
	Next
	If ReturnArray Then
		Return myArrayList
	End If
	'My.Computer.Clipboard.SetText(teksti)
End Function
