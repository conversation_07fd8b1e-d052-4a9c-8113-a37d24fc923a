Sub Main()

ResultPointCoord = GetWorkpointCoord("f" & totalFramePcs & "/Center Point")
e_hall_tot_length = ResultPointCoord(1) 'Y suunta

end_a_model = ThisDoc.Path & "\End_A_" & SharedVariable("ProjectNum") & "\End_A_" & SharedVariable("ProjectNum") & "-"& SharedVariable("ProjectNum") & ".iam"
end_b_model = ThisDoc.Path & "\End_B_" & SharedVariable("ProjectNum") & "\End_B_" & SharedVariable("ProjectNum") & "-"& SharedVariable("ProjectNum") & ".iam"

Dim EndA = Components.Add("", end_a_model)

Constraints.AddUcsToUcs("EndA_Cons_1", EndA.Name, "origin",
"", "origin", yOffset := reinforcement_tube_dist_cl)
Constraints.AddFlush("EndA_Cons_2", EndA.Name, "XY Plane",
"", "XY Plane")

Dim EndB = Components.Add("", end_b_model)

Constraints.AddMate("EndB_Cons_1", EndB.Name, "XZ Plane",
"", "XZ Plane", e_hall_tot_length + reinforcement_tube_dist_cl)
Constraints.AddMate("EndB_Cons_2", EndB.Name, "YZ Plane",
"", "YZ Plane", e_hall_tot_width)
Constraints.AddFlush("EndB_Cons_3", EndB.Name, "XY Plane",
"", "XY Plane")
End Sub

Function GetWorkpointCoord(OccWP As String)
	'ResultPointCoord = GetWorkpointCoord("f8/p1k/InsertionPoint: Center Point") / ResultPointCoord = GetWorkpointCoord("f8/Center Point")
	oArvot = OccWP.Split(New Char() {"/"c })
	Dim subAsmOccurrence As ComponentOccurrence
	Dim partOccurrence As ComponentOccurrence
	Dim mainAsm As AssemblyDocument = ThisApplication.ActiveDocument
	If oArvot.Length = 3 Then
		subAsmOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
		partOccurrence = subAsmOccurrence.Definition.Occurrences.ItemByName(oArvot(1))
		oWP = oArvot(2)
	ElseIf oArvot.Length = 2
		partOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
		oWP = oArvot(1)
	End If
	' Locate the work point in the part
	Dim workPoint As WorkPoint = partOccurrence.Definition.WorkPoints.Item(oWP)
	' Get the position of the work point in the part's local coordinate system
	Dim pointPosition As Point = workPoint.Point
	' Get the transformation matrices for part and subassembly
	Dim partMatrix As Matrix = partOccurrence.Transformation

	If oArvot.Length = 3 Then
		Dim subAsmMatrix As Matrix = subAsmOccurrence.Transformation
		' Combine the transformations by multiplying the matrices
		partMatrix.TransformBy(subAsmMatrix)
	End If

	' Transform the point position manually using the combined matrix
	Dim x As Double = Round(partMatrix.Cell(1, 1) * pointPosition.X + partMatrix.Cell(1, 2) * pointPosition.Y + partMatrix.Cell(1, 3) * pointPosition.Z + partMatrix.Cell(1, 4), 3) * 10
	Dim y As Double = Round(partMatrix.Cell(2, 1) * pointPosition.X + partMatrix.Cell(2, 2) * pointPosition.Y + partMatrix.Cell(2, 3) * pointPosition.Z + partMatrix.Cell(2, 4), 3) * 10
	Dim z As Double = Round(partMatrix.Cell(3, 1) * pointPosition.X + partMatrix.Cell(3, 2) * pointPosition.Y + partMatrix.Cell(3, 3) * pointPosition.Z + partMatrix.Cell(3, 4), 3) * 10
	' Output the coordinates in the main assembly coordinate system
	Logger.Debug("X: " & x & ", Y: " & y & ", Z: " & z)
	Return {x, y, z }
End Function



Function RetunOcc(Hakuehto As String, Optional oNegFilter As String = "") As ComponentArgument

	For Each oOcc In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		oOccName = oOcc.Name ': Logger.Debug(oOccName & " vs " & Hakuehto)
		If InStrRev(oOccName, Hakuehto) <> 0 And InStrRev(oOccName, oNegFilter) = 0 Or oNegFilter = "" Then
			Polku = FullOccurrenceName(oOcc, 1, "")
			'Logger.Debug("oOcc path " & Polku & oOcc.Name)
			TekstiMuotoilu = Polku & oOcc.Name
			Dim TekstiMuotoilu2 As String() = TekstiMuotoilu.Split(New Char() {"#"c })
			RetunOcc = TekstiMuotoilu2.ToArray()
			Exit For
		End If
	Next
End Function
Function FullOccurrenceName(Occ As ComponentOccurrence, Level As Integer, Rakenne As String) As String
	Dim oParentOcc As ComponentOccurrence
	Try
		oParentOcc = Occ.ParentOccurrence
		FullOccurrenceName = oParentOcc.Name
		Rakenne = CStr(oParentOcc.Name & "#") & Rakenne
		Rakenne = FullOccurrenceName(oParentOcc, Level + 1, Rakenne)
	Catch
	End Try
	FullOccurrenceName = Rakenne
End Function


Function RetunOccName(Hakuehto As String)
	For Each oOcc In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences ' occs ei toimi kuin päätasolla leaf menee alirakenteisiin 
		oFullName = oOcc.ReferencedFileDescriptor.FullFileName
		If oFullName.contains(Hakuehto) Then
			occName = oOcc.name
			Return occName
		End If
	Next
End Function