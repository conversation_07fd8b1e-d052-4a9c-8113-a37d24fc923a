Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports Autodesk.DataManagement.Client.Framework.Vault.Services.Connection
Imports System.IO
AddReference "Autodesk.DataManagement.Client.Framework.Vault.dll"
AddReference "Autodesk.DataManagement.Client.Framework.dll"
AddReference "Connectivity.Application.VaultBase.dll"
AddReference "Autodesk.Connectivity.WebServices.dll"



Sub Main()
	
StartTime = Now
Dim myFileList As New ArrayList
RawList = ReadListFile("C:\Temp\Filelist.txt")

For Each oVal In RawList
	If Not myFileList.Contains(oVal) Then
		myFileList.Add(oVal)
		Logger.Debug(oVal)
	End If
Next



GetVaultedFilesList(myFileList)

'PlaceFiles = False 'True
'If PlaceFiles Then
'	For Each oFilu In myFileList
'		koe = Components.Add("", oFilu)
'	Next
'End If
Logger.Debug("GetVaultedFiles : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub


Function GetVaultedFilesList(oFilelist As ArrayList)
	Server = "**************" : Vault = "BHvault"
	UserName = "administrator" : Pword = "BH_Vault4dm1n" 'obs empty UserName trigger windows auth 
	For Each oVal In oFilelist
		GetVaultedFiles(oVal & ".ipt", "", Server, Vault, UserName, Pword)
		GetVaultedFiles(oVal & ".iam", "", Server, Vault, UserName, Pword)
	Next
End Function

Function GetVaultedFiles(strFileName As String, SearchOption As String, strVaultServer As String, strVaultName As String, strVaultUser As String, strVaultPwd As String)
	Dim files() As Autodesk.Connectivity.WebServices.File
	Dim HakutulosList As New ArrayList
	If strVaultUser = "" Then 'windows auth
		results = VDF.Vault.Library.ConnectionManager.LogIn(strVaultServer, strVaultName, Nothing, Nothing, VDF.Vault.Currency.Connections.AuthenticationFlags.WindowsAuthentication, Nothing)
	Else
		results = VDF.Vault.Library.ConnectionManager.LogIn(strVaultServer, strVaultName, strVaultUser, strVaultPwd, VDF.Vault.Currency.Connections.AuthenticationFlags.Standard, Nothing)
	End If

	If results.Success = False Then
		Logger.Debug("Vault login Failed " & strVaultName)
	Else
		'Logger.Debug("Login Succeeded")
	End If
	Dim connection As VDF.Vault.Currency.Connections.Connection = Nothing
	If results.Success Then
		connection = results.Connection
	Else
		Logger.Debug("Vault connection failed")
	End If
	
	files = {}
	txtRevision = Nothing
	Dim SrcCond1 As New SrchCond
	Dim SrcCond2 As New SrchCond
	Dim bookmark As String = String.Empty
	Dim status As SrchStatus = Nothing
	Dim filePropDefs As PropDef() = connection.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
	Dim filenamePropDef As PropDef = filePropDefs.[Single](Function(n) n.SysName = "ClientFileName")
	If SearchOption = "*" Then
		HakuTyyppi = 1
	Else
		HakuTyyppi = 3
	End If
	With SrcCond1
		.PropDefId = filenamePropDef.id
		.SrchOper = HakuTyyppi '1 Contains, 3 exact?!
		.PropTyp = PropertySearchType.SingleProperty
		.SrchRule = SearchRuleType.Must
		.SrchTxt = strFileName
	End With
	'
	'MsgBox("FindDocument: Searchcondition 1.  Filename property Id:" + filenamePropDef.Id.ToString)
	Dim SrcConds(0) As SrchCond
	SrcConds(0) = SrcCond1
	files = connection.WebServiceManager.DocumentService.FindFilesBySearchConditions(SrcConds, Nothing, Nothing, True, True, bookmark, status)
	'MsgBox("FindDocument: End find")
	Dim settings As New VDF.Vault.Settings.AcquireFilesSettings(connection)

	If files Is Nothing Then
		Logger.Debug("No Document " & strFileName)
	Else
		For Each res In files
			Dim oFileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(connection, res)
			settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeRelatedDocumentation = True
			settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
			settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = True
			settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
			settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = VDF.Vault.Currency.VersionGatheringOption.Latest
			settings.AddFileToAcquire(oFileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
		Next
	End If
	Dim aquiresults As VDF.Vault.Results.AcquireFilesResults = connection.FileManager.AcquireFiles(settings)

	If aquiresults.FileResults.count = 0 Then
		'Logger.Debug("no Doc with search option")
		'
	End If

	For Each aquiresult As VDF.Vault.Results.FileAcquisitionResult In aquiresults.FileResults
		Dim aquiresultpath As String = aquiresult.LocalPath.FullPath
		HakutulosList.Add(aquiresultpath)
		Logger.Debug(aquiresultpath)
	Next
End Function
Function ReadListFile(OFileName As String, Optional debuggaus As Boolean = False)
	Dim oRead As New StreamReader(OFileName)
	Dim sLine As String = ""
	Dim MyFileList As New ArrayList
	i = 0
	Do
		sLine = oRead.ReadLine()
		If Not sLine Is Nothing And Len(sLine) >0 Then
			i = i + 1
			If debuggaus Then : Logger.Debug("sLine " & sLine) : End If
		MyFileList.Add(sLine)
		End If
	Loop Until sLine Is Nothing
	If debuggaus Then : Logger.Debug("Readed " & i) : End If
	oRead.Close()
	Return MyFileList
End Function



