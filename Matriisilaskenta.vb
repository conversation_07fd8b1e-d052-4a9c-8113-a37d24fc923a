Sub Main()
'Dim matrix1 = {{1, 2, 3 }, {4, 5, 6 }, {7, 8, 9 }}
'Dim matrix2  = {{9, 8, 7 }, {6, 5, 4 }, {3, 2, 1 }}

'Dim matrix1 = {{1, 4, 7 }, {2, 5, 8 }, {3, 6, 9 }}
'Dim matrix2  = {{1, 2, 3 }, {4, 5, 6 }, {7, 8, 9 }}

'oX1 = 100 : oY1 = 100
'Dim matrix1 = {{1, 0, oX1 }, {0, 1, oY1 }, {0, 0, 1 }}
'oX2 = 200 : oY2 = 200
'Dim matrix2 = {{1, 0, oX2 }, {0, 1, oY2 }, {0, 0, 1 }}

rot = RotationMatrix(10)
mov = MoveMatrix(200, 200)
Dim result = MultiplyMatrices(rot, mov)
X1 = result(0, 2) : Y1 = result(1, 2)
Logger.Debug("X:" & Round(X1, 1) & "Y:" & Round(Y1, 1))

rot = RotationMatrix(0)
mov = MoveMatrix(200 + X1, 200 + Y1)

Dim result2 = MultiplyMatrices(rot, mov)
X2 = result2(0, 2) : Y2 = result2(1, 2)
Logger.Debug("X:" & Round(X2, 1) & "Y:" & Round(Y2, 1))


End Sub

Function RotationMatrix(oAngle As Double)
	oAngleRad = oAngle * PI / 180
	oMatrixK = {{Cos(oAngleRad), -Sin(oAngleRad), 0 }, {Sin(oAngleRad), Cos(oAngleRad), 0 }, {0, 0, 1 }}
	Return oMatrixK
End Function

Function MoveMatrix(oXvalue As Double, oYvalue As Double)
	MoveMatrix = {{1, 0, oXvalue }, {0, 1, oYvalue }, {0, 0, 1 }}
	Return MoveMatrix
End Function

Function MultiplyMatrices(matrix1, matrix2)
	Dim result(2, 2) As Double
	For i As Integer = 0 To 2
		For j As Integer = 0 To 2
			result(i, j) = 0
			For k As Integer = 0 To 2
				result(i, j) += matrix1(i, k) * matrix2(k, j)
			Next
		Next
	Next
	Return result
End Function

Function DegToRad(oDeg As Double)
	Return oDeg * PI / 180
End Function

Function CosDeg(oDeg As Double)
	Return Cos(oDeg * PI / 180)
End Function

Function SinDeg(oDeg As Double)
	Return Sin(oDeg * PI / 180)
End Function