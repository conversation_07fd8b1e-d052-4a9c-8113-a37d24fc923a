' Get the active document (must be an Assembly)
Dim oAssyDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim oOccs As ComponentOccurrences = oAssyDoc.ComponentDefinition.Occurrences

' List to store Center of Gravity and names for each occurrence
Dim cogList As New List(Of Point)
Dim originList As New List(Of Point)
Dim nameList As New List(Of String)
Dim occList As New ArrayList()

' Loop through occurrences and retrieve their Center of Gravity and Names
For Each oOcc As ComponentOccurrence In oOccs
	If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
		Dim massProps As MassProperties = oOcc.MassProperties
		Dim cog As Point = massProps.CenterOfMass
		cogList.Add(cog)
		nameList.Add(oOcc.Name)
		occList.Add(oOcc)
		Dim trans As Matrix = oOcc.Transformation
		Dim originPoint As Point = ThisApplication.TransientGeometry.CreatePoint(trans.Translation.X, trans.Translation.Y, trans.Translation.Z)
		originList.Add(originPoint)
	End If
Next

ThisApplication.ActiveDocument.SelectSet.Clear()
Dim thresholdDistance As Double = 5.0 ' 
' Compare each CoG with others
For i As Integer = 0 To cogList.Count - 1
	For j As Integer = i + 1 To cogList.Count - 1
		PointtiJ = cogList(j) : PointtiI = cogList(i) :
		Dim distance As Double = PointtiI.DistanceTo(PointtiJ)
		' Check if they are near each other
		If distance <= thresholdDistance Then
			If nameList(i) <> nameList(j) Then
				PointiJtxt = j & " [" & Round(PointtiJ.X) & "," & Round(PointtiJ.Y) & "," & Round(PointtiJ.Z) & "] "
				PointiItxt = i & " [" & Round(PointtiI.X) & "," & Round(PointtiI.Y) & "," & Round(PointtiI.Z) & "] "
				'Logger.Debug(" Occurrences '" & nameList(i) & " n:" & PointiItxt & "' and '" & nameList(j) & " n:" & PointiJtxt & "' are near each other. Distance: " & distance.ToString())
				PointtiJOrg = originList(j) : PointtiIOrg = originList(i) :
				Dim distanceOrg As Double = PointtiIOrg.DistanceTo(PointtiJOrg)
				'Logger.Debug("distanceOrg:" & distanceOrg)
				If distanceOrg <= thresholdDistance * 3 Then
					ThisApplication.ActiveDocument.SelectSet.Select(occList(i))
					ThisApplication.ActiveDocument.SelectSet.Select(occList(j))
				End If
			End If
		End If
	Next
Next

ThisApplication.CommandManager.ControlDefinitions("AssemblyIsolateCmd").Execute()