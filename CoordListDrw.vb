Imports System.IO
Sub main()
	
Dim oDict As New Dictionary(Of String, Object)
Try
	NumberListEdit = SharedVariable("NumberListEdit")
Catch
	BUfile = ThisDoc.Path & "\data.csv"
	If Not IO.File.Exists(BUfile) Then
		Logger.Debug("No data.csv file in project foleder, run edit list first")
		Exit Sub
	End If
	Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
	NumberListEdit = ImportCSVToDictionary(BUfile)
	SharedVariable("NumberListEdit") = NumberListEdit
End Try
Dim oGeneratedDetModelsFiltered As New ArrayList

For Each kvp As KeyValuePair(Of String, String()) In NumberListEdit
	oKey = kvp.Key
	Logger.Debug("oKey:" & oKey)
	ResultPointCoord = GetWorkpointCoord(oKey & "/InsertionPoint: Center Point")
	idNum = kvp.Value(0) : PNum = kvp.Value(1)
	'Logger.Debug(ResultPointCoord (0) & "y:" & ResultPointCoord (1)& "z:" & ResultPointCoord (2))
	oDict.Add(oKey & "#" & idNum, ResultPointCoord)
Next

SharedVariable("CoordListModule") = oDict

iLogicVb.RunRule("ModulePcs2drw")


End Sub

Function DrwCoordList(CoordListModule)
	'CoordListModule = SharedVariable("CoordListModule")

	Dim oDrawDoc As DrawingDocument = ThisApplication.ActiveDocument
	Dim oSheet As Sheet = oDrawDoc.ActiveSheet
	Dim oTextStyle As TextStyle = oDrawDoc.StylesManager.TextStyles("BH_TOLERACE_TEXT")

	For Each oEntry As KeyValuePair(Of String, Object) In CoordListModule
		oKey = oEntry.Key
		oKeySplitti = oKey.Split(New Char() {"#"c })
		ModuleName = oKeySplitti(0)
		ModuleNumber = oKeySplitti(1)
		CoordsX = oEntry.Value(0) / 10 'mm/sm juttu
		CoordsY = oEntry.Value(1) / 10
		CoordsZ = oEntry.Value(2) / 10

		Dim modelCoord As Point = ThisApplication.TransientGeometry.CreatePoint(CoordsX, CoordsY, CoordsZ)

		If ModuleName.Contains("/s") Then
			If ModuleName.Contains("sMir") Then
				oViewName = "RIGHT"
			Else
				oViewName = "LEFT"
			End If
		Else
			oViewName = "TOP"
		End If

		Dim oView As DrawingView = ActiveSheet.View(oViewName).View
		Dim sheetCoord As Point2d = oView.ModelToSheetSpace(modelCoord)
		Dim text As String = ModuleNumber
		Dim oNote As GeneralNote = oSheet.DrawingNotes.GeneralNotes.AddFitted(sheetCoord, text)
		'oNote.Rotation = Math.PI / 2
		oNote.TextStyle = oTextStyle
		oDimAtt = oNote.AttributeSets.Add("ilogic_Created")
	Next
End Function



Function GetWorkpointCoord(OccWP As String)
	'ResultPointCoord = GetWorkpointCoord("f8/p1k/InsertionPoint: Center Point") / ResultPointCoord = GetWorkpointCoord("f8/Center Point")
	oArvot = OccWP.Split(New Char() {"/"c })
	Dim subAsmOccurrence As ComponentOccurrence
	Dim partOccurrence As ComponentOccurrence

	Dim mainAsm As AssemblyDocument = ThisApplication.ActiveDocument
	If oArvot.Length = 3 Then
		Try
			subAsmOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
			partOccurrence = subAsmOccurrence.Definition.Occurrences.ItemByName(oArvot(1))
		Catch
			Exit Function
		End Try
		oWP = oArvot(2)
	ElseIf oArvot.Length = 2
		partOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
		oWP = oArvot(1)
	End If
	' Locate the work point in the part
	Dim workPoint As WorkPoint = partOccurrence.Definition.WorkPoints.Item(oWP)
	' Get the position of the work point in the part's local coordinate system
	Dim pointPosition As Point = workPoint.Point
	' Get the transformation matrices for part and subassembly
	Dim partMatrix As Matrix = partOccurrence.Transformation

	If oArvot.Length = 3 Then
		Dim subAsmMatrix As Matrix = subAsmOccurrence.Transformation
		' Combine the transformations by multiplying the matrices
		partMatrix.TransformBy(subAsmMatrix)
	End If

	' Transform the point position manually using the combined matrix
	Dim x As Double = Round(partMatrix.Cell(1, 1) * pointPosition.X + partMatrix.Cell(1, 2) * pointPosition.Y + partMatrix.Cell(1, 3) * pointPosition.Z + partMatrix.Cell(1, 4), 3) * 10
	Dim y As Double = Round(partMatrix.Cell(2, 1) * pointPosition.X + partMatrix.Cell(2, 2) * pointPosition.Y + partMatrix.Cell(2, 3) * pointPosition.Z + partMatrix.Cell(2, 4), 3) * 10
	Dim z As Double = Round(partMatrix.Cell(3, 1) * pointPosition.X + partMatrix.Cell(3, 2) * pointPosition.Y + partMatrix.Cell(3, 3) * pointPosition.Z + partMatrix.Cell(3, 4), 3) * 10
	' Output the coordinates in the main assembly coordinate system
	Logger.Debug("X: " & x & ", Y: " & y & ", Z: " & z)
	Return {x, y, z }
End Function
Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Try
		Using reader As New StreamReader(filePath)
			Dim line As String
			Do While (reader.Peek() >= 0)
				line = reader.ReadLine()
				Dim parts As String() = line.Split(","c) ' Split by comma
				Dim key As String = parts(0)
				Dim values As String() = parts.Skip(1).ToArray()
				dictionary.Add(key, values)
			Loop
		End Using
		Return dictionary
	Catch
		Logger.Debug("No data.csv file in project foleder, run edit list first")
	End Try
End Function