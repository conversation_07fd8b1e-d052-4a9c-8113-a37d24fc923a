Sub Main()
oActiveDoc = ThisDoc.Document.FullDocumentName

FNamePos = InStrRev(ThisDoc.Document.FullDocumentName, "\", -1)

ReplaceDrvPart(Right(oActiveDoc, Len(oActiveDoc) -FNamePos).Replace(".iam", ""))



End Sub

Function ReplaceDrvPart(oNewFolder As String)
	Dim oDoc As Document = ThisDoc.Document
	Dim oRefFile As FileDescriptor
	For Each oRefFile In oDoc.File.ReferencedFileDescriptors


		oNewPart = "E:\Inventor\Designs\Proj\" & oNewFolder & "\" & Right(oRefFile.FullFileName, Len(oRefFile.FullFileName) -InStrRev(oRefFile.FullFileName, "\", -1))


				Try
					oRefFile.ReplaceReference(oNewPart)
					Logger.Debug("Korvataan: " & oRefFile.FullFileName & " tiedostolla " & oNewPart)
				Catch
				End Try


	Next
	iLogicVb.UpdateWhenDone = True
End Function