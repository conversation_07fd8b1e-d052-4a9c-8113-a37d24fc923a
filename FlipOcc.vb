Sub Main()
Dim oDoc As Document = ThisApplication.ActiveDocument
Dim oAsmDoc As AssemblyDocument = CType(oDoc, AssemblyDocument)
Dim oObjectCollection As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
'[Select Pre&Pick
Dim oSelectSet As SelectSet = oAsmDoc.SelectSet
Dim preSelectedCount As Integer = 0
If oSelectSet.Count > 0 Then
	For Each selectedItem As Object In oSelectSet
		If TypeOf selectedItem Is ComponentOccurrence Then
			Dim oOcc As ComponentOccurrence = CType(selectedItem, ComponentOccurrence)
			' Check if it's already in our collection (shouldn't be at this stage, but good practice)
			Dim alreadyExists As Boolean = False
			For Each itemInCollection As Object In oObjectCollection
				If itemInCollection Is oOcc Then
					alreadyExists = True
					Exit For
				End If
			Next
			If Not alreadyExists Then
				oObjectCollection.Add(oOcc)
				preSelectedCount += 1
			End If
		End If
	Next
	If preSelectedCount > 0 Then
		Logger.Debug(preSelectedCount & " item(s) were pre-selected and added to the list.")
	End If
End If

Dim selectedOccurrence As ComponentOccurrence
Dim continueSelection As Boolean = True
Dim pickPrompt As String

Do While continueSelection
	If oObjectCollection.Count > 0 Then
		pickPrompt = "Select an additional occurrence or press ESC to process " & oObjectCollection.Count & " selected item(s)."
	Else
		pickPrompt = "Select an occurrence (Press ESC to cancel if no items are selected)."
	End If

	Try
		selectedOccurrence = ThisApplication.CommandManager.Pick( _
		SelectionFilterEnum.kAssemblyOccurrenceFilter, _
		pickPrompt)
		' Check if Pick returned Nothing (e.g., user pressed ESC immediately or after a selection)
		If selectedOccurrence Is Nothing Then
			continueSelection = False ' Exit loop if ESC was pressed
		Else
			' Check if the newly picked item already exists in the collection
			Dim alreadyExists As Boolean = False
			For Each item As Object In oObjectCollection
				If item Is selectedOccurrence Then ' Use "Is" for object reference comparison
					alreadyExists = True
					MessageBox.Show("'" & selectedOccurrence.Name & "' is already in the selection list.", "Already Selected", MessageBoxButtons.OK, MessageBoxIcon.Information)
					Exit For
				End If
			Next

			If Not alreadyExists Then
				oObjectCollection.Add(selectedOccurrence)
				Logger.Debug("Added via pick: " & selectedOccurrence.Name & ". Total selected: " & oObjectCollection.Count)
			End If
		End If
	Catch ex As Exception
		Logger.Debug("Exception during Pick command (likely ESC): " & ex.Message)
		continueSelection = False
	End Try
Loop
' --- STEP 3: Process the Combined Selection ---
If oObjectCollection.Count = 0 Then
	MsgBox("No occurrences were selected. Exiting rule.", vbInformation, "Selection Empty")
	Exit Sub
End If
Logger.Debug("Proceeding to process " & oObjectCollection.Count & " occurrence(s).")
']	

For Each oOccu As ComponentOccurrence In oObjectCollection ' More type-safe iteration
	FlipSelectedOcc(oOccu)
Next

MsgBox("Processing complete for " & oObjectCollection.Count & " occurrence(s).", vbInformation, "Finished")

End Sub


Function FlipSelectedOcc(oOcc As ComponentOccurrence)
	'Dim oOcc As ComponentOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Occurrence")
	For Each oConstraint In oOcc.Constraints
		oConstName = oConstraint.name.replace("_point", "").replace("_dir", "").replace("_ang", "")
		If oConstName Is Nothing Then
			Exit Function
		End If
		oData = oConstName.Split(New Char() {"#"c })
		EkaNode = oData(1)
		TokaNode = oData(2)
		oConsName = oData(0) & "#" & TokaNode & "#" & EkaNode
		EkaNodeData = EkaNode.Split(New Char() {"/"c })
		TokaNodeData = TokaNode.Split(New Char() {"/"c })
		oConstraint.delete()
	Next

	If oData Is Nothing Then
		Logger.Debug("No Constraint information")
		Exit Function
	End If

	lahtoPiste = TokaNodeData(2).Replace("_L1", "").Replace("_L2", "") 'onkohan näitä enemmänkin?!
	loppuPiste = EkaNodeData(2).Replace("_L1", "").Replace("_L2", "")

	
	Dim CompoArguJointOcc As ComponentArgument = oOcc.Name.ToString
	Dim CompoArguStartPoint As ComponentArgument = {EkaNodeData(0), TokaNodeData(1) }
	Dim CompoArguEndPoint As ComponentArgument = {TokaNodeData(0), EkaNodeData(1) }

	Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
	Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
	Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
End Function

Function GetAllDocsParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional PalautusData As Boolean = False)
	Dim myArrayList As New ArrayList : Dim oRefDoc As Document : Dim ParaMeterHash As Hashtable = New Hashtable
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters    
		Pituus = Len(oFilter)
		For Each Item In UserParams
			oParaName = Item.Name
			If oParaName.contains(oFilter) And Left(oParaName, Len(oNegFilter)) <> oNegFilter Then

				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				Else If Item.Units = "deg" Then
				Arvo = Item.Value * 180 / PI
			Else
				Arvo = Item.Value
			End If
			myArrayList.Add(Item.Name & "#" & Arvo & "#" & oRefDoc.DisplayName)
			If Not ParaMeterHash.ContainsKey(oParaName) Then
				ParaMeterHash.Add(oParaName, Arvo) 'uusiarvo
			Else
				If Arvo <> ParaMeterHash(oParaName) Then 'ei lisätä duplikaatteja
					ParaMeterHash.Add(oParaName & "#" & oRefDoc.DisplayName, Arvo)
				End If
			End If
			End If
		Next
	Next
	If PalautusData Then
		Return myArrayList 'array voisi tallentaa multivalue parametriin
	Else
		Return ParaMeterHash
	End If
End Function
Function GetAllOccurance(Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Dim oOcc As ComponentOccurrence : Dim TunnisteTaulu As Hashtable = New Hashtable

	For Each oOcc In oDoc.ComponentDefinition.Occurrences
		If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If oOcc.SubOccurrences.Count = 0 Then
				oAllOccur.Add(oOcc)
			Else
				oAllOccur.Add(oOcc)
				processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
			End If
		End If
	Next
	Return oAllOccur
End Function
Function processAllSubOcc(ByVal oOcc As ComponentOccurrence, oAllOccur As ObjectCollection, Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oSubCompOcc As ComponentOccurrence
	For Each oSubCompOcc In oOcc.SubOccurrences
		If oSubCompOcc.SubOccurrences.Count = 0 Then
			oAllOccur.Add(oSubCompOcc)
		Else
			oAllOccur.Add(oSubCompOcc)
			processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
		End If
	Next
End Function
Function GetConsData(sWPFilter As String)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Try
			FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			'Logger.Debug("OccurrencePath: " & oOccurrence.OccurrencePath.ToString)
			Logger.Debug("Constraints: " & oOccurrence.Constraints.Count)

		Catch
			Logger.Debug("Error!! getting filename: " & oSearch)
		End Try
	Next
End Function
Function GetPointData(sWPFilter As String)
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	Dim myArrayList As New ArrayList
	Dim UCStaulu As Hashtable = New Hashtable
	Dim oUcsName As String()
	oDataTeksti = ""
	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If Not TypeOf thisOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisOcc.Definition Is WeldsComponentDefinition Then
				If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
					Dim currentWP As WorkPoint
					For Each currentWP In thisOcc.Definition.WorkPoints
						'check if pointname contains underscore
						If InStr(1, currentWP.Name, sWPFilter) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)
							Logger.Debug(thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
							oUcsName = oAsmPoint.name.Split(New Char() {":"c })
							oUCS = thisOcc.Name & "\" & oUcsName(0)
							myArrayList.Add(oUCS)
							UCStaulu.Add(oUCS, X & "," & Y & "," & Z)

						End If
					Next
				End If
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed Then
						If Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
							If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
								Dim currentSubWP As WorkPoint
								For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
									'check if pointname contains underscore
									If InStr(1, currentSubWP.Name, sWPFilter) Then
										Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
										X = Round(oAsmPoint.point.X)
										Y = Round(oAsmPoint.point.Y)
										Z = Round(oAsmPoint.point.Z)
										Logger.Debug(thisSubOcc.Name & " 2nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
										oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
										oUcsName = oAsmPoint.name.Split(New Char() {":"c })
										oUCS = thisOcc.Name & "\" & thisSubOcc.Name & "\" & oUcsName(0)
										myArrayList.Add(oUCS)
										UCStaulu.Add(oUCS, X & "," & Y & "," & Z)
									End If
								Next
							End If
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = Round(oAsmPoint.point.X)
												Y = Round(oAsmPoint.point.Y)
												Z = Round(oAsmPoint.point.Z)

												Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
												oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												oUcsName = oAsmPoint.name.Split(New Char() {":"c })

												oUCS = thisOcc.Name & "\" & thisSubOcc.Name & "\" & thisSubOcc.Name & "\" & oUcsName(0)
												myArrayList.Add(oUCS)
												UCStaulu.Add(oUCS, X & "," & Y & "," & Z)
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next

	CoordDataa = oDataTeksti
	My.Computer.Clipboard.SetText(oDataTeksti)
	Return myArrayList
End Function
Function DeleteMultipleOccs()
	For Each oSelection In SelectMultipleOccs()
		oSelection.delete
	Next
End Function
Function SelectMultipleOccs(Optional sWPFilter As String = "")
	Dim oDoc As Document
	oDoc = ThisApplication.ActiveDocument
	' Create a new SelectSet
	Dim oSelectSet As SelectSet
	oSelectSet = oDoc.SelectSet
	' Clear the SelectSet
	oSelectSet.Clear()
	' Use the Pick method to select multiple occurrences
	Dim oOccurrence As ComponentOccurrence
	Dim continuePicking As Boolean = True

	While continuePicking
		Try
			oOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Pick an editing component")
			oSelectSet.Select(oOccurrence)
		Catch ex As Exception
			' If the user cancels the pick operation, exit the loop
			continuePicking = False
		End Try
	End While

	Return myArrayList
End Function

Function GetUcsByNames(UCSinfo As String)
	'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
	oData = UCSinfo.Split(New Char() {"/"c })
	If oData.Length = 3 Then
		Dim oName As String() = {oData(0), oData(1) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(2))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
		End Try
		Exit Function
	ElseIf oData.Length = 2 Then
		Dim oName As String() = {oData(0) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(1))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try
	End If
	Return oUcs
End Function