Imports System.IO

Sub Main()
'SharedVariable.Remove("GenDrwList")
Try
	OnkoGenDrwList_SH = SharedVariable(GenDrwList)
	Logger.Debug("using sh parameter:")
	GenDrwList = SharedVariable(GenDrwList)
Catch
	If System.IO.File.Exists(ThisDoc.Path & "\drw.csv") Then
		GenDrwList = ImportCSVToDictionary(ThisDoc.Path & "\drw.csv")
		Logger.Debug("txt parameter:")
	Else
		Try 'listan hakeminen
			NumberListEdit = SharedVariable("NumberListEdit")
		Catch
			BUfile = ThisDoc.Path & "\data.csv"
			Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
			NumberListEdit = ImportCSVToDictionary(BUfile)
			SharedVariable("NumberListEdit") = NumberListEdit
		End Try
		GenDrwList = ConvertModelListToDrwList(NumberListEdit)
		Logger.Debug("Initial calucation of drw list")
	End If
End Try


ModulePcs = FormatDrwList(GenDrwList)
End Sub
Function FormatDrwList(NumberSchemaDrw As Dictionary(Of String, String()))
	Dim data As New Dictionary(Of String, Integer)
	Dim propList As New List(Of String)

	For Each kvp In NumberSchemaDrw
		Dim mainKey As String = kvp.Key
		Dim subValue As String = kvp.Value(2)
		subModules = subValue.Split(New Char() {"-"c })

		data.Add(mainKey, subModules.Length)
	Next
	For Each kvp As KeyValuePair(Of String, Integer) In data
		' Adjust the width for alignment (e.g. 6 characters for key, 3 for number)
		Dim alignedKey As String = kvp.Key.PadRight(6)
		Dim alignedQty As String = kvp.Value.ToString().PadLeft(3)
		propList.Add(alignedKey & alignedQty & " pcs")
	Next

	Dim sortList As New List(Of KeyValuePair(Of String, String))

	For Each item As String In propList
		Dim letter As String = item.Substring(0, 1)
		Dim match As System.Text.RegularExpressions.Match = _
		System.Text.RegularExpressions.Regex.Match(item, "[A-Z](\d+)")
		Dim number As Integer = 0
		If match.Success Then
			number = CInt(match.Groups(1).Value)
		End If
		Dim sortKey As String = letter & number.ToString("D4")
		sortList.Add(New KeyValuePair(Of String, String)(sortKey, item))
	Next
	sortList.Sort(Function(x, y) x.Key.CompareTo(y.Key))
	' Create the sorted list
	Dim sortedList As New List(Of String)
	For Each kvp In sortList
		sortedList.Add(kvp.Value)
	Next
	
	clipboardText = String.Join(vbCrLf, sortedList)
	'MessageBox.Show(String.Join(vbCrLf, propList))
	My.Computer.Clipboard.SetText(clipboardText)
	Return clipboardText
End Function







Function FormatDrwListOld(NumberSchemaDrw As Dictionary(Of String, String()))
	Dim propList As New ArrayList
	propList.Add("Module|Pcs")
	For Each kvp In NumberSchemaDrw
		Dim mainKey As String = kvp.Key
		Dim subValue As String = kvp.Value(2)
		subModules = subValue.Split(New Char() {"-"c })

		propList.Add(mainKey & " " & subModules.Length & " pcs") ' [" & subValue & "]")
	Next

	selectedItem = InputListBox("Component Properties: ", propList, propList(0), "Component Data Viewer", "Properties & Parameters List")

	Dim clipboardText As String = String.Join(System.Environment.NewLine, propList.ToArray())
	My.Computer.Clipboard.SetText(clipboardText)

End Function



Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function
Function ConvertModelListToDrwList(originalDict As Dictionary(Of String, String()))
	Dim groupedDict As New System.Collections.Generic.Dictionary(Of String, List(Of String))()

	For Each kvp In originalDict
		Dim mainKey As String = kvp.Key
		Dim subKey As String = kvp.Value(0)
		' Check if subKey already exists in groupedDict
		If Not groupedDict.ContainsKey(subKey) Then
			groupedDict(subKey) = New List(Of String)()
			' Add placeholder values "A" and "B" as an example

			oNumero = CDbl(Mid(subKey, 3, Len(subKey) -2))
			edellinenNumero = oNumero - 1

			If edellinenNumero>0
				Alku = Left(subKey, 2)
				groupedDict(subKey).Add(Alku & edellinenNumero)
			Else
				groupedDict(subKey).Add("-")
			End If
			groupedDict(subKey).Add("no")
		End If
		groupedDict(subKey).Add(mainKey)
	Next
	' Combine only the original keys into a single string
	Dim finalDict As New System.Collections.Generic.Dictionary(Of String, String())()
	For Each kvp In groupedDict
		Dim key As String = kvp.Key
		Dim values As List(Of String) = kvp.Value
		' Extract placeholders ("A", "B") and combine original keys
		Dim placeholders As List(Of String) = values.Take(2).ToList()
		Dim combinedKeys As String = String.Join("-", values.Skip(2))
		' Create the final structure with placeholders and combined keys
		Dim finalValues As String() = placeholders.Concat({combinedKeys }).ToArray()
		finalDict.Add(key, finalValues)
	Next
	Return finalDict
End Function