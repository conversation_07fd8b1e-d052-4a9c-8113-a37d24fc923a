Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
Dim iLogic As Object = addIn.Automation
Dim myArrayList As New ArrayList
oFilter = "a.iam" : oRuleName = "BoltSetCalcu"

For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
	oFullName = oRefDoc.FullFileName
	If Not myArrayList.Contains(oFullName) And oFullName.Contains(oFilter)
		myArrayList.Add(oFullName)
		oDoc = ThisApplication.Documents.Open(oFullName, True)
		Try
			Call iLogic.RunRule(oDoc, oRuleName)
		Catch
			Logger.Debug("Unable to run the rule")
		Finally
			oDoc = Nothing
		End Try
		ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
	End If
Next