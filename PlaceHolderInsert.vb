Dim myArrayList As New ArrayList
'[

myArrayList.Add({10815,51100,10035 })
myArrayList.Add({19185,56900,10035 })
myArrayList.Add({29827,56900,6677 })
myArrayList.Add({8038,100,9291 })
myArrayList.Add({26823,51100,7723 })
myArrayList.Add({173,51100,6677 })
myArrayList.Add({10815,56900,10035 })
myArrayList.Add({3177,56900,7723 })
myArrayList.Add({21962,100,9291 })
myArrayList.Add({10815,56900,10035 })
myArrayList.Add({26823,56900,7723 })
myArrayList.Add({173,56900,6677 })
myArrayList.Add({24595,5900,8490 })
myArrayList.Add({8038,5900,9291 })
myArrayList.Add({21962,5900,9291 })
myArrayList.Add({3177,51100,7723 })
myArrayList.Add({173,51100,6677 })
myArrayList.Add({5405,5900,8490 })
myArrayList.Add({5405,100,8490 })
myArrayList.Add({21962,5900,9291 })
myArrayList.Add({26823,56900,7723 })
myArrayList.Add({3177,56900,7723 })
myArrayList.Add({29827,56900,6677 })
myArrayList.Add({24595,100,8490 })
myArrayList.Add({19185,51100,10035 })
myArrayList.Add({173,56900,6677 })
myArrayList.Add({29827,51100,6677 })
myArrayList.Add({26823,51100,7723 })
myArrayList.Add({21962,100,9291 })
myArrayList.Add({24595,100,8490 })
myArrayList.Add({3177,51100,7723 })
myArrayList.Add({19185,51100,10035 })
myArrayList.Add({8038,5900,9291 })
myArrayList.Add({5405,5900,8490 })
myArrayList.Add({29827,51100,6677 })
myArrayList.Add({8038,100,9291 })
myArrayList.Add({10815,51100,10035 })
myArrayList.Add({5405,100,8490 })
myArrayList.Add({19185,56900,10035 })
myArrayList.Add({24595,5900,8490 })

']
n = 161

For i = 0 To myArrayList.Count-1
	testlatta_1Pos = ThisAssembly.Geometry.Point(myArrayList(i)(0), myArrayList(i)(1), myArrayList(i)(2))
	testlatta_1 = Components.Add("", "testlatta.ipt", position := testlatta_1Pos)
Next

