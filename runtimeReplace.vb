Dim myArrayList As New ArrayList 'runtimeReplace
Dim FNameVsOcc As Hashtable = New Hashtable
Dim IDVsFName As Hashtable = New Hashtable

For Each oOcc As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
	If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
		If Left(oOcc.Name, 1) = "e" Or Left(oOcc.Name, 5) = "Wind " Or  Left(oOcc.Name, 5) = "Wall " Then 'takista puuuttuuko vielä jotain tyyppejä...
			oFileName = oOcc.ReferencedDocumentDescriptor.FullDocumentName
			If Not FNameVsOcc.ContainsKey(oFileName) Then
				FNameVsOcc.Add(oFileName, oOcc.Name)
				oDesc = iProperties.Value(oOcc.Name, "Project", "Description")
				oStockNum = iProperties.Value(oOcc.Name, "Project", "Stock Number")
				oG_L = Parameter(oOcc.Name, "G_L")
				Try : oSDA_aux= Parameter(oOcc.Name, "SDA_aux") : Catch : oSDA_aux="" : End Try
				oTunniste = oDesc & oG_L & oStockNum & oSDA_aux
				If Not IDVsFName.ContainsKey(oTunniste) Then
					IDVsFName.Add(oTunniste,oFileName)
				Else
					Try
						
						Component.Replace(oOcc.Name, IDVsFName(oTunniste), True)
					Logger.Debug("Replacing " & oOcc.Name & " using file " & IDVsFName(oTunniste) & " because id: " & oTunniste)
					Catch
						Logger.Debug("Error replacing " & oOcc.Name & " with " & IDVsFName(oTunniste))
					End Try
				End If
			End If
		End If
	End If
Next
