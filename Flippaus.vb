Sub main()
Dim oRefDocs As DocumentsEnumerator = ThisApplication.ActiveDocument.AllReferencedDocuments

'AllParams = GetAllDocsParams("", "d", oRefDocs, False)

'oListInfo = GetPointData("joint_")
'AlloOccs = GetAllOccurance("f")

'Logger.Debug("e_roof_pcs:" & AllParams("e_roof_pcs"))
FlipSelectedOcc()
'
'DeleteMultipleOccs()

End Sub
Function FlipSelectedOcc()
	Dim oOcc As ComponentOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Occurrence")
	For Each oConstraint In oOcc.Constraints
		oConstName = oConstraint.name.replace("_point", "").replace("_dir", "").replace("_ang", "")
		If oConstName Is Nothing Then
			Exit Function
		End If
		oData = oConstName.Split(New Char() {"#"c })
		EkaNode = oData(1)
		TokaNode = oData(2)
		oConsName = oData(0) & "#" & TokaNode & "#" & EkaNode
		EkaNodeData = EkaNode.Split(New Char() {"/"c })
		TokaNodeData = TokaNode.Split(New Char() {"/"c })
		oConstraint.delete()
	Next
	lahtoPiste = TokaNodeData(2)

	loppuPiste = EkaNodeData(2)
	Dim CompoArguJointOcc As ComponentArgument = oOcc.Name.ToString
	Dim CompoArguStartPoint As ComponentArgument = {EkaNodeData(0), TokaNodeData(1) }
	Dim CompoArguEndPoint As ComponentArgument = {TokaNodeData(0), EkaNodeData(1) }

	Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
	Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
	Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try

End Function
Function GetAllDocsParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional PalautusData As Boolean = False)
	Dim myArrayList As New ArrayList : Dim oRefDoc As Document : Dim ParaMeterHash As Hashtable = New Hashtable
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters '.ModelParameters    
		Pituus = Len(oFilter)
		For Each Item In UserParams
			oParaName = Item.Name
			If oParaName.contains(oFilter) And Left(oParaName, Len(oNegFilter)) <> oNegFilter Then

				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				Else If Item.Units = "deg" Then
				Arvo = Item.Value * 180 / PI
			Else
				Arvo = Item.Value
			End If

			myArrayList.Add(Item.Name & "#" & Arvo & "#" & oRefDoc.DisplayName)
			If Not ParaMeterHash.ContainsKey(oParaName) Then
				ParaMeterHash.Add(oParaName, Arvo) 'uusiarvo
			Else
				If Arvo <> ParaMeterHash(oParaName) Then 'ei lisätä duplikaatteja
					ParaMeterHash.Add(oParaName & "#" & oRefDoc.DisplayName, Arvo)
				End If
			End If
			End If
		Next
	Next
	If PalautusData Then
		Return myArrayList 'array voisi tallentaa multivalue parametriin
	Else
		Return ParaMeterHash
	End If
End Function
Function GetAllOccurance(Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Dim oOcc As ComponentOccurrence : Dim TunnisteTaulu As Hashtable = New Hashtable

	For Each oOcc In oDoc.ComponentDefinition.Occurrences
		If Not TypeOf oOcc.Definition Is VirtualComponentDefinition And Not TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If oOcc.SubOccurrences.Count = 0 Then
				oAllOccur.Add(oOcc)
			Else
				oAllOccur.Add(oOcc)
				processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
			End If
		End If
	Next
	Return oAllOccur
End Function
Function processAllSubOcc(ByVal oOcc As ComponentOccurrence, oAllOccur As ObjectCollection, Optional Filtteri As String = "", Optional NegFiltteri As String = "")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oSubCompOcc As ComponentOccurrence
	For Each oSubCompOcc In oOcc.SubOccurrences
		If oSubCompOcc.SubOccurrences.Count = 0 Then
			oAllOccur.Add(oSubCompOcc)
		Else
			oAllOccur.Add(oSubCompOcc)
			processAllSubOcc(oOcc, oAllOccur, Filtteri, NegFiltteri)
		End If
	Next
End Function
Function GetConsData(sWPFilter As String)
	
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Try
			FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			'Logger.Debug("OccurrencePath: " & oOccurrence.OccurrencePath.ToString)
			Logger.Debug("Constraints: " & oOccurrence.Constraints.Count)

		Catch
			Logger.Debug("Error!! getting filename: " & oSearch)
		End Try
	Next
End Function
Function GetPointData(sWPFilter As String)
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	Dim myArrayList As New ArrayList
	Dim UCStaulu As Hashtable = New Hashtable
	Dim oUcsName As String()
	oDataTeksti = ""
	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If Not TypeOf thisOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisOcc.Definition Is WeldsComponentDefinition Then
				If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
					Dim currentWP As WorkPoint
					For Each currentWP In thisOcc.Definition.WorkPoints
						'check if pointname contains underscore
						If InStr(1, currentWP.Name, sWPFilter) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)
							Logger.Debug(thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
							oUcsName = oAsmPoint.name.Split(New Char() {":"c })
							oUCS = thisOcc.Name & "\" & oUcsName(0)
							myArrayList.Add(oUCS)
							UCStaulu.Add(oUCS, X & "," & Y & "," & Z)

						End If
					Next
				End If
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed Then
						If Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
							If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
								Dim currentSubWP As WorkPoint
								For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
									'check if pointname contains underscore
									If InStr(1, currentSubWP.Name, sWPFilter) Then
										Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
										X = Round(oAsmPoint.point.X)
										Y = Round(oAsmPoint.point.Y)
										Z = Round(oAsmPoint.point.Z)
										Logger.Debug(thisSubOcc.Name & " 2nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
										oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
										oUcsName = oAsmPoint.name.Split(New Char() {":"c })
										oUCS = thisOcc.Name & "\" & thisSubOcc.Name & "\" & oUcsName(0)
										myArrayList.Add(oUCS)
										UCStaulu.Add(oUCS, X & "," & Y & "," & Z)
									End If
								Next
							End If
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = Round(oAsmPoint.point.X)
												Y = Round(oAsmPoint.point.Y)
												Z = Round(oAsmPoint.point.Z)

												Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
												oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												oUcsName = oAsmPoint.name.Split(New Char() {":"c })

												oUCS = thisOcc.Name & "\" & thisSubOcc.Name & "\" & thisSubOcc.Name & "\" & oUcsName(0)
												myArrayList.Add(oUCS)
												UCStaulu.Add(oUCS, X & "," & Y & "," & Z)
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next
	
	CoordDataa = oDataTeksti
	My.Computer.Clipboard.SetText(oDataTeksti)
	Return myArrayList
End Function
Function DeleteMultipleOccs()
	For Each oSelection In SelectMultipleOccs()
		oSelection.delete
	Next
End Function
Function SelectMultipleOccs(Optional sWPFilter As String = "")
	Dim oDoc As Document
	oDoc = ThisApplication.ActiveDocument
	' Create a new SelectSet
	Dim oSelectSet As SelectSet
	oSelectSet = oDoc.SelectSet
	' Clear the SelectSet
	oSelectSet.Clear()
	' Use the Pick method to select multiple occurrences
	Dim oOccurrence As ComponentOccurrence
	Dim continuePicking As Boolean = True

	While continuePicking
		Try
			oOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Pick an editing component")
			oSelectSet.Select(oOccurrence)
		Catch ex As Exception
			' If the user cancels the pick operation, exit the loop
			continuePicking = False
		End Try
	End While

	Return myArrayList
End Function

Function GetUcsByNames(UCSinfo As String)
	'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
	oData = UCSinfo.Split(New Char() {"/"c })
	If oData.Length = 3 Then
		Dim oName As String() = {oData(0), oData(1) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(2))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
		End Try
		Exit Function
	ElseIf oData.Length = 2 Then
		Dim oName As String() = {oData(0) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(1))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try
	End If
	Return oUcs
End Function