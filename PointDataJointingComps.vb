Sub main()
StartTime = Now

Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
Dim BraceHash As Hashtable = New Hashtable : Dim CrossarmHash As Hashtable = New Hashtable
Dim WindBraceHash As Hashtable = New Hashtable : Dim PurlinHash As Hashtable = New Hashtable
Dim WallBraceHash As Hashtable = New Hashtable : Dim EaveHash As Hashtable = New Hashtable
Dim RidgeBraceHash As Hashtable = New Hashtable ': Dim PurlinHash As Hashtable = New Hashtable

Dim threshold As Double = 1.0



sWPFilters = {"Start", "End" } '"UCS" 
oDataTeksti = ""

CounterCrossarm2 = 0 : CounterCrossarm3 = 0 : oCounterWindBrace = 0
oCounterBrace = 0 : oCountercrossarm = 0 : oCounterPurlin = 0 : oCounterWallBrace = 0
oCounterEave = 0 : oCounterRidge = 0



For i = 1 To oAsmDef.Occurrences.Count
	thisOcc = oAsmDef.Occurrences(i)
	'skip suppressed components
	If Not thisOcc.Suppressed And Not TypeOf thisOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisOcc.Definition Is WeldsComponentDefinition Then
		Try : oCategory = iProperties.Value(thisOcc.Name, "Summary", "Category") : Catch : oCategory = "" : End Try

		If oCategory = "purlin" Then 'oma miten 2/3 muut osat
			PurlinHash.Add(thisOcc.Name, oCounterPurlin)
			oCounterPurlin += 1
		End If

		If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
			Dim currentWP As WorkPoint
			For Each currentWP In thisOcc.Definition.WorkPoints
				'check if pointname contains underscore
				For Each oWPFilter In sWPFilters
					If currentWP.Name = oWPFilter Then
						Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
						X = Round(oAsmPoint.point.X * 10, 1)
						Y = Round(oAsmPoint.point.Y * 10, 1)
						Z = Round(oAsmPoint.point.Z * 10, 1) ': Logger.Debug(thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")

						If oCategory = "brace" Then
							BraceHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterBrace, oAsmPoint)
							oCounterBrace += 1
						End If

						If oCategory = "crossarm" Then
							Try : MidPos = Parameter(thisOcc.Name, "G_L1") : Catch : MidPos = 0 : End Try
							If MidPos<100 Then
								HolePcs = 2
								CounterCrossarm2 += 1
							Else
								HolePcs = 3
								CounterCrossarm3 += 1
							End If
							CrossarmHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterBrace & "#" & HolePcs, oAsmPoint)
							oCountercrossarm += 1
						End If

						If oCategory = "windbrace" Then
							WindBraceHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterWindBrace, oAsmPoint)
							oCounterWindBrace += 1
						End If

						If oCategory = "wallbrace" Then
							WallBraceHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterWallBrace, oAsmPoint)
							oCounterWallBrace += 1
						End If

						If oCategory = "eavetube" Then
							EaveHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterEave, oAsmPoint)
							oCounterEave += 1
						End If

						If oCategory = "ridgetube" Then
							RidgeBraceHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterRidge, oAsmPoint)
							oCounterRidge += 1
						End If


						oDataTeksti += thisOcc.Name & "\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
					End If
				Next 'filter
			Next

		End If 'defType
	End If 'suppressed
Next

'[ sidosputki laskenta
Laskuri0 = 0 : Laskuri1 = 0

For Each key1 In BraceHash.Keys
	wp1 = BraceHash(key1)
	' Get the coordinates of the first work point
	Dim coord1 As Point = wp1.Point
	' Inner loop to compare with every other work point
	For Each key2 In BraceHash.Keys
		' Avoid comparing a work point with itself
		If key1 <> key2 Then
			wp2 = BraceHash(key2)
			' Get the coordinates of the second work point
			Dim coord2 As Point = wp2.Point
			' Calculate the distance between the two work points
			distance = coord1.DistanceTo(coord2)
			' Check if the distance is within the threshold
			If distance <= threshold Then
				ResulttiTest += key1 & " vs " & key2 & " dist: " & distance
				'Logger.Debug("WorkPoint 1: " & key1 & vbLf & " and WorkPoint " & key2 & vbLf & "Distance: " & distance)
				Laskuri0 += 1
			Else
				'Logger.Debug("WorkPoint " & key1 & " and WorkPoint " & key2 & " are not near each other. Distance: " & distance)
				Laskuri1 += 1
			End If
		End If
	Next
Next


totBraces = BraceHash.Count / 2
DuplicateBoltSetBraces = Laskuri0 / 2

bolt_brace_cal_pcs_tot = totBraces * 2 - Laskuri0 / 2
'-Laskuri0 / 2 'hash laskee alku&loppupään eli 2 kpl, mutta samassa on molemmat

bolt_brace_cal_pcs = bolt_brace_cal_pcs_tot * 0.5
bolt_brace_xtra_factor = bolt_brace_cal_pcs_tot * 0.5 'alempi

bolt_brace_info = "Total amount of braces: " & totBraces & vbLf & " Duplicates boltsets: " & DuplicateBoltSetBraces & "  Total set Pcs:  " & bolt_brace_cal_pcs_tot & " [TotPcs*2 - duplcate/2]"

If size<30
	bolt_brace_gen_ideet = "DET3000" 'sideputki 28 
	bolt_crossarm_gen_ideet = "DET3001a|DET3001b" & "|" & bolt_brace_gen_ideet'sideputki 28 molemmissa ruuvit obs D/S/W Onkohan seinällä aina sama
Else
	bolt_brace_gen_ideet = "DET3002" 'sideputki 42.4 
	bolt_crossarm_gen_ideet = "DET3003a|DET3003b" & "|" & bolt_brace_gen_ideet 'sideputki 42.4 molemmissa ruuvit
End If

'Logger.Debug("Checking, " & bolt_brace_info & "  total amount bolt set: " & bolt_brace_cal_pcs & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
']

'[nurjahdusorsi
Laskuri0 = 0 : Laskuri1 = 0
For Each key1 In CrossarmHash.Keys
	wp1 = CrossarmHash(key1)
	' Get the coordinates of the first work point
	Dim coord1 As Point = wp1.Point
	' Inner loop to compare with every other work point
	For Each key2 In CrossarmHash.Keys
		' Avoid comparing a work point with itself
		If key1 <> key2 Then
			wp2 = CrossarmHash(key2)
			' Get the coordinates of the second work point
			Dim coord2 As Point = wp2.Point
			' Calculate the distance between the two work points
			distance = coord1.DistanceTo(coord2)
			' Check if the distance is within the threshold
			If distance <= threshold Then
				ResulttiTest += key1 & " vs " & key2 & " dist: " & distance
				'Logger.Debug("WorkPoint 1: " & key1 & vbLf & " and WorkPoint " & key2 & vbLf & "Distance: " & distance)
				Laskuri0 += 1
			Else
				'Logger.Debug("WorkPoint " & key1 & " and WorkPoint " & key2 & " are not near each other. Distance: " & distance)
				Laskuri1 += 1
			End If
		End If
	Next
Next

bolt_crossarm_cal_pcs_tot = CrossarmHash.Count / 2 * 3 - Laskuri0 / 2 - CounterCrossarm2 / 2 'oletuksena 3, mutta voi tietysti 2
'e_wall_buckling
Dim inputString As String = e_wall_buckling
Dim parts() As String = inputString.Split(New String() {"__"}, StringSplitOptions.None)
If parts.Length > 1 Then
    Dim subParts() As String = parts(1).Split("_"c)
    Dim result As String = subParts(0)
End If


bolt_crossarm_cal_pcs = bolt_brace_xtra_factor & "|" & bolt_brace_xtra_factor & "|" & bolt_crossarm_cal_pcs_tot - 2*bolt_brace_xtra_factor 'double/single
bolt_crossarm_info = "Total amount of crossarm [3/2 boltset]: " & CrossarmHash.Count / 2 & " [" & CounterCrossarm3 / 2 & "/" & CounterCrossarm2 / 2 & "]" & vbLf & "Duplicates boltsets [" & Laskuri0 / 2 & "] selected bolt set pcs: " & bolt_crossarm_cal_pcs_tot & vbLf & e_wall_buckling

']

'[ Tuuliside
Laskuri0 = 0 : Laskuri1 = 0
For Each key1 In WindBraceHash.Keys
	wp1 = WindBraceHash(key1)
	Dim coord1 As Point = wp1.Point
	For Each key2 In WindBraceHash.Keys
		If key1 <> key2 Then
			wp2 = WindBraceHash(key2)
			Dim coord2 As Point = wp2.Point
			distance = coord1.DistanceTo(coord2)
			If distance <= threshold Then
				ResulttiTest += key1 & " vs " & key2 & " dist: " & distance & vbLf
				CoordArvot += "myArrayList.Add({" & Round(wp1.Point.x * 10) & "," & Round(wp1.Point.y * 10) & "," & Round(wp1.Point.z * 10) & " })" & vbLf
				Laskuri0 += 1
			Else
				Laskuri1 += 1
			End If
		End If
	Next
Next
'Logger.Debug("ResulttiTest:" & ResulttiTest)

SaranaKoodit = e_wind_brace_wa_code_st & "-" & e_wind_brace_wa_code_end & "-" & e_wind_brace_code_st & "-" & e_wind_brace_code_end & "-" & e_wind_brace_code_end_r


If Right(SaranaKoodit, 1) = "-" Then
	bolt_windbrace_codes = Left(SaranaKoodit, Len(SaranaKoodit) -1)
Else
	bolt_windbrace_codes = SaranaKoodit
End If

If windbarcesize<45
	bolt_windbrace_gen_ideet = "DET382" ' koko 40 'miten nämä määräytyy onko eroa jos ykittäinen
Else
	bolt_windbrace_gen_ideet = "DET383" 'koko 50
End If

bolt_windbrace_cal_pcs = WindBraceHash.Count - Laskuri0 / 2
bolt_windbrace_info = "Total amount of wind braces : " & WindBraceHash.Count / 2 & vbLf & "Duplicates boltsets [" & Laskuri0 / 2 & "] selected detail pcs: " & Round(bolt_windbrace_cal_pcs)
']

'[ Seinän vinotuki
Laskuri0 = 0 : Laskuri1 = 0
For Each key1 In WallBraceHash.Keys
	wp1 = WallBraceHash(key1)
	Dim coord1 As Point = wp1.Point
	For Each key2 In WallBraceHash.Keys
		If key1 <> key2 Then
			wp2 = WallBraceHash(key2)
			Dim coord2 As Point = wp2.Point
			distance = coord1.DistanceTo(coord2)
			If distance <= threshold Then
				'ResulttiTest += key1 & " vs " & key2 & " dist: " & distance  & vbLf
				'CoordArvot += "myArrayList.Add({" & Round(wp1.Point.x * 10) & "," & Round(wp1.Point.y * 10) & "," & Round(wp1.Point.z * 10) & " })" & vbLf
				Laskuri0 += 1
			Else
				Laskuri1 += 1
			End If
		End If
	Next
Next

bolt_wallbrace_codes = e_wab_brace_code_st & "-" & e_wab_brace_code_end

If wallbracesize<45
	bolt_wallbrace_gen_ideet = "DET309" ' koko 40 'miten nämä määräytyy onko eroa jos yksittäinen?!
Else
	bolt_wallbrace_gen_ideet = "DET310" 'koko 50
End If

bolt_wallbrace_cal_pcs = WallBraceHash.Count - Laskuri0 / 2
bolt_wallbrace_info = "Total amount of Wall braces : " & WallBraceHash.Count / 2 & vbLf & "Duplicates boltsets [" & Laskuri0 / 2 & "] selected detail pcs: " & bolt_wallbrace_cal_pcs & vbLf & e_wab_brace_code_st & " - " & e_wab_brace_code_end
']

Dim XScodes As String() = GetHingeCodes()
Dim PLcodes As String() = GetPlateCodes()
Dim result1 As String = String.Join("-", XScodes)
Dim result2 As String = String.Join("-", PLcodes)
bolt_hinge_info = result1 & vbLf & result2
Dim lastXScode As String = XScodes(XScodes.Length - 1)
Dim lastPLcode As String = PLcodes(PLcodes.Length - 1)
LastSetXS = ReturnDetVsJoint(lastXScode)
LastSetPL = ReturnDetVsJoint(lastPLcode)

oSumOfConnectionParts = CalcHinges
Dim text1 As String = "" : Dim text2 As String = "" : Dim quantity As Integer

For Each item In oSumOfConnectionParts
	If e_ridge_width < 10 Then 'ei harjaa 
		If item.key = LastSetXS Or item.key = LastSetPL Then
			quantity = (item.Value * 2 - 1) * totalFramePcs 'viimeinen setti
		Else
			quantity = item.Value * 2 * totalFramePcs
		End If
	Else
		quantity = item.Value * 2 * totalFramePcs
	End If
	text1 &= quantity.ToString() & "|"
	text2 &= item.Key & "|"
Next
' Remove the trailing "|"
If text1.Length > 0 Then text1 = text1.Substring(0, text1.Length - 1)
If text2.Length > 0 Then text2 = text2.Substring(0, text2.Length - 1)

bolt_hinge_cal_pcs = text1
bolt_hinge_ideet = text2

End Sub

Private Function ReturnDetVsJoint(code As String)
	Dim xsCodeMapping As New Dictionary(Of String, String) From {
	{"XS02", "DET001" }, {"XS03", "DET002" }, {"XS04", "DET003" },
	{"XS05", "DET004" }, {"XS06", "DET005" }, {"XS09", "DET006" },
	{"XS10", "DET007" }, {"XS11", "DET008" }, {"XS12", "DET009" },
	{"XS13", "DET010" }
	}

	Dim detCode As String = Nothing
	' Handle XS codes
	If xsCodeMapping.ContainsKey(code) Then
		detCode = xsCodeMapping(code)
		' Handle LJ codes
	ElseIf code.StartsWith("LJ") Then
		Dim thickness As Double = Double.Parse(code.Split("-"c)(1).Split("x"c)(1))
		detCode = If (thickness < 13, "DET102", "DET103")
	End If
	Return detCode
End Function


Function GetHingeCodes() As String()
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments
	Dim roofLowerJoints = ParamsValuToArray("E=e_k_joint_code_lo_1", "d", oRefDocs, True)
	Dim roofUpperJoints = ParamsValuToArray("e_k_joint_code_u_1", "d", oRefDocs, True)
	If roofUpperJoints.Count > 0 Then
		roofLowerJoints.Add(roofUpperJoints(roofUpperJoints.Count - 1))
	End If
	' Convert ArrayList to String Array before returning
	Return roofLowerJoints.ToArray(GetType(String))
End Function
Function GetPlateCodes() As String()
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments
	Dim roofLowerPlates = ParamsValuToArray("E=e_k_joint_code_lo_2", "d", oRefDocs, True)
	Dim roofUpperPlates = ParamsValuToArray("e_k_joint_code_l_1", "d", oRefDocs, True)
	If roofUpperPlates.Count > 0 Then
		roofLowerPlates.Add(roofUpperPlates(roofUpperPlates.Count - 1))
	End If
	Return roofLowerPlates.ToArray(GetType(String))
End Function

Function CalcHinges()
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments
	Dim oSumOfConnectionParts As New Hashtable()

	' Hinge calulation
	Dim roofLowerJoints = ParamsValuToArray("E=e_k_joint_code_lo_1", "d", oRefDocs, True)
	Dim roofUpperJoints = ParamsValuToArray("e_k_joint_code_u_1", "d", oRefDocs, True)

	' Add last upper joint to lower joints (handling ridge connection)
	If roofUpperJoints.Count > 0 Then
		roofLowerJoints.Add(roofUpperJoints(roofUpperJoints.Count - 1))
	End If

	ProcessJointCodes(roofLowerJoints, oSumOfConnectionParts)

	' Plate connection codes
	Dim roofLowerPlates = ParamsValuToArray("E=e_k_joint_code_lo_2", "d", oRefDocs, True)
	Dim roofUpperPlates = ParamsValuToArray("e_k_joint_code_l_1", "d", oRefDocs, True)

	' Add last upper plate to lower plates
	If roofUpperPlates.Count > 0 Then
		roofLowerPlates.Add(roofUpperPlates(roofUpperPlates.Count - 1))
	End If

	ProcessJointCodes(roofLowerPlates, oSumOfConnectionParts)
	' Output final quantities
	Return oSumOfConnectionParts
End Function


Private Function ProcessJointCodes(codes As ArrayList, results As Hashtable) As Hashtable
	Dim xsCodeMapping As New Dictionary(Of String, String) From {
	{"XS02", "DET001" }, {"XS03", "DET002" }, {"XS04", "DET003" },
	{"XS05", "DET004" }, {"XS06", "DET005" }, {"XS09", "DET006" },
	{"XS10", "DET007" }, {"XS11", "DET008" }, {"XS12", "DET009" },
	{"XS13", "DET010" }
	}
	For Each code As String In codes
		Dim detCode As String = Nothing
		' Handle XS codes
		If xsCodeMapping.ContainsKey(code) Then
			detCode = xsCodeMapping(code)
			' Handle LJ codes
		ElseIf code.StartsWith("LJ") Then
			Dim thickness As Double = Double.Parse(code.Split("-"c)(1).Split("x"c)(1))
			detCode = If (thickness < 13, "DET102", "DET103")
		End If

		If detCode IsNot Nothing Then
			If results.ContainsKey(detCode) Then
				results(detCode) = CInt(results(detCode)) + 1
			Else
				results.Add(detCode, 1)
			End If
		End If
	Next
	Return results
End Function

Function ParamsValuToArray(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional ReturnArray As Boolean = False)
	Dim myArrayList As New ArrayList
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			If Left(oFilter, 2) = "E=" Then
				If Item.Name = Right(oFilter, Len(oFilter) -2) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			Else
				If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			End If
		Next
	Next
	If ReturnArray Then
		Return myArrayList
	End If
	'My.Computer.Clipboard.SetText(teksti)
End Function