Sub main()

oOccs_WB = GetBrowserNodeOccs("Wind braces")

For Each WBKey In oOccs_WB.keys

	If WBKey.contains("_w_") Then
		RotateOccToWCS(W<PERSON><PERSON><PERSON>, "X")
	Else
		RotateOccToWCS(<PERSON><PERSON><PERSON><PERSON>, "Z")
	End If
Next

End Sub

Function RotateOccToWCS(OccuName As String, Direction As String)
	Dim oAsm As AssemblyDocument
	Try
		oAsm = ThisApplication.ActiveDocument
	Catch
		Logger.Error("This rule must be run in an Assembly document.")
		Exit Function
	End Try
	
	Dim oCompDef As AssemblyComponentDefinition = oAsm.ComponentDefinition
	Dim oOccurrence As ComponentOccurrence = Nothing
	Try
		oOccurrence = oCompDef.Occurrences.ItemByName(OccuName)
	Catch
		Logger.Warn("Occurrence not found: '" & OccuName & "'")
		Exit Function ' Use Exit Sub in a Sub, Exit Function in a Function
	End Try

	' Get the current transformation matrix of the occurrence
	Dim oCurrentMatrix As Matrix = oOccurrence.Transformation
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry

	' --- Determine Target Assembly Axis ---
	Dim targetAssemblyAxisVector As Vector
	Dim NumDir As Integer
	Select Case Direction.ToUpper()
		Case "Y"
			NumDir = 2 ' Assembly Y Axis
		Case "Z"
			NumDir = 3 ' Assembly Z Axis
		Case Else '"X" or default
			NumDir = 1 ' Assembly X Axis
	End Select
	
	Try
		targetAssemblyAxisVector = oCompDef.WorkAxes.Item(NumDir).Line.Direction.AsVector
        targetAssemblyAxisVector.Normalize() ' Ensure it's a unit vector
	Catch ex As Exception
		Logger.Error("Could not get Assembly Work Axis " & Direction & ". Ensure Work Axes 1, 2, 3 (X, Y, Z) exist. Details: " & ex.Message)
		Exit Function
	End Try

	' --- Determine Current Occurrence Axis to Align (Using Z-axis as per original code) ---
	' Extract the Z-axis of the occurrence from its current transformation matrix
	Dim currentOccurrenceZVector As Vector = oTG.CreateVector(oCurrentMatrix.Cell(3, 1), oCurrentMatrix.Cell(3, 2), oCurrentMatrix.Cell(3, 3))
    currentOccurrenceZVector.Normalize() ' Ensure it's a unit vector

	' --- Calculate Rotation ---
	Dim angle As Double = currentOccurrenceZVector.AngleTo(targetAssemblyAxisVector)
	Dim rotationAxis As Vector
	Const TOLERANCE As Double = 0.0001 ' Tolerance for angle comparisons

	If angle < TOLERANCE Then
		' Already aligned, no rotation needed
		Logger.Info("Occurrence '" & OccuName & "' Z-axis is already aligned with Assembly " & Direction & "-axis.")
		Exit Function
	ElseIf Math.Abs(angle - Math.PI) < TOLERANCE Then
		' Vectors are anti-parallel (180 degrees apart)
		angle = Math.PI
		' Cross product is zero. Need to find an arbitrary axis perpendicular to Z vector.
        ' Try crossing with the global X axis, unless Z is parallel to it.
        Dim globalX As Vector = oTG.CreateVector(1, 0, 0)
        If Not currentOccurrenceZVector.IsParallelTo(globalX, TOLERANCE) Then
            rotationAxis = currentOccurrenceZVector.CrossProduct(globalX)
        Else 
            ' If parallel to global X, try global Y (must be non-parallel)
            Dim globalY As Vector = oTG.CreateVector(0, 1, 0)
            rotationAxis = currentOccurrenceZVector.CrossProduct(globalY)
        End If
        
        If rotationAxis.Length < TOLERANCE Then
             Logger.Error("Failed to calculate perpendicular axis for 180-degree rotation for '" & OccuName & "'.")
             Exit Function
        End If
        rotationAxis.Normalize() ' Make it a unit vector
	Else
		' General case: Calculate rotation axis using cross product
		rotationAxis = currentOccurrenceZVector.CrossProduct(targetAssemblyAxisVector)
        If rotationAxis.Length < TOLERANCE Then
             Logger.Warn("Rotation axis calculation resulted in near-zero length vector for '" & OccuName & "'. Vectors might be collinear despite angle check.")
             Exit Function ' Avoid division by zero during normalization
        End If
		rotationAxis.Normalize() ' Make it a unit vector
	End If

	' --- Apply Rotation ---
	' Get the origin point (translation component) of the occurrence
	Dim oOrigin As Point = oTG.CreatePoint(oCurrentMatrix.Cell(1, 4), oCurrentMatrix.Cell(2, 4), oCurrentMatrix.Cell(3, 4))

	' Create a *new* matrix representing only the rotation we want to apply
	Dim oRotationMatrix As Matrix = oTG.CreateMatrix()
	Call oRotationMatrix.SetToRotation(angle, rotationAxis, oOrigin) ' Rotate around the component's current origin

	' Create a copy of the current matrix to modify safely
    Dim oNewMatrix As Matrix = oCurrentMatrix.Copy()

    ' Pre-multiply the current matrix by the rotation matrix.
    ' This applies the rotation *before* the existing transform, effectively rotating in place.
	Call oNewMatrix.PreMultiplyBy(oRotationMatrix)

	' Apply the new combined transformation matrix to the occurrence
	oOccurrence.Transformation = oNewMatrix

	Logger.Info("Rotated '" & OccuName & "' to align Z-axis with Assembly " & Direction & "-axis.")
	
	' Optional: Update the assembly view
	' oAsm.Update()
	' ThisApplication.ActiveView.Update()
End Function





Function GetBrowserNodeOccs(targetFolderName As String)
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmCompDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	Dim TableHash As Hashtable = New Hashtable
	Dim targetFolderNode As BrowserNode = Nothing

	Try
		' Get the Model browser pane (Use "Model$" for potential localization issues if "Model" doesn't work)
		Dim oPane As BrowserPane = oAsmDoc.BrowserPanes.Item("Model")
		Dim oTopNode As BrowserNode = oPane.TopNode
		' Iterate through the nodes directly under the top node to find the folder
		Dim oNode As BrowserNode
		For Each oNode In oTopNode.BrowserNodes
			' Check if the node's NativeObject is a BrowserFolder
			' NativeObject is generally preferred over AssociatedObject for type checking
			If TypeOf oNode.NativeObject Is BrowserFolder Then
				' Cast the NativeObject to BrowserFolder to access its properties (like Name)
				Dim currentFolder As BrowserFolder = CType(oNode.NativeObject, BrowserFolder)

				' Check if the folder name matches
				If currentFolder.Name = targetFolderName Then
					targetFolderNode = oNode ' We found the BrowserNode representing our folder
					Exit For ' Stop searching once found
				End If
			End If
		Next
	Catch ex As Exception
		Logger.Debug("Error accessing browser pane or finding the folder node: " & ex.Message)
		Exit Function
	End Try

	' If the folder node was found, select all occurrences inside it
	If Not targetFolderNode Is Nothing Then
		'		Dim oSelectSet As SelectSet = oAsmDoc.SelectSet
		'		oSelectSet.Clear()
		Dim selectionCount As Integer = 0
		Try
			' Iterate through the child nodes OF THE FOLDER NODE
			Dim childNode As BrowserNode
			For Each childNode In targetFolderNode.BrowserNodes ' *** This is the correct way ***
				' Check if the child node's NativeObject is a ComponentOccurrence
				If TypeOf childNode.NativeObject Is ComponentOccurrence Then
					Dim oOcc As ComponentOccurrence = CType(childNode.NativeObject, ComponentOccurrence)
					TableHash.Add(oOcc.Name, oOcc)
					'					oSelectSet.Select(oOcc)
					selectionCount = selectionCount + 1
				End If
			Next
			If selectionCount > 0 Then
				Logger.Debug("Selected " & selectionCount & " occurrences in folder: " & targetFolderName)
			Else
				Logger.Debug("Folder '" & targetFolderName & "' found, but it contains no selectable component occurrences.")
			End If
		Catch ex As Exception
			Logger.Debug("Error processing folder contents or selecting occurrences: " & ex.Message)
		End Try
	Else
		Logger.Debug("Folder not found: " & targetFolderName)
	End If
	Return TableHash
End Function