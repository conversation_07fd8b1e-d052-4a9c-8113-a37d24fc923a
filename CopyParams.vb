Sub main()
Dim <PERSON>mentoParaLista As New ArrayList

KomentoParaLista.Add("Close dialog")
KomentoParaLista.Add("Copy Parameters from active document")
KomentoParaLista.Add("Paste Parameters to active document")
KomentoParaLista.Add("Clear list")

Try
	ParaList = SharedVariable("ParaList")
	KomentoParaLista.AddRange(ParaList)
Catch
End Try

Dim SelectedCommand As String = InputListBox("Select command option:", KomentoParaLista, KomentoParaLista(0), "Parameter hanling [" & ThisDoc.Document.FullDocumentName & "]")

If SelectedCommand = "Close dialog" Then
	Dim filteredText As String = String.Join(vbCrLf, SelectedCommand)
	My.Computer.Clipboard.SetText(filteredText)
ElseIf SelectedCommand = "Copy Parameters from active document" Then
	Params = GetParamsActiveDocByFilter("_PNum_calc")
	Main()
ElseIf SelectedCommand = "Paste Parameters to active document" Then
	PasteParamsActiveDoc(KomentoParaLista)
ElseIf SelectedCommand = "Clear list" Then
	SharedVariable.Remove("ParaList")
	Main()
End If

End Sub

Function GetParamsActiveDocByFilter(ParaFilter As String)
	Dim ParaList As New ArrayList
	Dim UserParams As Parameters'UserParameters
	UserParams = ThisApplication.ActiveEditDocument.ComponentDefinition.Parameters '.ModelParameters            
	ParaList.Add(ThisDoc.Document.FullDocumentName)
	For Each Item In UserParams
		oParaName = Item.Name
		If oParaName.contains(ParaFilter) Then
			If Item.Units = "mm" Then
				arvo = Item.Value * 10
			Else If Item.Units = "deg" Then
			arvo = Item.Value * 180 / PI
		Else
			arvo = Item.Value
		End If

		oText = oText & vbLf & Item.Name & " = " & arvo
		ParaList.Add(Item.Name & " = " & arvo)
		End If
	Next
	My.Computer.Clipboard.SetText(oText)
	SharedVariable("ParaList") = ParaList
	Return ParaList
End Function

Function PasteParamsActiveDoc(sourceParams As ArrayList)
	For i = 5 To sourceParams.Count - 1
		Try
			oVal = sourceParams(i)
			ParaKey = oVal.Split(New Char() {"="c })
			paramName = ParaKey(0).trim()
			ParamValue = ParaKey(1).trim()
			oText += paramName & " Old value: " & Parameter(paramName) & " ->" & ParamValue & vbLf
			Logger.Debug("Old value: " & Parameter(paramName) & " ->" & ParamValue)
			Parameter(paramName) = ParamValue
		Catch ex As Exception
			Logger.Debug("Parameter " & paramName & " does not exist in the target document.")
		End Try
	Next
	MessageBox.Show("These parameters copy to active document " & vbLf & oText, "Parameters")
End Function

