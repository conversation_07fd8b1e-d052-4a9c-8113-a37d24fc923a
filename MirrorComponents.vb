Sub main()
StartTime = Now
For Each oOcc As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences

	oCategory = iProperties.Value(oOcc.Name, "Summary", "category")
	If Left(oOcc.Name, 9) <> "Mirrored" Then
		If Len(oCategory) >2 And oCategory <> "ridgetube" Then
			MirrorComps(oOcc, "hall_cl")
		End If
	End If

Next
Logger.Debug("Mirror Comps  : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function MirrorComps(oCompOccur As ComponentOccurrence, PlaneName As String)
	Dim oAssyDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oCompDef As AssemblyComponentDefinition = oAssyDoc.ComponentDefinition
	' Specify the work plane to use for mirroring
	Dim oMirrorPlane As WorkPlane = oCompDef.WorkPlanes.Item(PlaneName)
	' Get the component to mirror
	'Dim oCompOccur As ComponentOccurrence = oCompDef.Occurrences.ItemByName(oMoveCompName)
	' Get the plane geometry from the work plane
	Dim oPlane As Plane = oMirrorPlane.Plane
	' Get the current transformation matrix of the component
	Dim T1 As Matrix = oCompOccur.Transformation
	' Extract position and rotation from the original transformation matrix

	Dim oPosition As Point = ThisApplication.TransientGeometry.CreatePoint(T1.Cell(1, 4), T1.Cell(2, 4), T1.Cell(3, 4))
	Dim oRotation As Matrix = T1
	oRotation.Cell(1, 4) = 0
	oRotation.Cell(2, 4) = 0
	oRotation.Cell(3, 4) = 0

	' Mirror the rotation part
	Dim oMirrorMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix()
	oMirrorMatrix.Cell(1, 1) = 1 - 2 * oPlane.Normal.X * oPlane.Normal.X
	oMirrorMatrix.Cell(2, 1) = -2 * oPlane.Normal.X * oPlane.Normal.Y
	oMirrorMatrix.Cell(3, 1) = -2 * oPlane.Normal.X * oPlane.Normal.Z
	oMirrorMatrix.Cell(1, 2) = -2 * oPlane.Normal.Y * oPlane.Normal.X
	oMirrorMatrix.Cell(2, 2) = 1 - 2 * oPlane.Normal.Y * oPlane.Normal.Y
	oMirrorMatrix.Cell(3, 2) = -2 * oPlane.Normal.Y * oPlane.Normal.Z
	oMirrorMatrix.Cell(1, 3) = -2 * oPlane.Normal.Z * oPlane.Normal.X
	oMirrorMatrix.Cell(2, 3) = -2 * oPlane.Normal.Z * oPlane.Normal.Y
	oMirrorMatrix.Cell(3, 3) = 1 - 2 * oPlane.Normal.Z * oPlane.Normal.Z


	oMirrorMatrix.PostMultiplyBy(oRotation)
	' Manually project the position onto the mirror plane

	Dim oNormalVector As Vector = oPlane.Normal.AsVector()
	Dim oRootPoint As Point = oPlane.RootPoint
	' Calculate the vector from the plane's root point to the original position
	Dim oVectorToPosition As Vector = ThisApplication.TransientGeometry.CreateVector(oPosition.X - oRootPoint.X, oPosition.Y - oRootPoint.Y, oPosition.Z - oRootPoint.Z)
	' Compute the distance along the normal vector (d)
	Dim d As Double = oNormalVector.DotProduct(oVectorToPosition)
	' Calculate the projection of the point onto the plane
	Dim oProjection As Point = ThisApplication.TransientGeometry.CreatePoint(oPosition.X - d * oNormalVector.X, oPosition.Y - d * oNormalVector.Y, oPosition.Z - d * oNormalVector.Z)
	' Compute the mirrored position
	Dim oMirroredPosition As Vector = ThisApplication.TransientGeometry.CreateVector(2 * oProjection.X - oPosition.X, 2 * oProjection.Y - oPosition.Y, 2 * oProjection.Z - oPosition.Z)
	' Construct the final transformation matrix
	Dim oFinalMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix()
	oFinalMatrix = oMirrorMatrix
	oFinalMatrix.Cell(1, 4) = oMirroredPosition.X
	oFinalMatrix.Cell(2, 4) = oMirroredPosition.Y
	oFinalMatrix.Cell(3, 4) = oMirroredPosition.Z

	Dim SoveliaFix As Matrix = ThisApplication.TransientGeometry.CreateMatrix()
	SoveliaFix.Cell(1, 1) = 1
	SoveliaFix.Cell(2, 1) = 0
	SoveliaFix.Cell(3, 1) = 0
	SoveliaFix.Cell(4, 1) = 0
	SoveliaFix.Cell(1, 2) = 0
	SoveliaFix.Cell(2, 2) = 1
	SoveliaFix.Cell(3, 2) = 0
	SoveliaFix.Cell(4, 2) = 0
	SoveliaFix.Cell(1, 3) = 0
	SoveliaFix.Cell(2, 3) = 0
	SoveliaFix.Cell(3, 3) = -1
	SoveliaFix.Cell(4, 3) = 0
	SoveliaFix.Cell(1, 4) = 0
	SoveliaFix.Cell(2, 4) = 0
	SoveliaFix.Cell(3, 4) = 0
	SoveliaFix.Cell(4, 4) = 1
	
	Try : oCategory = iProperties.Value(oCompOccur.Name, "Summary", "category") : Catch : oCategory = "" : End Try
	
If oCategory="purlin" Or oCategory="eavetube" Then
	'tarviiko tehdä mitään
Else
	oFinalMatrix.PostMultiplyBy(SoveliaFix)
End If
	

	Try
		oNewMirName = "Mirrored " & oCompOccur.Name
		oOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oNewMirName)
		Logger.Debug("File already exists :" & oNewMirName)
	Catch
		Dim oMirroredOccur As ComponentOccurrence = oCompDef.Occurrences.AddByComponentDefinition(oCompOccur.Definition, oFinalMatrix)
		Try
			oMirroredOccur.Grounded = True
		Catch
		End Try
		Try
			oMirroredOccur.Name = oNewMirName
		Catch
			Logger.Debug("Error renaming:" & oCompOccur.Name)
		End Try
	End Try

End Function

Function ShowMatrixTxT(oMatrix As Matrix)
	Dim i As Integer: teksti = ""
	For i = 1 To 3
		teksti += Format(oMatrix.Cell(i, 1), "0.0") & ", " & _
		Format(oMatrix.Cell(i, 2), "0.0") & ", " & _
		Format(oMatrix.Cell(i, 3), "0.0") & ", " & _
		Format(oMatrix.Cell(i, 4), "0.0") & " # "
	Next
	Return teksti
End Function
