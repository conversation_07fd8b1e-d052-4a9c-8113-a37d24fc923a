'Dim oDoc As PartDocument = ThisApplication.ActiveDocument
'Dim oDef As ComponentDefinition = oDoc.ComponentDefinition

Dim Profile_DefaultLines As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
Dim Cut_YellowLines As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
Dim Plate_GreenLines As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
Dim Hinge_redCircle As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection

Dim oDefs As New ArrayList

Dim oLeafOccs As ComponentOccurrencesEnumerator = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AllLeafOccurrences

For Each oOcc As ComponentOccurrence In oLeafOccs
	oVector = oOcc.Transformation.Translation
		Dim oMatrix As Matrix=oOcc.Transformation
	'oMatrix = oTG. CreateMatrix
	
	Try
		oDefs.Add(oOcc.Definition.Document.ComponentDefinition)
	Catch
	End Try
Next


For Each oVal As ComponentDefinition In oDefs
	Logger.Debug("DisplayName: " & oVal.Document.DisplayName)

	Dim oSketch As PlanarSketch = oVal.Sketches.Item(1)
	TotalPcs = oSketch.SketchLines.Count
	'

	For k = 1 To TotalPcs 'viivat
		'Try
		oLine = oSketch.SketchLines.Item(k)
		Try
			oOverrideColor = oLine.OverrideColor
			Sininen = oOverrideColor.Blue
			Punainen = oOverrideColor.Red
			Vihrea = oOverrideColor.Green
			Logger.Debug("Väri viiva RGB " & Punainen & "," & Vihrea & "," & Sininen)
		Catch
			Profile_DefaultLines.Add(oLine)
		End Try


		If Punainen = 255 And Vihrea = 255 And Sininen = 0 Then
			Cut_YellowLines.Add(oLine)
		End If

		If Punainen = 0 And Vihrea = 255 And Sininen = 0 Then
			Plate_GreenLines.Add(oLine)
		End If

		'		Catch
		'		End Try
	Next 'lines


	For l = 1 To oSketch.SketchCircles.Count
		oCircle = oSketch.SketchCircles.Item(l)
		Try
			Sininen = oCircle.OverrideColor.Blue
			Punainen = oCircle.OverrideColor.Red
			Vihrea = oCircle.OverrideColor.Green
		Catch
		End Try
		If Sininen = 0 And Vihrea = 0 And Punainen = 255 Then
			Hinge_redCircle.Add(oCircle)
		End If
	Next 'circles

Next 'def

'
Teksti = "pl" & vbLf

For Each oDline In Profile_DefaultLines
	Try : Start_2d_X = oDline.Geometry.StartPoint.X : Catch : End Try
	Try : Start_2d_Y = oDline.Geometry.StartPoint.Y : Catch : End Try
	Try : End_2d_X = oDline.Geometry.EndPoint.X : Catch : End Try
	Try : End_2d_Y = oDline.Geometry.EndPoint.Y : Catch : End Try

	Try : Start_3d_X = oDline.Geometry3d.StartPoint.X : Catch : End Try
	Try : Start_3d_Y = oDline.Geometry3d.StartPoint.Y : Catch : End Try
	Try : Start_3d_Z = oDline.Geometry3d.StartPoint.Z : Catch : End Try
	Try : End_3d_X = oDline.Geometry3d.EndPoint.X : Catch : End Try
	Try : End_3d_Y = oDline.Geometry3d.EndPoint.Y : Catch : End Try
	Try : End_3d_Z = oDline.Geometry3d.EndPoint.Z : Catch : End Try
	Teksti = Teksti & Start_3d_X & "," & Start_3d_Z & vbLf
	Teksti = Teksti & End_3d_X & "," & End_3d_Z & vbLf
	Logger.Debug(Start_3d_X & "," & Start_3d_Z)
	Logger.Debug(End_3d_X & "," & End_3d_Z)
	Try : Catch : End Try
Next

My.Computer.Clipboard.SetText(Teksti)
'
