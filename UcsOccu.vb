Imports System.IO
Imports System.Text
Sub Main()
StartTime = Now

oCommandOption = "getUCSdata"

If oCommandOption = "getUCSdata" Then
	UCSMatrices = GetallUCS({"joint", "wind_brace_" })
	SharedVariable("UCSMatrices") = UCSMatrices

	'AllModules = GetAllModules()
Else 'oCommandOption="import"

	'WriteUCSposition()
	ReadUCSdataPosition()
End If
Logger.Debug("UCS read : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function ReadUCSdataPosition()
	oType = Left(ThisDoc.FileName(False), 1)

	Dim oFilterUCSs As New ArrayList ': Dim ucsNameList As New ArrayList
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmUserCoordinateSystems As UserCoordinateSystems = oAsmDoc.ComponentDefinition.UserCoordinateSystems
	AllParamsDict = SharedVariable("AllParamsDict")

	e_k1_purlin_pcs = ParaValue("e_k1_purlin_pcs", AllParamsDict, Left(ThisDoc.FileName(False), 2)) 'lisää taitettu&seinälle tarkastus
	e_k1_brace_pcs = ParaValue("e_k1_brace_pcs", AllParamsDict, Left(ThisDoc.FileName(False), 2))


	Try
		UCSMatrices = SharedVariable("UCSMatrices") 'miten backup
	Catch
		Logger.Debug("Error exiting rule run UcsOccu -rule from main h -assembly before...")
		MessageBox.Show("Error exiting rule run UcsOccu -rule from main h -assembly before...", "Error") 'lisää avaus&säännön käynnistys
		openFileRunrule("*_h.iam", "UcsOccu")
		Exit Function
	End Try

	Try
		NumberListEdit = SharedVariable("NumberListEdit")
	Catch
		BUfile = ThisDoc.Path & "\data.csv"
		Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
		NumberListEdit = ImportCSVToDictionary(BUfile)
		SharedVariable("NumberListEdit") = NumberListEdit
	End Try

	Dim oUcs As UserCoordinateSystem : Dim oAsmUCS As UserCoordinateSystem
	Dim oUCSDefinition As UserCoordinateSystemDefinition = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems.CreateDefinition
	oActiveDocumentFullName = ThisDoc.PathAndFileName(True).ToUpper

	For Each oVal In NumberListEdit
		oValue = oVal.Value
		DetModelPath = oValue(3)
		If DetModelPath.ToUpper = oActiveDocumentFullName Then
			oKey = oVal.Key
			ListString = oValue(6)
			Exit For
		End If
	Next

	oValueSplit = ListString.Split(New Char() {"#"c })
	KeySplit = oKey.Split(New Char() {"/"c })
	ridgeTube = True 'tarkistus missä katossa


	For Each oUcsKey In UCSMatrices.keys
		oUcsKeySplit = oUcsKey.Split(New Char() {"#"c })
		oUcsVertailuArvo = oUcsKeySplit(1)
		'ucsNameList.add(oUcsVertailuArvo)

		If oUcsKeySplit(0) = KeySplit(1).replace("Mir", "") Then 'ns ylimääräiset verrattuna vakioon
			For Each oUCSinModule In oValueSplit
				If oUCSinModule = oUcsVertailuArvo Then 'testaa toimihan
					oFilterUCSs.Add(UCSMatrices(oUcsKey))
				End If 'ucs nimi vastaavuus
			Next


			For i = 1 To e_k1_purlin_pcs
				If oUcsVertailuArvo.contains("joint_purlin_") Then 'nämä aina plain -model obs lisää laskenta + k1/2
					If Right(oUcsVertailuArvo, 1) = i Then
						oFilterUCSs.Add(UCSMatrices(oUcsKey))
					End If
				End If
			Next

			If oType = "K" And ridgeTube = True Then 'miten harjan tapauksessa
				If oUcsVertailuArvo.contains("joint_ridge_tube_plate") Then
					oFilterUCSs.Add(UCSMatrices(oUcsKey))
				End If
			End If
			
			If oType = "S" Then
				TyyppikohtainenTarkastus = "joint_crossarm_s_"
				e_k1_brace_pcs = 1 'tähän laskenta!!
				If oUcsVertailuArvo.contains("joint_eave_tube") Then 'räytäs
					oFilterUCSs.Add(UCSMatrices(oUcsKey))
				End If

				If oUcsVertailuArvo.contains("joint_xt") Then
					oFilterUCSs.Add(UCSMatrices(oUcsKey))
				End If

			Else ' "K"
				TyyppikohtainenTarkastus = "joint_crossarm_k1_"
			End If

			For i = 1 To e_k1_brace_pcs
				If oUcsVertailuArvo.contains(TyyppikohtainenTarkastus) Then 'nämä aina plain -model obs lisää laskenta + k1/2
					If Right(oUcsVertailuArvo, 1) = i Then
						oFilterUCSs.Add(UCSMatrices(oUcsKey))
					End If
				End If
			Next
		End If 'moduulinimi
	Next


	For Each oInsertUCS In oFilterUCSs
		oUCSDefinition.Transformation = oInsertUCS.Transformation
		oUCSname = oInsertUCS.name
		Try
			oUCSDefinition = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems(oUCSname)
			oUCSDefinition.Transformation = oInsertUCS.Transformation
		Catch 'luodaan uusi
			Logger.Debug("Creating :" & oUCSname)
			oAsmUCS = oAsmUserCoordinateSystems.Add(oUCSDefinition)
			Try
				oAsmUCS.Name = oUCSname '& "_update"
			Catch
			End Try
		End Try
	Next
End Function




Function ParaValue(oParaName, oParaDict, Optional PartNameFilter = "")
	If Left(PartNameFilter, 1) = "K" Then
		PartNameFilter2 = "p" & Mid(PartNameFilter, 2, 1) & "k"
	ElseIf Left(PartNameFilter, 1) = "S" Then
		PartNameFilter2 = "s"
	End If
	Dim myArrayList As New ArrayList
	Dim duplicateList As New ArrayList
	Dim ParaKey() As String
	For Each oEntry As KeyValuePair(Of String, Object) In oParaDict
		ParaKey = oEntry.Key.Split(New Char() {"#"c })
		ParaKey0 = ParaKey(0) : ParaKey1 = ParaKey(1)
		If ParaKey0.Contains(oParaName) And ParaKey1.Contains(PartNameFilter2) Then
			myArrayList.Add(oEntry.Value)
			duplicateList.Add(ParaKey(1))
		End If
	Next
	Try
		arvo = myArrayList(0)
	Catch
		arvo = 0
	End Try
	Return arvo
End Function
Function GetallUCS(oFilters As String())
	Dim UcsHashtable As Hashtable = New Hashtable
	For Each doc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
		If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) Then
			Dim Partdoc As PartDocument = doc
			oDocName = doc.FullFileName
			For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
				oUCSname = oUserCoordinateSystem.Name
				For Each oFilter In oFilters
					If oUCSname.contains(oFilter) Then

						oPos = InStrRev(oDocName, "_", -1) + 1
						oPos2 = InStrRev(oDocName, ".ipt", -1)
						oPositioName = Mid(oDocName, oPos, oPos2 - oPos)

						oKey = oPositioName & "#" & oUCSname
						If Not UcsHashtable.ContainsKey(oKey) Then
							UcsHashtable.Add(oKey, oUserCoordinateSystem)
						End If

					End If
				Next
			Next
		End If
	Next
	Return UcsHashtable
End Function
Function GetAllModules()
	Dim AllModulesList As New ArrayList

	Dim numberOfObjects As Integer = e_roof_pcs
	Dim requiredObjects As New List(Of String)

	' Add pXk (from p1k to pNk) based on the number of objects
	For i = 1 To numberOfObjects
		requiredObjects.Add("p" & i & "k")
	Next

	' Add "s" as it's also required
	requiredObjects.Add("s")
	If e_ridge_width > 10 Then
		requiredObjects.Add("h")
	End If

	' Create a list of expected f values (f1 to f200)
	Dim fValues As New List(Of String)

	For i = 1 To totalFramePcs
		fValues.Add("f" & i)
	Next

	For Each fValue As String In fValues
		For Each obj As String In requiredObjects
			AllModulesList.Add(fValue & "/" & obj)
		Next
	Next
	Return AllModulesList

End Function
Function GetUcsByNames(UCSinfo As String)
	'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
	'testi = GetUcsByNames("ex1/UCS2")
	oData = UCSinfo.Split(New Char() {"/"c })
	If oData.Length = 3 Then
		Dim oName As String() = {oData(0), oData(1) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(2))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try

	ElseIf oData.Length = 2 Then
		Dim oName As String() = {oData(0) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try

		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(1))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try
	End If

	Return oUcs
End Function
Function WriteUCSposition()
	Dim oUserCoordinateSystems As UserCoordinateSystems = ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems
	Dim UCSMatrices As List(Of Matrix) = New List(Of Matrix)
	Dim ucsNameList As New ArrayList
	For Each oUCS As UserCoordinateSystem In oUserCoordinateSystems
		' Get the transformation matrix of the UCS
		Dim oMatrix As Matrix = oUCS.Transformation
		' Store the matrix in the list
		UCSMatrices.Add(oMatrix)
		ucsNameList.Add(oUCS.Name)
	Next
	SharedVariable("UCSMatrices") = UCSMatrices
	SharedVariable("ucsNameList") = ucsNameList
End Function
Function openFileRunrule(oFilter As String, oRuleName As String)
	Dim openFileDialog As New OpenFileDialog()
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	' Set the title of the dialog
	openFileDialog.Title = "Select an Assembly File [a] & run CalHeight rule"
	' Set the initial directory (change to your specific folder)
	openFileDialog.InitialDirectory = ThisDoc.Path
	' Set the filter to only show assembly files (.iam)
	openFileDialog.Filter = "Inventor Assembly Files (" & oFilter & ")|" & oFilter & ""

	' Show the dialog and check if the user selected a file
	If openFileDialog.ShowDialog() = DialogResult.OK Then
		' Get the selected file path
		Dim selectedFile As String = openFileDialog.FileName
		' You can load the selected file or perform other actions here
		' Example: Open the assembly in Inventor
		oDoc = ThisApplication.Documents.Open(selectedFile)
		Try
			Call iLogic.RunRule(oDoc, oRuleName)
		Catch
			Logger.Debug("Unable to run the rule")
		Finally
			oDoc = Nothing
		End Try
	End If
End Function
Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function

Function DeleteAssyUCS(ucsName As String)
	'	DeleteAssyUCS("joint_crossarm_k1_5")
	For Each ucs As UserCoordinateSystem In ThisApplication.ActiveDocument.ComponentDefinition.UserCoordinateSystems
		If ucs.Name = ucsName Then
			ucs.Delete()
			' Exit the loop once UCS is found and deleted
			Exit For
		End If
	Next
End Function