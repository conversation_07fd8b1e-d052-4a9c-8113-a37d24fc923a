Sub main()
Dim myArrayList As New ArrayList
myArrayList.Add("Change Part")
myArrayList.Add("Change Position")
myArrayList.Add("Delete")
myArrayList.Add("Recipe OUT")
myArrayList.Add("Recipe IN")

oEditingComp = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Pick an editing component")
oOccName = oEditingComp.name : oFullFileName = oEditingComp.ReferencedFileDescriptor.FullFileName
oConstraints = oEditingComp.Constraints

Try
	EkaConstraint = oConstraints(1)
	Logger.Debug("1 st Constraint:" & EkaConstraint.name)
	occ1 = EkaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
	Try : occ1_2 = "/#" & EkaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
Logger.Debug("OccurrenceOne 1:" & occ1 & occ1_2)

occ2 = EkaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
Try : occ2_2 = "/#" & EkaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)

Logger.Debug("EntityOne:" & EkaConstraint.EntityOne.name)
Logger.Debug("EntityTwo:" & EkaConstraint.EntityTwo.name)
Catch
	Logger.Debug("Error 1st constraint")
End Try

Try
	TokaConstraint = oConstraints(2)
	Logger.Debug("2 nd const:" & TokaConstraint.name)

occ1 = TokaConstraint.OccurrenceOne.OccurrencePath.Item(1).Name
	Try : occ1_2 = "/" & TokaConstraint.OccurrenceOne.OccurrencePath.Item(2).Name : Catch : End Try
Logger.Debug("OccurrenceOne 2:" & occ1 & occ1_2)

occ2 = TokaConstraint.OccurrenceTwo.OccurrencePath.Item(1).Name
koe=TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name
Try : occ2_2 = "/" & TokaConstraint.OccurrenceTwo.OccurrencePath.Item(2).Name : Catch : End Try
Logger.Debug("OccurrenceTwo 2:" & occ2 & occ2_2)

Logger.Debug("EntityOne:" & TokaConstraint.EntityOne.name)
Logger.Debug("EntityTwo:" & TokaConstraint.EntityTwo.name)
Catch
	Logger.Debug("Error 2nd constraint")
End Try

Try
	KolmasConstraint = oConstraints(3)
	Logger.Debug("2 nd const:" & KolmasConstraint.name)
	Logger.Debug("AffectedOccurrenceOne:" & KolmasConstraint.AffectedOccurrenceOne.name)
	Logger.Debug("AffectedOccurrenceTwo:" & KolmasConstraint.AffectedOccurrenceTwo.name)
	Logger.Debug("EntityOne:" & KolmasConstraint.EntityOne.name)
	Logger.Debug("EntityTwo:" & KolmasConstraint.EntityTwo.name)
Catch
	Logger.Debug("Error 3 rd constraint")
End Try

oCommand = InputListBox("Select one.", myArrayList, myArrayList.Item(0), "Manual Insertion", "Component")

If oCommand = "Delete" Then
	DeleteOccu(oOccName)
ElseIf oCommand = "Change Part" Then
	ReplaceOccu(oOccName)
ElseIf oCommand = "Change Position" Then

End If

End Sub




Function ReplaceOccu(Occuname As String)
	Try
		Component.InventorComponent(Occuname).Delete()
		Logger.Debug("Deleted " & Occuname)
	Catch
		Logger.Debug("Error deleting " & Occuname)
	End Try
End Function


Function DeleteOccu(Occuname As String)
	Try
		Component.InventorComponent(Occuname).Delete()
		Logger.Debug("Deleted " & Occuname)
	Catch
		Logger.Debug("Error deleting " & Occuname)
	End Try
End Function