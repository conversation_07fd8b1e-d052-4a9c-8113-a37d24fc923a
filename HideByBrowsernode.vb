Sub main()'HideByBrowsernode
StartTime = Now : oStep =1

Dim MyArrayList As New ArrayList
oProgressBarStep = 0 : oProgressBarSteps = 7 : Dim oMessage As String = "Hide/Show jointing components"
oProgressBar = ThisApplication.CreateProgressBar(False, oProgressBarSteps, oMessage)

If cui_jp_delete_selection Then
	i = MessageBox.Show("Delete? (applies to browser folder nodes)", "Joint part", MessageBoxButtons.YesNo)
	If i = vbYes Then

	Else
		cui_jp_delete_selection = False
	End If
End If


If cui_jp_boolean_purlin Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Purlins")
End If
If cui_jp_boolean_crossarm Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Crossarm")
End If
If cui_jp_boolean_brace Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Braces")
End If
If cui_jp_boolean_windbrace Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Wind braces")
End If
If cui_jp_boolean_wallbrace Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Wall braces")
End If
If cui_jp_boolean_eavetube Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Eave tubes")
End If
If cui_jp_boolean_ridgetube Or cui_jp_boolean_all = False Then
	MyArrayList.Add("Ridge tubes")
End If

For Each oVal In MyArrayList
	oProgressBar.Message = (oVal & " changing visibility of folder " & oStep & " of " & oProgressBarSteps & " Procesed ")
	oProgressBar.UpdateProgress
	oStep = oStep + 1
	SetVisibilityFolderContent(oVal, cui_jp_node_show_hide, cui_jp_delete_selection)
Next

oProgressBar.Close
Logger.Debug("Hide by Browser nodes : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub
Function SetVisibilityFolderContent(FolderName As String, Visibility As Boolean, Optional Delete As Boolean = False)
	On Error Resume Next
	oPane = ThisDoc.Document.BrowserPanes("Model")
	oFolder = oPane.TopNode.BrowserFolders.Item(FolderName)
	oFolderNodes = oFolder.BrowserNode.BrowserNodes

	Logger.Debug("Count of " & FolderName & ":" & oFolderNodes.Count)

	For Each oNode As BrowserNode In oFolderNodes
		oComp = oNode.NativeObject

		If Visibility = True Then
			oComp.Visible = True
		Else
			If Delete Then 'obs pitäisikö estää alkuperäisen tuhoaminen
				If Len(oComp.name) >3
					oComp.delete()
				End If
			Else
				oComp.Visible = False
			End If
		End If
	Next

End Function