Imports System.IO
Sub Main()
Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments

Try
	AllParamsDict = SharedVariable("AllParamsDict")
Catch 'SharedVariable.RemoveAll()
	i = MessageBox.Show("Delete?", "No saved shared data", MessageBoxButtons.YesNo)
	If i = vbYes Then
		AllParamsDict = ParamsDict("", "d", oRefDocs)

	End If
End Try
Dim filePath1 As String = "D:\temp\before_calculation.txt"
Dim filePath2 As String = "D:\temp\after_calculation.txt"

i = MessageBox.Show("Is this first time gen values?", "Compare values", MessageBoxButtons.YesNo)

If i = vbYes Then
	FirstRun = True
	SecondRun = False
	CompareValues = False
Else
	FirstRun = False
	SecondRun = True
	CompareValues = True
End If


If FirstRun Then
	SaveDictionaryToFile(AllParamsDict, filePath1)
End If

If SecondRun Then
	SaveDictionaryToFile(AllParamsDict, filePath2)
End If

If CompareValues Then
	Dim process As New System.Diagnostics.Process()
	process.StartInfo.FileName = "code"
	process.StartInfo.Arguments = "--diff """ & filePath1 & """ """ & filePath2 & """"
	process.Start()
End If
End Sub


Sub SaveDictionaryToFile(ByVal dictionary As Dictionary(Of String, Object), ByVal filePath As String)

	Try
		Using sw As New System.IO.StreamWriter(filePath)
			For Each kvp In dictionary
				Dim key As String = kvp.Key
				Dim value As String = ""
				' Check if the value is Nothing/Null before accessing its ToString method
				If kvp.Value IsNot Nothing Then
					value = kvp.Value.ToString() 'Use .ToString() to convert object to string, or format if needed
				Else
					value = "<NULL>" ' Or some other placeholder
				End If

				sw.WriteLine(key & " = " & value) ' Consistent delimiter for easy parsing.
			Next
		End Using
		MessageBox.Show("Dictionary saved to " & filePath, "Success")

	Catch ex As Exception
		MessageBox.Show("Error saving dictionary: " & ex.Message, "Error")
	End Try

End Sub

Function ParamsDict(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator)
	Dim oADoc As AssemblyDocument = TryCast(ThisDoc.Document, Inventor.AssemblyDocument)
	Dim oOccs As ComponentOccurrences = oADoc.ComponentDefinition.Occurrences
	Dim oDict As New Dictionary(Of String, Object)
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				ElseIf Item.Units = "deg" Then
					Arvo = Item.Value * 180 / PI
				Else
					Arvo = Item.Value
				End If
				Try
					oDict.Add(Item.Name & "#" & oRefDoc.displayname, Arvo)
				Catch
				End Try
			End If
		Next
	Next
	Return oDict
End Function