'FindRule - Enhanced with Edit capability
oDoc = ThisApplication.ActiveDocument
Dim myArrayList As New ArrayList
Dim myArrayListFiltered As New ArrayList
Auto = iLogicVb.Automation
Dim iLogicAuto As Object = Auto
Dim rules As Object = iLogicAuto.rules(oDoc)
i = 0
myArrayList.Add("Free text search")
For Each rule In rules
	i = i + 1
		Logger.Debug(rule.name & " pos: " & i)
	myArrayList.Add(rule.name)
Next
KomentoRule = InputListBox("Select one.", myArrayList, myArrayList.Item(0), "Search", "Rule")
If KomentoRule = "Free text search" Then
	Hakuehto = InputBox("Search Text", "Search", sPrefix)
	For Each oval In myArrayList
		If InStrRev(UCase(oval), UCase(Hakuehto)) <> 0 Then 'estää iSot Pienet vertailussa
			Logger.Debug(oval)
			myArrayListFiltered.Add(oval)
		End If
	Next
	If myArrayListFiltered.Count = 0 Then
		MessageBox.Show("Not found search string :" & Hakuehto & vbLf & "Check spelling..", "Error")
		GoTo 100
	End If
	myArrayListFiltered.Sort
	KomentoRule = InputListBox("Select one.", myArrayListFiltered, myArrayListFiltered.Item(0), "Search", "Rule")
End If

' Ask user what action to perform
If KomentoRule <> "Free text search" And KomentoRule <> "" Then
	Dim actionList As New ArrayList
	actionList.Add("Run Rule")
	actionList.Add("Edit Rule")
	actionList.Add("View Rule Content")

	Dim selectedAction As String = InputListBox("What would you like to do with rule: " & KomentoRule, actionList, actionList.Item(0), "Action", "Select Action")

	Select Case selectedAction
		Case "Run Rule"
			Try
				iLogicVb.RunRule(KomentoRule)
			Catch ex As Exception
				MessageBox.Show("Error running rule: " & ex.Message, "Error")
			End Try

		Case "Edit Rule"
			Try
				' Open rule in iLogic editor
				iLogicAuto.EditRule(oDoc, KomentoRule)
			Catch ex As Exception
				MessageBox.Show("Error opening rule for editing: " & ex.Message, "Error")
			End Try

		Case "View Rule Content"
			Try
				' Get rule content and display it
				Dim ruleObj As Object = iLogicAuto.GetRule(oDoc, KomentoRule)
				If ruleObj IsNot Nothing Then
					Dim ruleText As String = ruleObj.Text
					' Use the large message dialog if available, otherwise use MessageBox
					Try
						ShowLargeMessageDialog(ruleText, "Rule Content: " & KomentoRule, MessageBoxButtons.OK, MessageBoxIcon.Information)
					Catch
						MessageBox.Show(ruleText, "Rule Content: " & KomentoRule)
					End Try
				Else
					MessageBox.Show("Could not retrieve rule content.", "Error")
				End If
			Catch ex As Exception
				MessageBox.Show("Error viewing rule content: " & ex.Message, "Error")
			End Try
	End Select
End If

100 :
'virheenkorjus