'FindRule - Enhanced with Edit capability
oDoc = ThisApplication.ActiveDocument
Dim myArrayList As New ArrayList
Dim myArrayListFiltered As New ArrayList
Auto = iLogicVb.Automation
Dim iLogicAuto As Object = Auto
Dim rules As Object = iLogicAuto.rules(oDoc)
i = 0
myArrayList.Add("Free text search")
For Each rule In rules
	i = i + 1
		Logger.Debug(rule.name & " pos: " & i)
	myArrayList.Add(rule.name)
Next
KomentoRule = InputListBox("Select one.", myArrayList, myArrayList.Item(0), "Search", "Rule")
If KomentoRule = "Free text search" Then
	Hakuehto = InputBox("Search Text", "Search", sPrefix)
	For Each oval In myArrayList
		If InStrRev(UCase(oval), UCase(Hakuehto)) <> 0 Then 'estää iSot Pienet vertailussa
			Logger.Debug(oval)
			myArrayListFiltered.Add(oval)
		End If
	Next
	If myArrayListFiltered.Count = 0 Then
		MessageBox.Show("Not found search string :" & Hakuehto & vbLf & "Check spelling..", "Error")
		GoTo 100
	End If
	myArrayListFiltered.Sort
	KomentoRule = InputListBox("Select one.", myArrayListFiltered, myArrayListFiltered.Item(0), "Search", "Rule")
End If

' Ask user what action to perform
If KomentoRule <> "Free text search" And KomentoRule <> "" Then
	Dim actionList As New ArrayList
	actionList.Add("Run Rule")
	actionList.Add("Edit Rule")
	actionList.Add("View Rule Content")

	Dim selectedAction As String = InputListBox("What would you like to do with rule: " & KomentoRule, actionList, actionList.Item(0), "Action", "Select Action")

	Select Case selectedAction
		Case "Run Rule"
			Try
				iLogicVb.RunRule(KomentoRule)
			Catch ex As Exception
				MessageBox.Show("Error running rule: " & ex.Message, "Error")
			End Try

		Case "Edit Rule"
			Try
				' Method 1: Try using the iLogic command directly
				Try
					ThisApplication.CommandManager.ControlDefinitions.Item("iLogicEditRuleCmd").Execute()
				Catch
					' Method 2: Alternative approach - open iLogic browser and let user navigate
					Try
						ThisApplication.CommandManager.ControlDefinitions.Item("iLogicBrowserCmd").Execute()
						MessageBox.Show("iLogic Browser opened. Please navigate to rule: " & KomentoRule & " and double-click to edit.", "Edit Rule")
					Catch
						' Method 3: Show rule content for manual editing
						Dim ruleObj As Object = iLogicAuto.GetRule(oDoc, KomentoRule)
						If ruleObj IsNot Nothing Then
							Dim ruleText As String = ruleObj.Text
							Clipboard.SetText(ruleText)
							MessageBox.Show("Rule content copied to clipboard. You can paste it into a new rule or text editor." & vbLf & vbLf & "To edit the rule:" & vbLf & "1. Go to Manage > iLogic > Edit Rule" & vbLf & "2. Select rule: " & KomentoRule, "Edit Rule - Manual Method")
						Else
							MessageBox.Show("Could not access rule content.", "Error")
						End If
					End Try
				End Try
			Catch ex As Exception
				MessageBox.Show("Error opening rule for editing: " & ex.Message, "Error")
			End Try

		Case "View Rule Content"
			Try
				' Get rule content and display it
				Dim ruleObj As Object = iLogicAuto.GetRule(oDoc, KomentoRule)
				If ruleObj IsNot Nothing Then
					Dim ruleText As String = ruleObj.Text
					' Display rule content in a simple message box
					' For large content, consider copying to clipboard
					If ruleText.Length > 1000 Then
						Dim result As DialogResult = MessageBox.Show("Rule content is large (" & ruleText.Length & " characters)." & vbLf & vbLf & "Click Yes to copy to clipboard, No to show in message box.", "Large Rule Content", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
						If result = DialogResult.Yes Then
							Clipboard.SetText(ruleText)
							MessageBox.Show("Rule content copied to clipboard!", "Success")
						ElseIf result = DialogResult.No Then
							MessageBox.Show(ruleText, "Rule Content: " & KomentoRule)
						End If
					Else
						MessageBox.Show(ruleText, "Rule Content: " & KomentoRule)
					End If
				Else
					MessageBox.Show("Could not retrieve rule content.", "Error")
				End If
			Catch ex As Exception
				MessageBox.Show("Error viewing rule content: " & ex.Message, "Error")
			End Try
	End Select
End If

100 :
'virheenkorjus