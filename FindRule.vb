'FindRule
oDoc = ThisApplication.ActiveDocument
Dim myArrayList As New ArrayList
Dim myArrayListFiltered As New ArrayList
Auto = iLogicVb.Automation
Dim iLogicAuto As Object = Auto
Dim rules As Object = iLogicAuto.rules(oDoc)
i = 0
myArrayList.Add("Free text search")
For Each rule In rules
	i = i + 1
		Logger.Debug(rule.name & " pos: " & i)
	myArrayList.Add(rule.name)
Next
KomentoRule = InputListBox("Select one.", myArrayList, myArrayList.Item(0), "Search", "Rule")
If KomentoRule = "Free text search" Then
	Hakuehto = InputBox("Search Text", "Search", sPrefix)
	For Each oval In myArrayList
		If InStrRev(UCase(oval), UCase(Hakuehto)) <> 0 Then 'estää iSot Pienet vertailussa
			Logger.Debug(oval)
			myArrayListFiltered.Add(oval)
		End If
	Next
	If myArrayListFiltered.Count = 0 Then
		MessageBox.Show("Not found search string :" & Hakuehto & vbLf & "Check spelling..", "Error")
		GoTo 100
	End If
	myArrayListFiltered.Sort
	KomentoRule = InputListBox("Select one.", myArrayListFiltered, myArrayListFiltered.Item(0), "Search", "Rule")
End If
Try
	iLogicVb.RunRule(KomentoRule)

Catch
End Try
100 :
'virheenkorjus