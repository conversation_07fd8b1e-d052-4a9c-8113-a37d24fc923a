Sub Main 'oMatrix
	ShowSelectedoccTransMatrix(True)
	'TwoUcsTransmatrixBySelection()
	'MoveOccToOcc("kii<PERSON><PERSON>","siirto")
	'MirrosSelectedOcc()
	'RotateOccTransMatrix(ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select a occurence"), 90, "Z")
End Sub



Function TwoUcsTransmatrixBySelection()
	Dim oUCS1 As UserCoordinateSystem = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kUserCoordinateSystemFilter, "Select UCS1")
	Dim oUCS2 As UserCoordinateSystem = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kUserCoordinateSystemFilter, "Select UCS2")
	If oUCS1 Is Nothing Or oUCS2 Is Nothing Then Exit Function
	Dim oUCS1Transformation As Matrix = oUCS1.Transformation
	Dim oUCS2Transformation As Matrix = oUCS2.Transformation
	'ShowMatrix(oUCS1Transformation)
	'ShowMatrix(oUCS2Transformation)
	'
	If TypeOf (oUCS1) Is UserCoordinateSystemProxy Then oUCS1Transformation.TransformBy(oUCS1.ContainingOccurrence.Transformation)
	If TypeOf (oUCS2) Is UserCoordinateSystemProxy Then oUCS2Transformation.TransformBy(oUCS2.ContainingOccurrence.Transformation)
	Dim oOrigin1 As Point
	Dim oXAxis1 As Vector
	Dim oYAxis1 As Vector
	Dim oZAxis1 As Vector
	Dim oOrigin2 As Point
	Dim oXAxis2 As Vector
	Dim oYAxis2 As Vector
	Dim oZAxis2 As Vector
	oUCS1Transformation.GetCoordinateSystem(oOrigin1, oXAxis1, oYAxis1, oZAxis1)
	oUCS2Transformation.GetCoordinateSystem(oOrigin2, oXAxis2, oYAxis2, oZAxis2)
	ShowMatrix(oUCS1Transformation)
	ShowMatrix(oUCS2Transformation)

	Dim oMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix()

	oMatrix.SetToAlignCoordinateSystems(oOrigin1, oXAxis1, oYAxis1, oZAxis1, _
	oOrigin2, oXAxis2, oYAxis2, oZAxis2)

	ShowMatrix(oMatrix)

	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)
	Dim oReturn As String
	For i = 0 To 3
		oReturn = oReturn & Math.Round(cells(i), 4, MidpointRounding.AwayFromZero) & " ; "
	Next
	oReturn = oReturn & vbCrLf
	For i = 4 To 7
		oReturn = oReturn & Math.Round(cells(i), 4, MidpointRounding.AwayFromZero) & " ; "
	Next
	oReturn = oReturn & vbCrLf
	For i = 8 To 11
		oReturn = oReturn & Math.Round(cells(i), 4, MidpointRounding.AwayFromZero) & " ; "
	Next
	oReturn = oReturn & vbCrLf
	For i = 12 To 15
		oReturn = oReturn & Math.Round(cells(i), 4, MidpointRounding.AwayFromZero) & " ; "
	Next
	MsgBox(oReturn)
End Function
Function LkaTest()
	Dim testiPos = ThisAssembly.Geometry.Matrix(-0.037985671881094, 0.0139604851093202, -0.999180761217544, 20060.89509233817,
	-0.97313837288995, 0.226690640129087, 0.040162929262713, 29480.98611094504,
	0.227065620340989, 0.973866756046935, 0.00497448748957967, 2130.265793946933,
	0, 0, 0, 1)

	'Dim T2 As Matrix = testiPos.Invert
	T4 = ThisAssembly.Geometry.Matrix(-1, 0, 0, 0,
	0, 1, 0, 0,
	0, 0, 1, 0,
	0, 0, 0, 1)

	'Dim T3 As Matrix = 
	T4.PreMultiplyBy(testiPos)

	Dim testi = Components.Add("testi7", "C:\Vault_BH\Designs\Proj\5319-20\koe5_19\01\2024 14.18.11.ipt", position := T4)
End Function
Function MoveOccToOcc(oFixCompName As String, oMoveCompName As String, Optional oSelection As Boolean = False)
	Dim oAsm As AssemblyDocument = ThisDoc.Document
	Dim oFixComp As ComponentOccurrence
	Dim oMoveComp As ComponentOccurrence
	If oSelection = False Then
		Try
			oFixComp = oAsm.ComponentDefinition.Occurrences.ItemByName(oFixCompName)
		Catch
			Logger.Debug("Error getting oFixComp: " & oFixCompName)
			Exit Function
		End Try
		Try
			oMoveComp = oAsm.ComponentDefinition.Occurrences.ItemByName(oMoveCompName)
		Catch
			Logger.Debug("Error getting oMoveCompName: " & oMoveCompName)
			Exit Function
		End Try
	Else 'manuaalivalinta
		oFixComp = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select a Fix component")
		oMoveComp = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Moving component")
	End If
	Dim T1 As Matrix = GetUcsTransformationMatrix(oFixComp) 'ucs def 
	Dim T2 As Matrix = GetUcsTransformationMatrix(oMoveComp) : 'ShowMatrix(T1) : ShowMatrix(T2)
	Dim T As Matrix = oFixComp.Transformation.Copy ': ShowMatrix(T)
	T.PostMultiplyBy(T1) ': ShowMatrix(T) 'kertominen TxT1 excel =MMULT(A27:D30,A16:D19)
	T2.Invert()  ': ShowMatrix(T2) 'siirrettävä =MINVERSE(A21:D24)
	ShowMatrix(T) : ShowMatrix(T2)
	T.PostMultiplyBy(T2) : ShowMatrix(T)
	oMoveComp.Transformation = T
End Function
Function GetUcsTransformationMatrix(occ As ComponentOccurrence) As Matrix
	Dim transformationMatrix As Matrix
	Try
		transformationMatrix = occ.Definition.UserCoordinateSystems(1).Transformation.Copy
	Catch
		Logger.Debug("No ucs -definition in :" & occ.Name)
		Exit Function
	End Try
	Return transformationMatrix
End Function
Function ShowMatrix(oMatrix As Matrix)
	Dim i As Integer
	Logger.Debug(vbLf & "###")
	For i = 1 To 4
		Logger.Debug( _
		Format(oMatrix.Cell(i, 1), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 2), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 3), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 4), "0.00"))
	Next
End Function
Function ShowSelectedoccTransMatrix(Optional compoCreationCode As Boolean = False)
	Dim comps As ObjectCollection
	Dim comp As Object
	comps = ThisApplication.TransientObjects.CreateObjectCollection
	myComponent = ThisApplication.CommandManager.Pick(
	SelectionFilterEnum.kAssemblyOccurrenceFilter,
	"Select a component")
	oOccuName = myComponent.name
	If compoCreationCode Then
		creationText = "Dim " & oOccuName & "Pos = ThisAssembly.Geometry.Matrix("
	End If
	showText = oOccuName & iProperties.Value(oOccuName, "Project", "Part Number") & vbLf
	Logger.Debug(showText)

	Dim oMatrix As Matrix = myComponent.Transformation
	Dim i As Integer
	For i = 1 To 4
		Logger.Debug( _
		Format(oMatrix.Cell(i, 1), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 2), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 3), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 4), "0.00"))

		showText += Format(oMatrix.Cell(i, 1), "0.00") & ", "
		showText += Format(oMatrix.Cell(i, 2), "0.00") & ", "
		showText += Format(oMatrix.Cell(i, 3), "0.00") & ", "
		showText += Format(oMatrix.Cell(i, 4), "0.00") & ", " & vbLf
		If compoCreationCode Then
			If i = 4 Then
				loppu = ")" & vbLf
			Else
				loppu = ", " & vbLf
			End If
			creationText += oMatrix.Cell(i, 1) & ", " & oMatrix.Cell(i, 2) & ", " & oMatrix.Cell(i, 3) & ", " & oMatrix.Cell(i, 4) & loppu

		End If
	Next
	If compoCreationCode Then
		creationText += "Dim " & oOccuName & " = Components.Add(""" & oOccuName & """, """ & myComponent.ReferencedFileDescriptor.FullFileName & """, position := " & oOccuName & "Pos)"
		My.Computer.Clipboard.SetText(creationText)
	Else
		My.Computer.Clipboard.SetText(showText)
	End If
End Function
Function MirrosSelectedOcc()
	oOcc = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select a occurence")
	Dim oMatrixAlku As Matrix = oOcc.Transformation
	ShowMatrix(oMatrixAlku)
	oFName = oOcc.ReferencedFileDescriptor.FullFileName
	oNewOccName = oOcc.name & "_mir"
	'Dim Comp_mir = Components.Add(oNewOccName, oFName)
	'	Dim oNewOcc As ComponentOccurrence = CType(ManagedComponentOccurrence, Comp_mir)
	'ShowSelectedoccTransMatrix(Comp_mir)
	Dim oTransMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix
	'	oTransMatrix.SetToRotateTo
	
End Function
Function FixRotation(oSearch As String)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		If Not TypeOf oOccurrence.Definition Is VirtualComponentDefinition And Not TypeOf oOccurrence.Definition Is WeldsComponentDefinition Then
			FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			If FullFileName.contains(oSearch) Then : 'Logger.Debug(FullFileName)
				YakselinX = GetOccTransMatrix(oOccurrence.Name, {1, 2 })
				If YakselinX>0 Then
					Logger.Debug("rotation comp :" & oOccurrence.Name & "y:" & YakselinX)
					RotateOccTransMatrix(oOccurrence.Name, 180, "Z")
				End If
			End If
		End If
	Next
End Function
Function RotateOccTransMatrix(oComp As ComponentOccurrence, Kulma As Double, akseli As String)
	'Dim oComp As ComponentOccurrence = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccuName)
	Dim Pos As Matrix = oComp.Transformation
	oX = Pos.Cell(1, 4)
	oY = Pos.Cell(2, 4)
	oZ = Pos.Cell(3, 4)
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	If akseli = "X" Then
		oXX = 1
	ElseIf akseli = "Y" Then
		oYY = 1
	Else
		oZZ = 1
	End If
	Dim oTempMatrix As Matrix = oTG.CreateMatrix
	oTempMatrix.SetToRotation(Kulma * PI / 180, oTG.CreateVector(oXX, oYY, oZZ), oTG.CreatePoint(oX, oY, oZ))
	Dim oTransMatrix As Matrix = oTG.CreateMatrix
	oTransMatrix.TransformBy(oTempMatrix)
	Pos.TransformBy(oTransMatrix)
	oComp.Transformation = Pos
End Function
Function GetOccTransMatrix(oOccuName As String, Optional oReturnSolu As String() = Nothing, Optional Debuggaus As Boolean = False)
	Dim oComp1 As ComponentOccurrence
	Try
		oComp = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccuName)
	Catch
		Logger.Debug("GetOccTransMatrix: not occ :" & oOccuName)
		Exit Function
	End Try
	Dim oMatrix As Matrix = oComp.Transformation
	Dim i As Integer
	'	If Debuggaus Then
	'		For i = 1 To 4
	'			Logger.Debug( _
	'			Format(oMatrix.Cell(i, 1), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 2), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 3), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 4), "0.00"))
	'		Next
	'	End If
	If Not oReturnSolu Is Nothing Then
		Return oMatrix.Cell(oReturnSolu(0), oReturnSolu(1))
	End If
End Function
Function SavePlaceFilu(SrcFilu As String, NewFilu As String, occName As String)
	oSrcPath = "C:\Vault_BH\Designs\Src\" : oDoc = ThisApplication.Documents.Open(oSrcPath & SrcFilu, False)
	oNewname = ThisDoc.Path & "\" & NewFilu & ".ipt"
	If IO.File.Exists(oNewname) Then
		Logger.Debug("File already exists : " & oNewname)
	Else
		Logger.Debug("New part File being created : " & oNewname)
		oDoc.SaveAs(oNewname, True)
	End If
	Components.Add(occName, oNewname)
End Function
Function ShowAnglesOcc()
	Dim aDoc As AssemblyDocument = TryCast(ThisApplication.ActiveDocument, AssemblyDocument)
	If IsNothing(aDoc) Then Logger.Debug("Not Run In Assembly Document") : Exit Sub

	'Dim oOcc As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(FORM_Object_Target)
	Dim oOcc As ComponentOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Occurrence")
	If IsNothing(oOcc) Then Exit Sub ' If nothing gets selected then we're done

	Dim AssemblyYZplane As WorkPlane = aDoc.ComponentDefinition.WorkPlanes.Item(1)	'YZ Plane is Item 1
	Dim AssemblyXZplane As WorkPlane = aDoc.ComponentDefinition.WorkPlanes.Item(2)
	Dim AssemblyXYplane As WorkPlane = aDoc.ComponentDefinition.WorkPlanes.Item(3)

	Dim componentMatrix As Inventor.Matrix = oOcc.Transformation
	Dim componentXaxis As Vector = ThisApplication.TransientGeometry.CreateVector(componentMatrix.Cell(1, 1), componentMatrix.Cell(2, 1), componentMatrix.Cell(3, 1))
	Dim componentYaxis As Vector = ThisApplication.TransientGeometry.CreateVector(componentMatrix.Cell(1, 2), componentMatrix.Cell(2, 2), componentMatrix.Cell(3, 2))
	Dim componentZaxis As Vector = ThisApplication.TransientGeometry.CreateVector(componentMatrix.Cell(1, 3), componentMatrix.Cell(2, 3), componentMatrix.Cell(3, 3))
	Dim componentOrigin As Point = ThisApplication.TransientGeometry.CreatePoint(componentMatrix.Cell(1, 4), componentMatrix.Cell(2, 4), componentMatrix.Cell(3, 4))

	Dim oAngle_YZ_x As Double = AssemblyYZplane.Plane.Normal.AngleTo(componentXaxis.AsUnitVector)
	Dim oAngle_XZ_x As Double = AssemblyXZplane.Plane.Normal.AngleTo(componentXaxis.AsUnitVector)
	Dim oAngle_XY_x As Double = AssemblyXYplane.Plane.Normal.AngleTo(componentXaxis.AsUnitVector)

	Dim oAngle_YZ_y As Double = AssemblyYZplane.Plane.Normal.AngleTo(componentYaxis.AsUnitVector)
	Dim oAngle_XZ_y As Double = AssemblyXZplane.Plane.Normal.AngleTo(componentYaxis.AsUnitVector)
	Dim oAngle_XY_y As Double = AssemblyXYplane.Plane.Normal.AngleTo(componentYaxis.AsUnitVector)

	Dim oAngle_YZ_z As Double = AssemblyYZplane.Plane.Normal.AngleTo(componentZaxis.AsUnitVector)
	Dim oAngle_XZ_z As Double = AssemblyXZplane.Plane.Normal.AngleTo(componentZaxis.AsUnitVector)
	Dim oAngle_XY_z As Double = AssemblyXYplane.Plane.Normal.AngleTo(componentZaxis.AsUnitVector)

	Logger.Debug("oAngle_YZ_x: " & Round(oAngle_YZ_x / PI * 180, 1) & " between assembly plane: " & AssemblyYZplane.Name & " and the X axis of the component: ")
	Logger.Debug("oAngle_XZ_x: " & Round(oAngle_XZ_x / PI * 180, 1) & " between assembly plane: " & AssemblyXZplane.Name & " and the X axis of the component: ")
	Logger.Debug("oAngle_XY_x: " & Round(oAngle_XY_x / PI * 180, 1) & " between assembly plane: " & AssemblyXYplane.Name & " and the X axis of the component: ")

	Logger.Debug("oAngle_YZ_y: " & Round(oAngle_YZ_y / PI * 180, 1) & " between assembly plane: " & AssemblyYZplane.Name & " and the Y axis of the component: ")
	Logger.Debug("oAngle_XZ_y: " & Round(oAngle_XZ_y / PI * 180, 1) & " between assembly plane: " & AssemblyXZplane.Name & " and the Y axis of the component: ")
	Logger.Debug("oAngle_XY_y: " & Round(oAngle_XY_y / PI * 180, 1) & " between assembly plane: " & AssemblyXYplane.Name & " and the Y axis of the component: ")

	Logger.Debug("oAngle_YZ_z: " & Round(oAngle_YZ_z / PI * 180, 1) & " between assembly plane: " & AssemblyYZplane.Name & " and the Z axis of the component: ")
	Logger.Debug("oAngle_XZ_z: " & Round(oAngle_XZ_z / PI * 180, 1) & " between assembly plane: " & AssemblyXZplane.Name & " and the Z axis of the component: ")
	Logger.Debug("oAngle_XY_z: " & Round(oAngle_XY_z / PI * 180, 1) & " between assembly plane: " & AssemblyXYplane.Name & " and the Z axis of the component: ")
End Function