Sub Main
	Dim oList As New List(Of String) From {"Generate", "Make Pdf", "Close Documents" }
	Dim Selected As IEnumerable(Of Object) = MultiSelectListBox("Select Some", oList, Nothing, "Multi-Select InputListBox", "My List")
	Dim PDFAddIn As TranslatorAddIn
	Dim oContext As TranslationContext
	Dim oOptions As NameValueMap
	Dim oDataMedium As DataMedium
	Call ConfigurePDFAddinSettings(PDFAddIn, oContext, oOptions, oDataMedium)

	Dim oFileDlg As Inventor.FileDialog = Nothing
	InventorVb.Application.CreateFileDialog(oFileDlg)
	oFileDlg.Filter = "Inventor Files (*.idw)|*.idw"
	oFileDlg.DialogTitle = "Delete Ilogic Rule in drawing"
	oFileDlg.InitialDirectory = ThisDoc.Path
	oFileDlg.MultiSelectEnabled = True
	oFileDlg.FilterIndex = 1
	oFileDlg.CancelError = True
	On Error Resume Next
	oFileDlg.ShowOpen()
	If Err.Number <> 0 Then
		MessageBox.Show("File not chosen.", "Dialog Cancellation")
	ElseIf oFileDlg.FileName <> "" Then
		For Each wrd In oFileDlg.FileName.Split("|") 'Words
			Dim odoc As Document = ThisApplication.Documents.Open(wrd, True) 'Vaihda False niin ei tee updatea joka kuvassa
			On Error Resume Next


			If Selected.Contains("Generate") Then
				RunruleDoc(odoc, "InitialReset")
			End If
			
			If Selected.Contains("Make Pdf") Then
				oDataMedium.FileName = Left(odoc.FullFileName, (InStrRev(odoc.FullFileName, ".", -1, vbTextCompare) -1)) & ".pdf"
				Call PDFAddIn.SaveCopyAs(odoc, oContext, oOptions, oDataMedium)
			End If

			If Selected.Contains("Close Documents") Then
				odoc.Close
			End If
		Next
	End If
End Sub

Function MultiSelectListBox(Optional Instructions As String = vbNullString, Optional Items As IEnumerable = Nothing,
	Optional DefaultValue As Object = Nothing, Optional Title As String = vbNullString, Optional ListName As String = vbNullString) As IEnumerable(Of Object)
	Using oILBD As New Autodesk.iLogic.Runtime.InputListBoxDialog(Title, ListName, Instructions, Items, DefaultValue)
		Dim oLB As System.Windows.Forms.ListBox = oILBD.Controls.Item(0).Controls.Item(2)
		oLB.SelectionMode = System.Windows.Forms.SelectionMode.MultiSimple
		Dim oDlgResult As System.Windows.Forms.DialogResult = oILBD.ShowDialog()
		Dim oSelected As IEnumerable(Of Object) = oLB.SelectedItems.Cast(Of Object)
		Return oSelected
	End Using
End Function
Sub ConfigurePDFAddinSettings(ByRef PDFAddIn As TranslatorAddIn, ByRef oContext As TranslationContext, ByRef oOptions As NameValueMap, ByRef oDataMedium As DataMedium)
	oPath = ThisDoc.Path
	PDFAddIn = ThisApplication.ApplicationAddIns.ItemById("{0AC6FD96-2F4D-42CE-8BE0-8AEA580399E4}")
	oContext = ThisApplication.TransientObjects.CreateTranslationContext
	oContext.Type = IOMechanismEnum.kFileBrowseIOMechanism
	oOptions = ThisApplication.TransientObjects.CreateNameValueMap
	oOptions.Value("All_Color_AS_Black") = 0
	oOptions.Value("Remove_Line_Weights") = 0
	oOptions.Value("Vector_Resolution") = 400
	oOptions.Value("Sheet_Range") = Inventor.PrintRangeEnum.kPrintAllSheets
	'oOptions.Value("Custom_Begin_Sheet") = 1
	'oOptions.Value("Custom_End_Sheet") = 1
	oDataMedium = ThisApplication.TransientObjects.CreateDataMedium
End Sub
Function RunruleDoc(odoc As Document, oRuleName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Try
		Call iLogic.RunRule(odoc, oRuleName)
	Catch ex As Exception
		Logger.Debug("Error in Opening :" & vbLf & ex.Message)
	Finally
		oDoc = Nothing
	End Try
End Function