Sub main()
StartTime = Now

oAllOccur = GetAllOccurances(debugMode :=False)
AllF1s = FilterOccsByName("f", "iam", 2, oAllOccur)
AllLSPlates = FilterOccsByName("LS02", "ipt", 1, oAllOccur)

'PrintAllOccuData(AllLSPlates)

Dim DemoteList As New ArrayList : Dim AssyOccList As New ArrayList : Dim PlateOccList As New ArrayList
DemoteList.Add("ACCEPT ALL")

For Each PL_occ In AllLSPlates
	ClosestOcc = FindClosestOccurrences(PL_occ, AllF1s)
	DemoteList.Add(PL_occ.name & " vs " & ClosestOcc.Name)
	AssyOccList.Add(ClosestOcc)
	PlateOccList.Add(PL_occ)
Next

DemoteList.Add("Manual demote")
selectedItem = InputListBox("Accept all for proceed demote: ", DemoteList, DemoteList(0), "Demote Data Viewer", "Plate Component vs Detailed assembly")

If selectedItem = "Manual demote" Then
	ManualDemote()
ElseIf selectedItem = "ACCEPT ALL" Then
	For i = 0 To PlateOccList.Count - 1
		'Logger.Debug(Aocc1.name & " in " & Mocc1.name)
		DemoteOcc(AssyOccList(i), PlateOccList(i))
	Next
End If
Logger.Debug("Demote command : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function FindClosestOccurrences(occ1 As ComponentOccurrence, set2 As ObjectCollection)
	Dim minDistance As Double = Double.MaxValue
	Dim closestOcc1 As ComponentOccurrence = Nothing
	Dim closestOcc2 As ComponentOccurrence = Nothing

	oOcc1CenX = (occ1.RangeBox.MinPoint.X + occ1.RangeBox.MaxPoint.X) / 2

	For Each occ2 As ComponentOccurrence In set2

		oOcc2MinCenX = occ2.RangeBox.MinPoint.X
		oOcc2MaxCenX = occ2.RangeBox.MaxPoint.X

		If oOcc1CenX>oOcc2MinCenX And oOcc1CenX<oOcc2MaxCenX Then

			Dim dist As Double = GetMinimumDistanceByOcc(occ1, occ2)
			If dist < minDistance Then
				minDistance = dist
				closestOcc1 = occ1
				closestOcc2 = occ2
			End If
		End If
	Next

	If closestOcc1 IsNot Nothing And closestOcc2 IsNot Nothing Then
		Return closestOcc2
	End If
End Function


Function ManualDemote()
	oMoveOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Moved occurrence")
	Dim oLeafOccurrence As ComponentOccurrence
	Dim oTargetSubAssembly As ComponentOccurrence
	oLeafOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyLeafOccurrenceFilter, "Select target assembly (leaf 2 up)")
	If oLeafOccurrence Is Nothing Then
		MsgBox("Selection was canceled or invalid.")
		Exit Function
	End If
	' Step 2: Traverse up the assembly hierarchy
	Dim targetLevel As Integer = 2 ' Adjust this to the desired level (e.g., 2 means parent of the parent, etc.)
	oTargetSubAssembly = GetParentOccurrenceAtLevel(oLeafOccurrence, targetLevel)

	If oTargetSubAssembly Is Nothing Or oMoveOccurrence Is Nothing Then
		MsgBox("No subassembly found at the desired level.")
	Else
		Logger.Debug(oMoveOccurrence.name & " -> " & oTargetSubAssembly.Name)
	End If
	DemoteOcc(oTargetSubAssembly, oMoveOccurrence)

End Function


Function DemoteOcc(oSubAssyOcc As ComponentOccurrence, oMoveOcc As ComponentOccurrence)
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	If oSubAssyOcc Is Nothing Or oMoveOcc Is Nothing Then
		Logger.Debug("Error!! not found Occurence " & Kohde & " or " & SiirtoOcc)
		Exit Function
	End If
	' Get the model browser
	Dim oPane As BrowserPane = oDoc.BrowserPanes.Item("Model")
	Dim oSourceNode As BrowserNode = oPane.GetBrowserNodeFromObject(oMoveOcc)
	Dim oSubAssyNode As BrowserNode = oPane.GetBrowserNodeFromObject(oSubAssyOcc)
	Dim oTargetNode As BrowserNode
	Dim i As Long
	For i = oSubAssyNode.BrowserNodes.Count To 1 Step -1
		oTargetNode = oSubAssyNode.BrowserNodes.Item(i)
		oTargetNodeFN = oTargetNode.FullPath
		If oTargetNodeFN.Contains("Origin") Then
			oTargetNode = oSubAssyNode.BrowserNodes.Item(i)
			Logger.Debug("oTargetNodeFN" & oTargetNodeFN)
			Exit For
		End If
	Next
	Logger.Debug(oSourceNode.FullPath & " -> " & oTargetNode.FullPath)
	' Demote the occurrence
	Call oPane.Reorder(oTargetNode, False, oSourceNode)
End Function




Function FilterOccsByName(filter1 As String, Type As String, Level As Integer, oAllOccur As ObjectCollection) As ObjectCollection
	Dim FilterOccs As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection

	' Pre-compile the filter string for faster comparison
	filter1 = filter1.ToLower()

	' Use For loop instead of For Each for better performance
	For i As Integer = 1 To oAllOccur.Count
		Dim oOcc As ComponentOccurrence = oAllOccur(i)

		' Early exit if level doesn't match
		If oOcc.OccurrencePath.Count <> Level Then Continue For

		If Type = "iam" Then
			If oOcc.DefinitionDocumentType <> kAssemblyDocumentObject Then Continue For
		Else
			If oOcc.DefinitionDocumentType <> kPartDocumentObject Then Continue For
		End If

		Dim pathName As String = oOcc.OccurrencePath.Item(1).Name.ToLower()
		If pathName.Contains(filter1) Then
			FilterOccs.Add(oOcc)
		End If
	Next

	Return FilterOccs
End Function

Function GetAllOccurances(Optional debugMode As Boolean = False) As ObjectCollection
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection

	' Pre-allocate collection size if possible
	Dim occurrences As ComponentOccurrences = oDoc.ComponentDefinition.Occurrences

	' Process in batches for better performance
	Const BATCH_SIZE As Integer = 100
	Dim batch As New List(Of ComponentOccurrence)(BATCH_SIZE)

	For i As Integer = 1 To occurrences.Count
		Dim oOcc As ComponentOccurrence = occurrences(i)
		batch.Add(oOcc)

		If batch.Count = BATCH_SIZE OrElse i = occurrences.Count Then
			' Process batch
			For Each batchOcc In batch
				ProcessOccurrence(batchOcc, oAllOccur, debugMode)
			Next
			batch.Clear()
		End If
	Next
	Return oAllOccur
End Function

Sub ProcessOccurrence(ByVal oOcc As ComponentOccurrence, ByRef oAllOccur As ObjectCollection, Optional debugMode As Boolean = False)
	Try
		' Combine checks to reduce branching
		If oOcc.Definition Is Nothing OrElse
			TypeOf oOcc.Definition Is VirtualComponentDefinition OrElse
			TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			Exit Sub
		End If
		' Add the occurrence
		oAllOccur.Add(oOcc)

		' Only log if debug mode is on
		If debugMode Then Logger.Debug("Added occurrence: " & oOcc.Name)

		' Process sub-occurrences using For loop instead of For Each
		Dim subOccurrences As ComponentOccurrences = oOcc.SubOccurrences
		For i As Integer = 1 To subOccurrences.Count
			ProcessOccurrence(subOccurrences(i), oAllOccur, debugMode)
		Next
	Catch ex As Exception
		If debugMode Then Logger.Error("Error processing occurrence '" & oOcc.Name & "': " & ex.Message)
	End Try
End Sub



Function GetMinimumDistanceByOcc(oComp1 As ComponentOccurrence, oComp2 As ComponentOccurrence)
	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = Round(pos1.X - pos2.X, 1)
		Dim deltaY As Double = Round(pos1.Y - pos2.Y, 1)
		Dim deltaZ As Double = Round(pos1.Z - pos2.Z, 1)
		Dim distance As Double = Round(Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ), 1)
		' Print or use the distance value as needed
		Return distance
	End If
End Function

Function GetParentOccurrenceAtLevel(ByVal leafOccurrence As ComponentOccurrence, ByVal levelUp As Integer) As ComponentOccurrence
	Dim currentOccurrence As ComponentOccurrence = leafOccurrence
	' Traverse upward
	For i As Integer = 1 To levelUp
		If currentOccurrence.ParentOccurrence Is Nothing Then
			' We've reached the top-level assembly
			Return Nothing
		End If
		currentOccurrence = currentOccurrence.ParentOccurrence
	Next
	Return currentOccurrence
End Function

Function PromoteSubAssy(oAssyName As String, Optional DeleteOpt As Boolean = False)
	'PromoteSubAssy("K1_1:1,true")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oDef As AssemblyComponentDefinition = oDoc.ComponentDefinition
	' Get the top level occurrence of an assembly
	Dim oSubAssyOcc As ComponentOccurrence
	' Insert the name of the subassembly as shown in the modeltree
	oSubAssyOcc = oDef.Occurrences.ItemByName(oAssyName)
	' Get the 2nd level occurrence under the assembly occurrence
	Dim oSubOcc As ComponentOccurrenceProxy
	Dim oPane As BrowserPane = oDoc.BrowserPanes.Item("Model")
	' Get the browser nodes corresponding to the two occurrences
	Dim oTargetNode As BrowserNode
	oTargetNode = oPane.GetBrowserNodeFromObject(oSubAssyOcc)
	For Each oSubOcc In oDef.Occurrences.ItemByName(oAssyName).SubOccurrences
		Dim oSourceNode As BrowserNode
		oSourceNode = oPane.GetBrowserNodeFromObject(oSubOcc)
		Call oPane.Reorder(oTargetNode, True, oSourceNode)
	Next

	If DeleteOpt Then
		oSubAssyOcc.Delete()
	End If
End Function

Function PrintAllOccuData(oAllOccur As ObjectCollection)
	For i As Integer = 1 To oAllOccur.Count
		Dim oOcc As ComponentOccurrence = oAllOccur(i)
		oLevel = oOcc.OccurrencePath.Count
		Dim pathName As String = oOcc.OccurrencePath.Item(1).Name.ToLower()
		Logger.Debug(oOcc.Name & " P:" & pathName & " L:" & oLevel)
	Next
End Function