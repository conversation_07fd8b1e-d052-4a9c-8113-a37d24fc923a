Sub main()
PartNumbers = GetAllFileNamesPartNumber({"C:\Vault_BH\Designs\Proj" } , "PartNumber")

For Each oVal In PartNumbers
	Paino = SoveliaSearh(oVal, "WEIGHT")
	Logger.Debug(oVal & " mass:" & Paino)
Next


'oFileList = GetAllFileNames({"C:\Vault_BH\Designs\Proj" })

'Publish(ThisDoc.PathAndFileName(True))

'	For Each oVal In oFileList
'		Publish(oVal)
'	Next

'iLogicVb.UpdateWhenDone = True

End Sub

Function SoveliaSearh(SearchID As String, resultCriteria As String)

	If SearchID = "" Then
		exit function
	End If

	Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
	_addInInterface = addin.Automation
	Dim searchCriteria As New List(Of String)()
	Dim resultLimit As Integer
	searchCriteria.Add("DocID:" & SearchID)
	resultLimit = 1
	Dim task As System.Threading.Tasks.Task(Of String) = _addInInterface.Search(searchCriteria, resultCriteria, resultLimit)
	Dim oResult As String
	Dim TxtData() As String

	'[15 s aika raja
	Dim timeout As Integer = 15000
	Try
	If task.Wait(timeout) Then
		oResult = task.Result
	Else
		Logger.Debug("The search operation timed out.")
	End If
	Catch
		Logger.Debug("Timeloop error " & SearchID)
	End Try
	']

	'[unlimited
	'	Dim awaiter As System.Runtime.CompilerServices.TaskAwaiter(Of String) = task.GetAwaiter()
	'	Try
	'		awaiter.GetResult()
	'		oResult = awaiter.GetResult()
	'	Catch
	'		Logger.Debug("Error in ID: " & SearchID)
	'	End Try
	']


	Try
		TxtData = oResult.Split(";"c)
		Vali = TxtData(0).Split(":"c)
		Try
			Resultti = CDbl(Vali(1))
		Catch
			Resultti = TxtData(0)
		End Try

		Return Resultti
	Catch
		Logger.Debug("Error in search :" & SearchID)
	End Try
End Function

Function Publish(oFileName As String)
	Try
		Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
		_addInInterface = addin.Automation
		_addInInterface.Publish(oFileName)
		Logger.Debug("Publish done :" & oFileName)
	Catch ex As Exception
		System.Windows.Forms.MessageBox.Show(ex.Message)
		Exit Function
	End Try
End Function
Function GetAllFileNamesPartNumber(Filtteri() As String, Optional oType As String = "")
	Dim myFileArrayList As New ArrayList : teksti = ""
	Dim myPNumArrayList As New ArrayList
	For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
		Try
			For Each oFiltteri In Filtteri
				oFullName = oRefDoc.FullFileName
				If Not myFileArrayList.Contains(oFullName) And oFullName.Contains(oFiltteri) Then
					myFileArrayList.Add(oFullName)
					partNumber = oRefDoc.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value
					myPNumArrayList.Add(partNumber)
				End If
			Next
		Catch ex As Exception
			Logger.Debug("Error :" & ex.Message)
		End Try
	Next
	If oType = "PartNumber" Then
		Return myPNumArrayList
	Else
		Return myArrayList
	End If
End Function
