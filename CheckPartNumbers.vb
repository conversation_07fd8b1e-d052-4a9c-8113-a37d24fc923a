Sub Main()
Dim oRootDoc As AssemblyDocument = CType(ThisApplication.ActiveDocument, AssemblyDocument)
Dim docHierarchy As New Dictionary(Of Document, Integer)
Dim componentCounts As New Dictionary(Of String, Integer)

BuildHierarchy(oRootDoc.ComponentDefinition.Occurrences, 0, docHierarchy, componentCounts)

Dim oTeksti As String = ""
Dim IdTableHash As Hashtable = New Hashtable

Dim groupTotals As New Dictionary(Of String, Integer)
Dim previousGroup As String = ""
Dim DialogTXT As New ArrayList :

DialogTXT.Add("Filter")
DialogTXT.Add("Close dialog")

For Each oRefDoc As Document In oRootDoc.AllReferencedDocuments
	Dim paramValue As String = ""
	Dim oParaTxt As String = ""
	Dim partNumber As String = ""
	Dim oDesc As String = ""
	oAddInfo = "" : AddAddInfo = False

	Try
		Dim oFullName As String = oRefDoc.FullFileName
		If Not oFullName.ToUpper.Contains("\STD\") Then
			Try
				partNumber = oRefDoc.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value
				oDesc = oRefDoc.PropertySets.Item("Design Tracking Properties").Item("Description").Value
			Catch ex As Exception
				partNumber = "n/a"
			End Try

			oDrawingName = Left(oFullName, Len(oFullName) -4) & ".idw"

			If System.IO.File.Exists(oDrawingName) = True Then
				HasDrawing = "|DR:Yes"
			Else
				HasDrawing = "|DR:No"
			End If

			If TypeOf oRefDoc Is PartDocument Then
				Dim oPartDoc As PartDocument = CType(oRefDoc, PartDocument)
				Dim oParameters As Parameters = oPartDoc.ComponentDefinition.Parameters
				For Each oParam As Parameter In oParameters
					If oParam.Name = "G_L" Then
						paramValue = Math.Round(oParam.Value * 10).ToString()
						Exit For
					End If
				Next
			End If

			If paramValue <> "" Then
				oParaTxt = paramValue & "|"
			End If

			Dim level As Integer = If (docHierarchy.ContainsKey(oRefDoc), docHierarchy(oRefDoc), 0)
			Dim indentation As String = New String(" "c, level * 3)

			Dim componentCount As Integer = 0
			If componentCounts.ContainsKey(partNumber) Then
				componentCount = componentCounts(partNumber)
			End If
			' Determine the group key dynamically based on the part number
			Dim groupKey As String = ""
			If partNumber.Length > 1 AndAlso Char.IsLetter(partNumber(0)) Then
				If Char.IsDigit(partNumber(1)) Then
					' Single letter group when second character is a digit (e.g., C2, T1)
					groupKey = partNumber.Substring(0, 1)
				Else
					' Double letter group when first two characters are letters (e.g., XJ3, VT4)
					groupKey = partNumber.Substring(0, 2)
				End If
			ElseIf partNumber.Length > 0 Then
				' Fallback to the first character if the part number is very short
				groupKey = partNumber.Substring(0, 1)
			End If

			' Add to group totals
			If groupTotals.ContainsKey(groupKey) Then
				groupTotals(groupKey) += componentCount
			Else
				groupTotals(groupKey) = componentCount
			End If
			' Check if a new group starts to insert the total of the previous group
			If previousGroup <> "" AndAlso previousGroup <> groupKey Then
				oTeksti += previousGroup & " tot Count: " & groupTotals(previousGroup).ToString() & vbCrLf
				groupTotals(previousGroup) = 0 ' Reset subtotal after showing
			End If

			previousGroup = groupKey

			Logger.Debug(indentation & partNumber & "|" & oDesc & "|" & oParaTxt & "|" & "Count: " & componentCount.ToString())
			oComment = ""
			If Not IdTableHash.ContainsKey(partNumber) Then
				IdTableHash.Add(partNumber, oDesc & "|" & oParaTxt)
			Else
				If IdTableHash(partNumber) = oDesc & "|" & oParaTxt Then
					Logger.Debug("Same Id OK : " & partNumber)
				Else
					If Len(partNumber) > 2 Then
						Logger.Debug("Check this Id : " & partNumber)
						oComment = ":CHECK ID old" & IdTableHash(partNumber) & " vs new " & oDesc & "|" & oParaTxt
					End If
				End If
			End If

			If oFullName.ToUpper.Contains("\SRC\") Then
				oComment += "#FILE LOCATED SRC -folder"
			End If

			If AddAddInfo Then
				oAddInfo = "|" & oParaTxt & " [" & oFullName & "]"
			End If

			If Len(partNumber) > 14 Then
				CheckLen = " | TOO LONG PART NUMBER [" & Len(partNumber) & "]"
				'			ElseIf Len(partNumber) < 10 'periaatteessa noin mutta poikkeuksena projektille tulevat vakiot esim XE01 polku proj mutta tilataan
				'				CheckLen = " TOO SHORT PART NUMBER " & Len(partNumber)
			Else
				CheckLen = ""
			End If
			oDatTxt = indentation & partNumber & HasDrawing & oComment & "|" & oDesc & oAddInfo & " | Count: " & componentCount.ToString() & CheckLen & "|" & oFullName
			oTeksti += oDatTxt & vbCrLf
			DialogTXT.Add(oDatTxt)
		End If

	Catch ex As Exception
		Logger.Debug("Error processing document: " & ex.Message)
	End Try
Next
' Add the total of the last group if needed
If previousGroup <> "" Then
	oTeksti += previousGroup & " tot Count: " & groupTotals(previousGroup).ToString() & vbCrLf
End If

My.Computer.Clipboard.SetText(oTeksti)
selectedItem = InputListBox("Select option, selecting part row creates drawing: ", DialogTXT, DialogTXT(0), "Part number checker", "PN|DRW|Description|Pcs|Fullname")

If selectedItem = "Close dialog" Then

	Dim stringArray As String() = DialogTXT.ToArray(GetType(String))
	Dim clipboardText As String = String.Join(vbCrLf, CType(stringArray, String()))
	My.Computer.Clipboard.SetText(clipboardText)
ElseIf selectedItem = "Filter" Then
	' Show filter dialog
	Dim filterOptions As New ArrayList
	filterOptions.Add("T/windbrace parts without drawings")
	filterOptions.Add("VT/wallbrace parts without drawings")
	filterOptions.Add("C/eave tube parts without drawings")
	filterOptions.Add("B/ridge tube parts without drawings")
	filterOptions.Add("G/braceparts without drawings")
	filterOptions.Add("E/crossarm parts without drawings")
	filterOptions.Add("DL/purlin parts without drawings")
	filterOptions.Add("XJ/profile parts without drawings")
	filterOptions.Add("S/Profile parts without drawings")
	filterOptions.Add("DR:No")
	filterOptions.Add("Custom filter...")
	filterOptions.Add("Check these models -no project number")

	Dim selectedFilter As String = InputListBox("Select filter option:", filterOptions, filterOptions(0), "Filter Options")
	' Create filtered list based on selection
	Dim filteredList As New ArrayList
	filteredList.Add("Close dialog")
	filteredList.Add("Back to full list")
	filteredList.Add("Generate all")

	If selectedFilter = "Custom filter..." Then
		customPrefix = InputBox("Enter part number prefix(es) separated by commas (e.g., T,C,P):", "Custom Filter")
		prefixes = customPrefix.Split(New Char() {","c })
	End If

	For i As Integer = 2 To DialogTXT.Count - 1
		Dim item As String = DialogTXT(i).ToString()
		Dim parts As String() = item.Split(New Char() {"|"c })
		Dim partNumber As String = parts(0).Trim()
		Dim drawingStatus As String = "|" & parts(1)

		Select Case selectedFilter
			Case "T/windbrace parts without drawings"
				If partNumber.StartsWith("T") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "VT/wallbrace parts without drawings"
				If partNumber.StartsWith("VT") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "C/eave tube parts without drawings"
				If partNumber.StartsWith("C") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "B/Ridge tube parts without drawings"
				If partNumber.StartsWith("B") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "G/braceparts without drawings"
				If partNumber.StartsWith("G") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "E/crossarm parts without drawings"
				If partNumber.StartsWith("E") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "DL/purlin parts without drawings"
				If partNumber.StartsWith("DL") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "XJ/profile parts without drawings"
				If partNumber.StartsWith("XJ") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "S/Profile parts without drawings"
				If partNumber.Contains("S_") AndAlso drawingStatus = "|DR:No" Then
					filteredList.Add(item)
				End If
			Case "DR:No"
				If drawingStatus = "|DR:No" AndAlso partNumber.Contains(ProjectNum) Then
					filteredList.Add(item)
				End If

			Case "Custom filter..."
				Dim matchesPrefix As Boolean = False

				For Each prefix In prefixes
					If partNumber.StartsWith(prefix.Trim()) Then
						matchesPrefix = True
						Exit For
					End If
				Next

				If matchesPrefix Then
					filteredList.Add(item)
				End If

			Case "Check these models -no project number"
				If Not partNumber.Contains(ProjectNum) Then
					filteredList.Add(item)
				End If

		End Select
	Next
	' Show filtered results
	Dim filteredItem As String = InputListBox("Filtered results:", filteredList, filteredList(0), "Filtered Data")

	If filteredItem = "Close dialog" Then
		' Copy filtered list to clipboard
		Dim filteredText As String = String.Join(vbCrLf, filteredList)
		My.Computer.Clipboard.SetText(filteredText)
	ElseIf filteredItem = "Back to full list" Then
		' Recursively call Main to restart
		Main()
	ElseIf filteredItem = "Generate all" Then
		k = MessageBox.Show("Generate all drawing?", "Create all drws", MessageBoxButtons.YesNo)
		If k = vbYes Then
			ErrorTxt = ""
			For j = 3 To filteredList.Count - 1
				IsOk = CopyDrw(filteredList(j))

				If Not IsOk Is Nothing Then
					ErrorTxt += IsOk & vbLf
				End If
			Next
			MessageBox.Show(ErrorTxt, "Errors in drawings generation")
		End If
	ElseIf filteredItem Is Nothing Then
		Logger.Debug("Exiting from rule")
		Exit Sub
	Else
		openM = MessageBox.Show("Generate drawing (No for open model) ?", "Drawing", MessageBoxButtons.YesNo)
		If openM = vbYes Then
			CopyDrw(filteredItem)
		Else
			OpenModel(filteredItem)
		End If
	End If
Else
	If Not selectedItem Is Nothing Then
		openM = MessageBox.Show("Generate drawing (No for open model) ?", "Drawing", MessageBoxButtons.YesNo)
		If openM = vbYes Then
			CopyDrw(selectedItem)
		Else
			OpenModel(selectedItem)
		End If
	End If
End If
End Sub

Function OpenModel(selectedItem As String)
	If selectedItem Is Nothing Then Exit Function
	Dim TextSplittaus As String() = selectedItem.Split(New Char() {"|"c })
	ModelAlready = TextSplittaus(4)
	oDrawingDoc = ThisApplication.Documents.Open(ModelAlready, True)
End Function


Function CopyDrw(selectedItem As String, Optional startFromModel As Boolean = False, Optional localRuleNameToRun As String = "ReplaceModelReference")
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation

	If selectedItem Is Nothing Then Exit Function

	Dim TextSplittaus As String() = selectedItem.Split(New Char() {"|"c })
	Dim oDrawingDoc As DrawingDocument

	If startFromModel Then
		PartNumber = iProperties.Value("Project", "Part Number")
		Modelname = ThisDoc.PathAndFileName(True)
		oNewDrwName = Left(Modelname, Len(Modelname) -4) & ".idw"
	Else
		If TextSplittaus(1) = "DR:Yes" Then
			i = MessageBox.Show("Drawing already exists, open drawing", "DRW", MessageBoxButtons.YesNo)
			If i = vbYes Then
				ModelAlready = TextSplittaus(4)
				oDrwName = Left(ModelAlready, Len(ModelAlready) -4) & ".idw"
				oDrawingDoc = ThisApplication.Documents.Open(oDrwName, True)
				Exit Function
			Else
				Exit Function
			End If
		ElseIf TextSplittaus(1) = "DR:No" Then
			Modelname = TextSplittaus(UBound(TextSplittaus))
			oNewDrwName = Left(Modelname, Len(Modelname) -4) & ".idw"
		End If
		PartNumber = TextSplittaus(0).Trim()
	End If

	oTemplateDrwPath = CalcTemplatePN(PartNumber, selectedItem)

	If Not System.IO.File.Exists(oTemplateDrwPath) Then
		MsgTxt = "Template drawing not found at: " & PartNumber
		Return MsgTxt
	End If

	If System.IO.File.Exists(oNewDrwName) Then
		Dim result = MessageBox.Show("Drawing file already exists: " & oNewDrwName & vbLf & "Overwrite?", "File Exists", MessageBoxButtons.YesNo)
		If result = vbNo Then
			MessageBox.Show("Operation cancelled by user.", "Cancelled")
			Exit Function
		End If
	End If

	Try
		System.IO.File.Copy(oTemplateDrwPath, oNewDrwName, True)
		Logger.Debug("Copied template to: " & oNewDrwName)
	Catch ex As Exception
		MessageBox.Show("Error copying file: " & ex.Message, "File Copy Error")
		Exit Function
	End Try

	Try
		attributes = System.IO.File.GetAttributes(oNewDrwName)
		If (attributes And System.IO.FileAttributes.ReadOnly) = System.IO.FileAttributes.ReadOnly Then
			attributes = attributes And Not System.IO.FileAttributes.ReadOnly
			System.IO.File.SetAttributes(oNewDrwName, attributes)
		End If
	Catch

	End Try

	SharedVariable("Modelname") = Modelname
	Try
		oDrawingDoc = ThisApplication.Documents.Open(oNewDrwName, True)
	Catch ex As Exception
		MessageBox.Show("Error opening new drawing: " & ex.Message, "Open Error")
		Exit Function
	End Try

	oDrawingDoc.Update()

	Try
		Call iLogic.RunRule(oDrawingDoc, localRuleNameToRun)
	Catch
		Logger.Debug("Unable to run the rule")
	Finally
		oDrawingDoc = Nothing
	End Try
End Function



Function CalcTemplatePN(PartNumber As String, selectedItem As String)
	If PartNumber <> PartNumber.Trim() Then
		MessageBox.Show("Part number includes leading and trailing white spaces", "Error")
		PartNumber = PartNumber.Trim()
	End If

	If Left(PartNumber, 1) = "T" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\wind_brace.idw"
	ElseIf Left(PartNumber, 1) = "E" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\brace3.idw"
	ElseIf Left(PartNumber, 2) = "VT" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\wall_brace.idw"
	ElseIf Left(PartNumber, 1) = "B" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\ridge_tube.idw"
	ElseIf Left(PartNumber, 1) = "C" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\eave_tube.idw"
	ElseIf Left(PartNumber, 1) = "G" Then
		If selectedItem.Contains("gable_end_") Or selectedItem.Contains("Päädyn vinotuki") Then 'tarvitaanko detskut erilaisia?!
			oTemplateDrwPath = "C:\Vault_BH\Designs\Src\gable_end_pillar_tube.idw"
		Else
			oTemplateDrwPath = "C:\Vault_BH\Designs\Src\brace.idw"
		End If
	ElseIf Left(PartNumber, 2) = "DL" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\purlin_tube.idw"
	ElseIf Left(PartNumber, 2) = "XJ" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\gable_end_pillar\pocket_profile.idw"
	ElseIf Left(PartNumber, 3) = "DIA" Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\O.idw"
	ElseIf PartNumber.Contains("S_") Then
		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\O.idw"
		'	ElseIf Left(PartNumber, 2) = "PO" Then 'tämä on kokoonpano!!
		'		oTemplateDrwPath = "C:\Vault_BH\Designs\Src\O.idw"
	End If


	Return oTemplateDrwPath
End Function

Sub BuildHierarchy(ByVal oOccurrences As ComponentOccurrences, ByVal level As Integer, ByRef hierarchy As Dictionary(Of Document, Integer), ByRef componentCounts As Dictionary(Of String, Integer))
	For Each oOccurrence As ComponentOccurrence In oOccurrences
		Try
			Dim oDoc As Document = oOccurrence.Definition.Document
			Dim partNumber As String = ""
			Try
				partNumber = oDoc.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value
			Catch ex As Exception
				partNumber = "n/a"
			End Try

			If componentCounts.ContainsKey(partNumber) And partNumber <> "n/a" And partNumber <> "" Then 'onko hyvä toisaalta
				componentCounts(partNumber) += 1
			Else
				componentCounts.Add(partNumber, 1)
			End If

			If Not hierarchy.ContainsKey(oDoc) Then
				hierarchy.Add(oDoc, level)
			End If

			If oOccurrence.DefinitionDocumentType = DocumentTypeEnum.kAssemblyDocumentObject Then
				BuildHierarchy(oOccurrence.SubOccurrences, level + 1, hierarchy, componentCounts)
			End If
		Catch ex As Exception
			'Logger.Debug(oOccurrence.Name & " Error: " & ex.Message)
		End Try
	Next
End Sub
