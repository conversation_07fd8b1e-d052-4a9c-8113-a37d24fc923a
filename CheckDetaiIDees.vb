Sub Main()
Dim asmDoc As AssemblyDocument = ThisApplication.ActiveDocument
Dim bomDict As New Dictionary(Of String, Integer) ' Store DET* items and their total quantities

OverrideQtyHash = GetPartNumQtyHash("DET")


ProcessOccurrences(asmDoc.ComponentDefinition.Occurrences, bomDict, OverrideQtyHash)



' Output the results
Dim output As String = "DET Part List:" & vbCrLf
For Each kvp In bomDict
	output &= kvp.Key & " - " & kvp.Value & " pcs" & vbCrLf
Next

MsgBox(output, vbInformation, "DET Part Summary")
End Sub

' Recursive function to process all occurrences
Sub ProcessOccurrences(oCompOccs As ComponentOccurrences, ByRef bomDict As Dictionary(Of String, Integer), HashQty As Hashtable)
	For Each oCompOcc In oCompOccs
		Dim oDef As ComponentDefinition = oCompOcc.Definition
		Dim isVirtual As Boolean = TypeOf oCompOcc.Definition Is VirtualComponentDefinition
		' Check if part number starts with "DET*"
		Dim partNumber As String = ""
		Try
			partNumber = iProperties.Value(oCompOcc.name, "Project", "Part Number")
		Catch ex As Exception
		End Try
		If partNumber.StartsWith("DET") Then
			Try
				oQty = HashQty(partNumber)
			Catch
				oQty = 1
			End Try
			If bomDict.ContainsKey(partNumber) Then
				bomDict(partNumber) += oQty
			Else
				bomDict.Add(partNumber, oQty)
			End If
		End If
		' Check for PHANTOM_ASSY_GEN iProperty
		Try
			Dim phantomProp As String = iProperties.Value(oCompOcc.name, "Custom", "PHANTOM_ASSY_GEN")
			Dim detItems As Dictionary(Of String, Integer) = ExtractDETQuantities(phantomProp)
			For Each kvp In detItems
				If bomDict.ContainsKey(kvp.Key) Then
					bomDict(kvp.Key) += kvp.Value
				Else
					bomDict.Add(kvp.Key, kvp.Value)
				End If
			Next
		Catch ex As Exception
		End Try
		' If it's an assembly, process sub-occurrences recursively
		If TypeOf oDef Is AssemblyComponentDefinition Then
			ProcessOccurrences(oCompOcc.SubOccurrences, bomDict, HashQty)
		End If
	Next
End Sub
Function GetPartNumQtyHash(oNumFilter As String)
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oBOM As BOM = oAsmDoc.ComponentDefinition.BOM
	Dim oBOMViews As BOMViews = oBOM.BOMViews
	Dim oBOMView As BOMView = oBOMViews.Item("Unnamed") 'Unnamed/Structured
	Dim TableHash As Hashtable = New Hashtable

	Dim oBOMRow As BOMRow = Nothing
	For Each Row As BOMRow In oBOMView.BOMRows
		' Check if the row has any ComponentOccurrences
		If Row.ComponentOccurrences.Count > 0 Then
			Dim oComponent As ComponentOccurrence = Row.ComponentOccurrences.Item(1) ' Get the first occurrence
			'Dim componentPartNumber As String = oComponent.Definition.Document.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value
			Dim componentPartNumber As String = iProperties.Value(oComponent.Name, "Project", "Part Number")
			If componentPartNumber.Contains(oNumFilter) Then
				If Row.TotalQuantityOverridden Then
					TotCount = CInt(Row.TotalQuantity)
				Else
					TotCount = 1
				End If
				'Logger.Debug("ItemNumber: " & Row.ItemNumber) : Logger.Debug("componentPartNumber: " & componentPartNumber) : Logger.Debug("TotCount: " & TotCount)
				TableHash.Add(componentPartNumber, TotCount)
			End If
		End If
	Next

	Return TableHash
End Function
Function ExtractDETQuantities(propValue As String) As Dictionary(Of String, Integer)
	Dim result As New Dictionary(Of String, Integer)
	Dim parts() As String = propValue.Split("|"c) ' Split at "|"
	For Each part In parts
		Dim details() As String = part.Split("¤"c) ' Split details at "¤"
		If details.Length >= 3 Then
			Dim partName As String = details(0).Trim()
			If partName.StartsWith("DET") Then
				Dim qty As Integer
				If Integer.TryParse(details(1), qty) Then
					If result.ContainsKey(partName) Then
						result(partName) += qty
					Else
						result.Add(partName, qty)
					End If
				End If
			End If
		End If
	Next
	Return result
End Function
