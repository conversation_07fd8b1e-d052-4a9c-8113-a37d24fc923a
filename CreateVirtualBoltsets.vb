Sub main()
Dim VirtOccHash As Hashtable = New Hashtable
UpdateHash(bolt_brace_cal_pcs, bolt_brace_gen_ideet, VirtOccHash)
UpdateHashList(bolt_crossarm_cal_pcs, bolt_crossarm_gen_ideet, VirtOccHash)
UpdateHash(bolt_windbrace_cal_pcs, bolt_windbrace_gen_ideet, VirtOccHash)
UpdateHash(bolt_wallbrace_cal_pcs, bolt_wallbrace_gen_ideet, VirtOccHash)
UpdateHashList(bolt_hinge_cal_pcs, bolt_hinge_ideet, VirtOccHash)


For Each oKey In VirtOccHash.Keys 'actuak virtual occ cration
	CreateByIDdb(oKey, 1, VirtOccHash(oKey))
Next

End Sub

Function UpdateHashList(TextSplittausPcs As String, TextSplittausId As String, VirtOccHash As Hashtable)
	Dim TextSaranaPcs As String() = TextSplittausPcs.Split(New Char() {"|"c })
	Dim TextSaranaIdeet As String() = TextSplittausId.Split(New Char() {"|"c })
	For i = 1 To TextSaranaPcs.Count
		UpdateHash(CDbl(TextSaranaPcs(i - 1)), TextSaranaIdeet(i - 1), VirtOccHash)
	Next
	Return VirtOccHash
End Function

Function UpdateHash(pcs As Integer, DataS As String, VirtOccHash As Hashtable)
	Dim TextToSplit() As String = DataS.Split(New String() {"|" }, StringSplitOptions.RemoveEmptyEntries)
	For Each oVal In TextToSplit
		NamePcs = oVal.Split(New String() {"¤" }, StringSplitOptions.RemoveEmptyEntries)
		oName = NamePcs(0)
		Try
			oPcs = CInt(NamePcs(1)) * pcs 'obs muita ei huomida jos käytetään gen_assy koodia Tämä lähinn' jos 'vanhaa muotoilua 
		Catch
			oPcs = 1 * pcs
		End Try
		If VirtOccHash.ContainsKey(oName) Then
			VirtOccHash(oName) = VirtOccHash(oName) + oPcs
		Else
			VirtOccHash.Add(oName, oPcs)
		End If
	Next
	Return VirtOccHash
End Function

Function CreateByIDdb(VirtualNumber As String, VirtualRevisio As Integer, oPcs As Integer)
	If oPcs<1 Then
		Exit Function
	End If

	Dim virtOcc As ComponentOccurrence
	Dim identity As Matrix = ThisApplication.TransientGeometry.CreateMatrix
	Dim virtOccNimi As String = VirtualNumber
	Try
		virtOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AddVirtual(virtOccNimi, identity)
	Catch
		DeleteAllByID(VirtualNumber)
		virtOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AddVirtual(virtOccNimi, identity)
	End Try
	virtOcc.Definition.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value = VirtualNumber
	virtOcc.Definition.PropertySets.Item("Summary Information").Item("Revision Number").Value = VirtualRevisio
	If oPcs>1 Then
		ChangeQtyByPartNumberID(VirtualNumber, oPcs + oSaveQty)
	End If
End Function
Function DeleteAllByID(ID As String)
	For Each oOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Dim oName As String = oOccurrence.Name
		If oName.Contains(ID) Then
			Logger.Debug("Deleting ID " & ID & " occ: " & oName)
			Try : Component.InventorComponent(oName).Delete() : Catch : End Try
		End If
	Next
End Function
Function ChangeQtyByPartNumberID(ID As String, quantity As Integer)
	Try
		ThisBOM.OverrideQuantity("Model Data", ID, quantity)
		Logger.Debug("BOM Override for " & ID & " is now " & quantity & " pcs")
	Catch
		Logger.Debug("Qty error in itemNumber " & ID)
	End Try
End Function