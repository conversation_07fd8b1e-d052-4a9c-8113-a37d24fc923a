Sub Main
	Try
		MultiValue.List("PillarsPos") = SharedVariable("PillarsPos")
	Catch
		Logger.Debug("Shared variable PillarsPos is missing...")
	End Try

	i = MessageBox.Show("Insert Braces", "Gable end braces", MessageBoxButtons.YesNo)
	If i = vbYes Then

		If GableEndBracesList Then
			Dim PillarsPosList As ArrayList = MultiValue.List("PillarsPos")
			For Each oPos In PillarsPosList
				Insert1BE(oPos)
			Next
		Else
			Insert1BE(PillarsPos)
		End If

	Else
		LS02Counter = 1
	End If
End Sub

Function Insert1BE(PositionGE As Double)
	Try : oTest = SharedVariable("ProjectNum") : Catch : Logger.Debug("Shared variable ProjectNum is missing. Start Get Data command before") : Exit Function : End Try

	Latta = "C:\Vault_BH\Designs\Std\BH-Vakiolatat\LS-latat\LS02.ipt" 'missä nämä olisi hyvä määritellä
	oType = "D60"
	If oType = "D60" Then
		Vinotuki = "gable_end_pillar_tube.ipt"
		cons2 = "YZ Plane"
	Else
		Vinotuki = "gable_end_pillar_brace.ipt"
		cons2 = "XZ Plane"
	End If

	'	Dim oList As New List(Of String) From {"f2", "f7", "f8", "mirror", "###", "p1k", "p2k", "h" }
	'	Dim Selected As IEnumerable(Of Object) = MultiSelectListBox("Select Some", oList, Nothing, "Multi-Select InputListBox", "My List")
	'	If Selected.Count = 0 Then
	'		Logger.Debug("Nothing was selected from the list.", "None Selected")
	'		Exit Sub
	'	End If

	Dim LS02_1 = Components.Add("LS02_" & LS02Counter, Latta) : LS02Counter += 1

	Dim LS02_2 = Components.Add("LS02_" & LS02Counter, Latta) : LS02Counter += 1

	Dim occ1Name As String = LS02_1.Name : Dim occ2Name As String = LS02_2.Name

	oList = SharedVariable("oList")
	oType = GetPositionLabel(oList, PositionGE) '+ tähän h tarkistus

	ModuleNum = RoofNumberCheck(PositionGE)

	If oType = "k2" Then
		AlaPinta = "inner_bottom_k2"
		YlaPinta = "inner_up_k2"
		Compo = ModuleNum
	Else
		AlaPinta = "inner_bottom_k1"
		YlaPinta = "inner_up_k1"
		Compo = ModuleNum ' miten harja ! 
	End If


	MakeConsGE(occ1Name, "f1", Compo, AlaPinta) 'flippaus inner_bottom_k2
	MakeConsGE(occ2Name, "f" & GableEndBraceInsertFrame, Compo, YlaPinta)  'flippaus inner_up_k2

	Constraints.AddMate("", LS02_1, "XY Plane", LS02_2, "XY Plane")

	ThisApplication.ActiveDocument.Update2(True)

	oDist = Get2pointDist(occ1Name, occ2Name)

	oNewFilu = "gable_end_pillar_brace_" & oDist & "_" & SharedVariable("ProjectNum") '& Abs(Now.GetHashCode)
	oNewOccName = "gable_end_pillar_brace_" & oDist & "_" & LS02Counter
	UusiFullFileName = SaveFilu(Vinotuki, oNewFilu)
	Dim EBrace = Components.Add(oNewOccName, UusiFullFileName)
	Parameter(oNewOccName, "G_L") = oDist
	Try : Constraints.AddInsert("", EBrace, "Edge0", LS02_1, "Edge0", axesOpposed := True) : Catch : End Try
	Constraints.AddMate("", EBrace, cons2, LS02_2, "start: Center Point")

	Constraints.AddMate("Xdir_" & LS02Counter, "", "YZ Plane", LS02_1, "Center Point", PositionGE)


	If MirrorGableEndBraces Then
		Dim LS02_1_mir = Components.Add("LS02mir_" & LS02Counter - 2, Latta)
		Dim LS02_2_mir = Components.Add("LS02mir_" & LS02Counter - 1, Latta)

		Dim occ1MirName As String = LS02_1_mir.Name : Dim occ2MirName As String = LS02_2_mir.Name
		'		Dim LS02_1_mir = Components.Add("LS02_" & LS02Counter, Latta) : LS02Counter += 1
		'		Dim LS02_2_mir = Components.Add("LS02_" & LS02Counter, Latta) : LS02Counter += 1
		EndGEFrame = totalFramePcs - GableEndBraceInsertFrame + 1

		MakeConsGE(occ1MirName, "f" & totalFramePcs, Compo, AlaPinta) 'flippaus inner_bottom_k2
		MakeConsGE(occ2MirName, "f" & EndGEFrame, Compo, YlaPinta)  'flippaus inner_up_k2
		Constraints.AddMate("", LS02_1_mir, "XY Plane", LS02_2_mir, "XY Plane")
		Constraints.AddFlush("", LS02_1_mir, "XY Plane", LS02_1, "XY Plane")

		Dim EBraceMir = Components.Add(oNewOccName & "_mir", UusiFullFileName)
		Try : Constraints.AddInsert("", EBraceMir, "Edge0", LS02_1_mir, "Edge0", axesOpposed := True) : Catch : End Try
		Constraints.AddMate("", EBraceMir, cons2, LS02_2_mir, "start: Center Point")
	End If

	InventorVb.DocumentUpdate()
	Constraints.Delete("Xdir_" & LS02Counter)
End Function

Function GetPositionLabel(oList As System.Collections.ArrayList, value As Double) As String
	If oList Is Nothing Then Exit Function


	If value<e_hall_tot_width / 2 Then
		positionCheck = value
	Else
		positionCheck = e_hall_tot_width - value
	End If

	For Each item In oList
		' Cast the item to Object() array
		Dim rangeItem As Object() = CType(item, Object())

		Dim lower As Double = Min(CDbl(rangeItem(0)), CDbl(rangeItem(1)))
		Dim upper As Double = Max(CDbl(rangeItem(0)), CDbl(rangeItem(1)))
		Dim label As String = CStr(rangeItem(2))

		' Check if the value is within the range
		If positionCheck > lower And positionCheck < upper Then
			' Extract just "k1", "k2", etc. from "bottom_k1"
			If label.Contains("bottom_k2") Then
				Return "k2"
			Else
				Return label 'miten harja
			End If
		End If
	Next
	' If no range matched
	Logger.Debug("Error not found surface, using k2")
	Return "k2"
End Function

Function Get2pointDist(oOccName1 As String, oOccName2 As String)
	InventorVb.DocumentUpdate()
	PointtiXYZ1 = GetWorkpointCoord(oOccName1 & "/start: Center Point")
	PointtiXYZ2 = GetWorkpointCoord(oOccName2 & "/start: Center Point")
	oDistanceCalc = Sqrt((PointtiXYZ2(0) -PointtiXYZ1(0)) ^ 2 + (PointtiXYZ2(1) -PointtiXYZ1(1)) ^ 2 + (PointtiXYZ2(2) -PointtiXYZ1(2)) ^ 2)
	If oDistanceCalc<100 Then
		MessageBox.Show("Error getting disctance " & oOccName1 & " vs " & oOccName2 & " dist: " & oDistanceCalc, "Error")
		oDistanceCalc = 3210
	End If
	Return Round(oDistanceCalc)
End Function

Function GetWorkpointCoord(OccWP As String)
	'ResultPointCoord = GetWorkpointCoord("f8/p1k/InsertionPoint: Center Point") / ResultPointCoord = GetWorkpointCoord("f8/Center Point")
	oArvot = OccWP.Split(New Char() {"/"c })
	Dim subAsmOccurrence As ComponentOccurrence
	Dim partOccurrence As ComponentOccurrence
	Dim mainAsm As AssemblyDocument = ThisApplication.ActiveDocument
	If oArvot.Length = 3 Then
		subAsmOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
		partOccurrence = subAsmOccurrence.Definition.Occurrences.ItemByName(oArvot(1))
		oWP = oArvot(2)
	ElseIf oArvot.Length = 2
		Try
			partOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName(oArvot(0))
		Catch
			Logger.Debug("No point value for :" & OccWP)
			Return {0, 0, 0 }
		End Try
		oWP = oArvot(1)
	End If
	' Locate the work point in the part
	Dim workPoint As WorkPoint = partOccurrence.Definition.WorkPoints.Item(oWP)
	' Get the position of the work point in the part's local coordinate system
	Dim pointPosition As Point = workPoint.Point
	' Get the transformation matrices for part and subassembly
	Dim partMatrix As Matrix = partOccurrence.Transformation

	If oArvot.Length = 3 Then
		Dim subAsmMatrix As Matrix = subAsmOccurrence.Transformation
		' Combine the transformations by multiplying the matrices
		partMatrix.TransformBy(subAsmMatrix)
	End If

	' Transform the point position manually using the combined matrix
	Dim x As Double = Round(partMatrix.Cell(1, 1) * pointPosition.X + partMatrix.Cell(1, 2) * pointPosition.Y + partMatrix.Cell(1, 3) * pointPosition.Z + partMatrix.Cell(1, 4), 3) * 10
	Dim y As Double = Round(partMatrix.Cell(2, 1) * pointPosition.X + partMatrix.Cell(2, 2) * pointPosition.Y + partMatrix.Cell(2, 3) * pointPosition.Z + partMatrix.Cell(2, 4), 3) * 10
	Dim z As Double = Round(partMatrix.Cell(3, 1) * pointPosition.X + partMatrix.Cell(3, 2) * pointPosition.Y + partMatrix.Cell(3, 3) * pointPosition.Z + partMatrix.Cell(3, 4), 3) * 10
	' Output the coordinates in the main assembly coordinate system
	Logger.Debug("X: " & x & ", Y: " & y & ", Z: " & z)
	Return {x, y, z }
End Function

Function SaveFilu(SrcFilu As String, NewFilu As String) 'lisää taulu ettei duplikaatteja
	oSrcPath = "C:\Vault_BH\Designs\Src\" : oDoc = ThisApplication.Documents.Open(oSrcPath & SrcFilu, False)
	oNewname = ThisDoc.Path & "\" & NewFilu & ".ipt"
	If IO.File.Exists(oNewname) Then
		Logger.Debug("File already exists : " & oNewname)
	Else
		Logger.Debug("New part File being created : " & oNewname)
		Try
			oDoc.SaveAs(oNewname, True)
		Catch
			MessageBox.Show("Error saving: " & oNewname, "Error")
			exit function
		End Try
	End If
	Return oNewname
End Function

Function MakeConsGE(FirstName As String, SecondName1 As String, SecondName2 As String, Pinta2 As String)
	ucsName = FirstName & "#" & SecondName1 & "/" & SecondName2 & "/" & FirstName & "#" & Pinta2
	Logger.Debug("Creating Constrain :" & ucsName)
	Constraints.AddMate(ucsName, FirstName, "Face0", {SecondName1, "" & SecondName2 & "" }, Pinta2)
	Constraints.AddFlush(ucsName & "_2", FirstName, "YZ Plane", SecondName1, "XZ Plane")
End Function


Function ConvertStr_CArgu(OccName As String()) As ComponentArgument
	ConvertStr_CArgu = OccName
End Function

Function MultiSelectListBox(Optional Instructions As String = vbNullString, Optional Items As IEnumerable = Nothing, Optional DefaultValue As Object = Nothing, Optional Title As String = vbNullString, Optional ListName As String = vbNullString) As IEnumerable(Of Object)
	Using oILBD As New Autodesk.iLogic.Runtime.InputListBoxDialog(Title, ListName, Instructions, Items, DefaultValue)
		Dim oLB As System.Windows.Forms.ListBox = oILBD.Controls.Item(0).Controls.Item(2)
		oLB.SelectionMode = System.Windows.Forms.SelectionMode.MultiSimple
		Dim oDlgResult As System.Windows.Forms.DialogResult = oILBD.ShowDialog()
		Dim oSelected As IEnumerable(Of Object) = oLB.SelectedItems.Cast(Of Object)
		Return oSelected
	End Using
End Function



Function RoofNumberCheck(position As Double)
	AllParamsDict = SharedVariable("AllParamsDict")
	'	AllParamsDict = ParamsDict("", "d", ThisApplication.ActiveDocument.AllReferencedDocuments)
	Dim e_roof_widths As New List(Of Double) From { ParaValue("e_roof_1_width", AllParamsDict, 0),
	ParaValue("e_roof_2_width", AllParamsDict, 0), ParaValue("e_roof_3_width", AllParamsDict, 0), ParaValue("e_roof_4_width", AllParamsDict, 0),
	ParaValue("e_roof_5_width", AllParamsDict, 0), ParaValue("e_roof_6_width", AllParamsDict, 0), ParaValue("e_roof_7_width", AllParamsDict, 0), ParaValue("e_roof_8_width", AllParamsDict, 0) }
	Dim widths As New List(Of Double)

	For Each oVal In e_roof_widths
		If oVal > 0 Then
			widths.Add(oVal)
		End If
	Next
	' Variables to track
	Dim cumulative As Double = 0
	Dim roofNumber As Integer = -1

	If position<e_hall_tot_width / 2 Then
		positionCheck = position
		oSuffix = "k"
	Else
		positionCheck = e_hall_tot_width - position
		oSuffix = "kMir"
	End If

	For i = 0 To widths.Count - 1
		cumulative += widths(i)

		If positionCheck <= cumulative Then
			roofNumber = i + 1 ' Roofs are 1-based
			Exit For
		End If
	Next

	If roofNumber = -1 Then
		roofNumber = widths.Count ' Beyond last segment
	End If

	Return "p" & roofNumber & oSuffix
End Function

Function ParaValue(oParaName, oParaDict, Optional oIns = 0)
	Dim myArrayList As New ArrayList
	Dim duplicateList As New ArrayList
	Dim ParaKey() As String
	For Each oEntry As KeyValuePair(Of String, Object) In oParaDict
		ParaKey = oEntry.Key.Split(New Char() {"#"c })
		ParaKey0 = ParaKey(0)
		If ParaKey0.Contains(oParaName) Then
			'If Not myArrayList.Contains(oEntry.Value) Then
			myArrayList.Add(oEntry.Value)
			duplicateList.Add(ParaKey(1))
			'End If
		End If
		'Logger.Debug(oEntry.Value)
	Next
	oCount = myArrayList.Count
	If oCount = 1 Then
		arvo = myArrayList(0)
	Else
		Try
			arvo = myArrayList(oIns)
		Catch
			arvo = 0 '"N/A"
		End Try
	End If
	Return arvo
End Function



Function ParamsDict(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator)
	Dim oADoc As AssemblyDocument = TryCast(ThisDoc.Document, Inventor.AssemblyDocument)
	Dim oOccs As ComponentOccurrences = oADoc.ComponentDefinition.Occurrences
	Dim oDict As New Dictionary(Of String, Object)
	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			ItemName = Item.Name
			If ItemName.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
					Arvo = Item.Value * 10
				ElseIf Item.Units = "deg" Then
					Arvo = Item.Value * 180 / PI
				Else
					Arvo = Item.Value
				End If
				Try
					oDict.Add(Item.Name & "#" & oRefDoc.displayname, Arvo)
				Catch
				End Try
			End If
		Next
	Next
	For Each Item In ThisApplication.ActiveDocument.ComponentDefinition.Parameters.UserParameters 'päätason parametrit mukaan -onhan päivittynyt tässä vaiheessa
		ItemName = Item.Name
		If ItemName.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
			If Item.Units = "mm" Then 'yksiköille korjaus cm/rad
				Arvo = Item.Value * 10
			ElseIf Item.Units = "deg" Then
				Arvo = Item.Value * 180 / PI
			Else
				Arvo = Item.Value
			End If
			Try
				oDict.Add(Item.Name & "#" & "000", Arvo)
			Catch
			End Try
		End If
	Next
	Return oDict
End Function