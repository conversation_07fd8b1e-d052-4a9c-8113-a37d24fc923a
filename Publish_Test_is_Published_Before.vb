Sub main()

oFileList = GetAllFileNamesPartNumber({"C:\Vault_BH\Designs\Proj" } , "Hash")
'
Dim FilteredPNumArrayList As New ArrayList
For Each oKey In oFileList.Keys
	Massaton = True
	Try
		oMassa = SoveliaSearh(oKey, "WEIGHT")
		If oMassa>0.1 Then
			Massaton = False
		End If
	Catch
	End Try
	If Massaton Then
		FilteredPNumArrayList.Add(oKey)
	End If
Next

For Each oVal In FilteredPNumArrayList
	oDoc = ThisApplication.Documents.Open(oFileList(oVal))
	Publish(oFileList(oVal))
	Publish(ThisApplication.ActiveDocument.FullFileName)
	ThisApplication.CommandManager.ControlDefinitions.Item("AppFileCloseCmd").Execute
Next

'	For Each oVal In oFileList
'		Publish(oVal)
'	Next

'Publish(ThisDoc.PathAndFileName(True))

End Sub
Function SoveliaSearh(SearchID As String, resultCriteria As String)
	Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
	_addInInterface = addin.Automation
	Dim searchCriteria As New List(Of String)()
	Dim resultLimit As Integer
	searchCriteria.Add("DocID:" & SearchID)
	resultLimit = 1
	Dim task As System.Threading.Tasks.Task(Of String) = _addInInterface.Search(searchCriteria, resultCriteria, resultLimit)
	Dim timeout As Integer = 15000
	Dim oResult As String
	If task.Wait(timeout) Then
		oResult = task.Result
		' Use the result
	Else
		' Timeout occurred
		Throw New TimeoutException("The search operation timed out.")
	End If
	'	Dim awaiter As System.Runtime.CompilerServices.TaskAwaiter(Of String) = task.GetAwaiter()
	'	awaiter.GetResult()
	'Dim oResult As String = awaiter.GetResult()
	Dim TxtData() As String
	Try
		TxtData = oResult.Split(";"c)
	Catch
		Logger.Debug("Error in search :" & SearchID)
	End Try
	'Logger.Debug(SearchID & TxtData(0))
	Dim TulosData() As String = TxtData(0).Split(":"c)
	Try
		oTulos = CDbl(TulosData(1))
	Catch
		oTulos = 0
	End Try
	Return oTulos
End Function
Function Publish(oFileName As String)
	Try
		Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
		_addInInterface = addin.Automation
		_addInInterface.Publish(oFileName)
		Logger.Debug("Publish done :" & oFileName)
	Catch ex As Exception
		System.Windows.Forms.MessageBox.Show(ex.Message)
		Exit Function
	End Try
End Function
Function GetAllFileNamesPartNumber(Filtteri() As String, Optional oType As String = "")
	Dim myFileArrayList As New ArrayList : teksti = ""
	Dim myPNumArrayList As New ArrayList
	Dim FileVsPNum As Hashtable = New Hashtable
	For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
		Try
			For Each oFiltteri In Filtteri
				oFullName = oRefDoc.FullFileName
				If Not myFileArrayList.Contains(oFullName) And oFullName.Contains(oFiltteri) Then
					myFileArrayList.Add(oFullName)
					partNumber = oRefDoc.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value

					If Len(partNumber) >2 Then
						myPNumArrayList.Add(partNumber)


						If oType = "Hash" Then
							Try
								FileVsPNum.Add(partNumber, oFullName)
							Catch
							End Try
						End If
					End If


				End If 'pnume len
			Next
		Catch ex As Exception
			Logger.Debug("Error :" & ex.Message)
		End Try
	Next
	If oType = "PartNumber" Then
		Return myPNumArrayList
	ElseIf oType = "Hash" Then
		Return FileVsPNum
	Else
		Return myFileArrayList
	End If
End Function
