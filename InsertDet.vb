AddReference "System.Windows.Forms"
AddReference "System.Drawing"
Imports System.Windows.Forms
Imports System.Drawing
Imports System.IO

Sub Main()
oGeneratedDetModels = ReturnListDetmodels()

' Create form
Dim form As New Form
form.Text = "Insert Detailed Frames"
form.Size = New System.Drawing.Size(300, 400)
form.StartPosition = FormStartPosition.CenterScreen

' Create a CheckedListBox for multiple selection
Dim checkedListBox As New CheckedListBox()
checkedListBox.Location = New System.Drawing.Point(50, 50)
checkedListBox.Size = New System.Drawing.Size(200, 250)
checkedListBox.CheckOnClick = True

' Add "Select All" checkbox
Dim selectAllCheckBox As New CheckBox()
selectAllCheckBox.Text = "Select All"
selectAllCheckBox.Location = New System.Drawing.Point(50, 20)

' Add items to the CheckedListBox
For i As Integer = 1 To SDA_pcs
	checkedListBox.Items.Add(i)
Next

' Handle Select All checkbox
AddHandler selectAllCheckBox.CheckedChanged, Sub(sender, e)
For i As Integer = 0 To checkedListBox.Items.Count - 1
	checkedListBox.SetItemChecked(i, selectAllCheckBox.Checked)
Next
End Sub

' Create OK button
Dim okButton As New Button
okButton.Text = "OK"
okButton.Location = New System.Drawing.Point(50, 320)
okButton.DialogResult = DialogResult.OK

' Create Cancel button
Dim cancelButton As New Button
cancelButton.Text = "Cancel"
cancelButton.Location = New System.Drawing.Point(150, 320)
cancelButton.DialogResult = DialogResult.Cancel

' Add controls to the form
form.Controls.Add(selectAllCheckBox)
form.Controls.Add(checkedListBox)
form.Controls.Add(okButton)
form.Controls.Add(cancelButton)

' Set the form's Accept and Cancel buttons
form.AcceptButton = okButton
form.CancelButton = cancelButton

' Show the form as a dialog and capture the result
Dim result As DialogResult = form.ShowDialog()

' Handle the result
If result = DialogResult.OK Then
	' Get selected values
	Dim selectedValues As New List(Of Integer)
	For i As Integer = 0 To checkedListBox.Items.Count - 1
		If checkedListBox.GetItemChecked(i) Then
			selectedValues.Add(CInt(checkedListBox.Items(i)))
		End If
	Next

	If selectedValues.Count > 0 Then
		Try
			NumberListEdit = SharedVariable("NumberListEdit")
		Catch
			BUfile = ThisDoc.Path & "\data.csv"
			Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
			NumberListEdit = ImportCSVToDictionary(BUfile)
			SharedVariable("NumberListEdit") = NumberListEdit
		End Try

		For Each selectedValue In selectedValues
			Dim oGeneratedDetModelsFiltered As New ArrayList
			For Each kvp As KeyValuePair(Of String, String()) In NumberListEdit
				If kvp.Key.StartsWith("f" & selectedValue & "/") Then
					oGeneratedDetModelsFiltered.Add(kvp.Value(3))
				End If
			Next

			OpenPlaceConstrain("f" & selectedValue, oGeneratedDetModelsFiltered, "ConstraintsDetailModels")

		Next
	End If
End If

ThisApplication.ActiveDocument.ObjectVisibility.AllWorkFeatures = False
ThisApplication.ActiveDocument.ObjectVisibility.UCSTriads = False

End Sub

Function OpenPlaceConstrain(oTargetOccName As String, oInsertComps As ArrayList, RuleName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}") : Dim iLogic As Object = addIn.Automation
	Dim map As Inventor.NameValueMap = ThisApplication.TransientObjects.CreateNameValueMap()
	subAsmOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oTargetOccName)
	oFullName = subAsmOcc.ReferencedFileDescriptor.FullFileName
	oDoc = ThisApplication.Documents.Open(oFullName, True)
	Dim oMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix
	Dim newOccurrence As ComponentOccurrence

	For Each oInsertComp In oInsertComps
		newOccurrence = oDoc.ComponentDefinition.Occurrences.Add(oInsertComp, oMatrix)
	Next

	map.Add("TotalFrames", SDA_pcs)
	map.Add("RoofPcs", e_roof_pcs)

	If e_ridge_width>10 Then
		map.Add("RidgePcs", 1)
	Else
		map.Add("RidgePcs", 0)
	End If

	map.Add("oGeneratedDetModels", oInsertComps)

	iLogicVb.RunRule(oTargetOccName, RuleName, map)
End Function
Function ReturnListDetmodels()
	Dim folderPath As String = ThisDoc.Path
	Dim DetModelList As New ArrayList
	Dim fileExtension As String = "*.iam"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)
	For Each oFile As String In files
		If oFile.Contains("adet") Then
			DetModelList.Add(oFile)
		End If
	Next
	Return DetModelList
End Function
Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function