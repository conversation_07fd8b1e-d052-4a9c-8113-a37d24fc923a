Imports System.IO
Imports System.Text
Imports System.Windows.Forms
Imports System.Collections
Imports System.Text.RegularExpressions
AddReference "C:\Vault_BH\ExternalRules_BH_Vault\MyTableForm.dll"
Sub Main()
Dim startTime As DateTime = Now
Dim timeDebug As Boolean = True

' Define filters
Dim oWpFilters As String() = {"windbrace", "wallbrace", "gableendbrace" }
Dim consList As Dictionary(Of String, Object) = GetConsInfo(oWpFilters)
'Dim koe As String = PrinttaaDict(consList)

If timeDebug Then Logger.Debug("ConsList: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))
Dim allModules As ArrayList = GetAllModules()

If timeDebug Then Logger.Debug("AllModules: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))
Dim consCollection As List(Of Tuple(Of String, List(Of String))) = AnalyzeConsDict(consList, "result", allModules)
SharedVariable("ConsCollection") = consCollection

If timeDebug Then Logger.Debug("ConsCollection: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))
Dim apuolenSamat As Hashtable = ListPrintResult(consCollection)
If timeDebug Then Logger.Debug("ApuolenSamat: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))
Dim uniqueFramesList As ArrayList = UniqueFrame(consCollection)

If timeDebug Then Logger.Debug("UniqueFramesList: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))

' Point side

Dim pointtiHash As Hashtable = PointHash({"wind_brace_", "joint_brace" }, "Mir")

If timeDebug Then Logger.Debug("PointtiHash: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))
Dim jointtiHash As Hashtable = JointHash()

If timeDebug Then Logger.Debug("JointtiHash: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))

Dim pointCollection As List(Of Tuple(Of String, List(Of String))) = ComparePointHash(jointtiHash, pointtiHash, allModules)
AddEndBSide(pointCollection)

SharedVariable("PointCollection") = pointCollection
If timeDebug Then Logger.Debug("PointCollection: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))

Dim similarKeys As Hashtable = FindSimilarKeys(consCollection, pointCollection)

If timeDebug Then Logger.Debug("SimilarKeys: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))

Dim numberList As Dictionary(Of String, String()) = CalcModuleNumbers(allModules, apuolenSamat, similarKeys)
If timeDebug Then Logger.Debug("NumberList: " & Round((Now().Subtract(startTime)).TotalSeconds, 1))

Dim numberListEdit As Dictionary(Of String, String()) = ShowEditlist(numberList)
SharedVariable("NumberListEdit") = numberListEdit
ExportDictionaryToCSV(numberListEdit, ThisDoc.Path & "\data.csv")

InitialRun = False
End Sub

Function AddEndBSide(pointCollection As List(Of Tuple(Of String, List(Of String))))

	If isEndSame Then
		End1 = "end"
		End2 = "end"
		GableEnd1 = "gableend"
		GableEnd2 = "gableend"
	Else
		End1 = "end1"
		End2 = "end2"
		GableEnd1 = "gableend1"
		GableEnd2 = "gableend2"
	End If

	AddToPointCollection(pointCollection, "f1/s", End1)
	AddToPointCollection(pointCollection, "f" & totalFramePcs & "/s", End2)

	For i = 1 To e_roof_pcs
		AddToPointCollection(pointCollection, "f1/p" & i & "k", End1 & "_" & i)
		AddToPointCollection(pointCollection, "f1/p" & i & "k", GableEnd1 & "_" & i)
		AddToPointCollection(pointCollection, "f" & totalFramePcs & "/p" & i & "k", End2 & "_" & i)
		AddToPointCollection(pointCollection, "f" & totalFramePcs & "/p" & i & "k", GableEnd2 & "_" & i)
		AddToPointCollection(pointCollection, "f" & GableEndBraceInsertFrame & "/p" & i & "k", GableEnd1 & "_" & i)
		AddToPointCollection(pointCollection, "f" & totalFramePcs - GableEndBraceInsertFrame + 1 & "/p" & i & "k", GableEnd2 & "_" & i)
	Next

	If e_ridge_width > 0 Then
		AddToPointCollection(pointCollection, "f1/h", End1)
		AddToPointCollection(pointCollection, "f" & totalFramePcs & "/h", End2)
		AddToPointCollection(pointCollection, "f" & GableEndBraceInsertFrame & "/h", GableEnd1)
		AddToPointCollection(pointCollection, "f" & totalFramePcs - GableEndBraceInsertFrame + 1 & "/h", GableEnd2)
	End If
End Function

Function PrinttaaDict(oDictoOcc As Dictionary(Of String, Object))
	' Create a variable to store the entire dictionary output as a string
	Dim output As String = ""
	' Iterate over each item in the dictionary
	For Each kvp As KeyValuePair(Of String, Object) In oDictoOcc
		output &= "Key: " & kvp.Key & vbCrLf
		output &= "Values:" & vbCrLf
		' Check if the value is an array or list of strings
		Dim values As String() = TryCast(kvp.Value, String())
		' If the value is a string array, output each element
		If values IsNot Nothing Then
			For Each Val As String In values
				output &= "  - " & Val & vbCrLf
			Next
		Else
			' If not an array, directly print the value
			output &= "  - " & kvp.Value.ToString() & vbCrLf
		End If
	Next
	' Output the complete dictionary to the logger
	Logger.Debug(output)
	Return output
End Function

Function ShowEditlist(NumberSchema As Dictionary(Of String, String()))
	Using mf As New MyTableForm.MyForm
		mf.Text = "Module numbering"
		Dim dgv As DataGridView = mf.dgvTable
		' Set columns
		Dim c1 As Integer = dgv.Columns.Add("MyColumn1", "Module")
		Dim c2 As Integer = dgv.Columns.Add("MyColumn2", "Number")
		Dim c3 As Integer = dgv.Columns.Add("MyColumn3", "Suffix")
		Dim c4 As Integer = dgv.Columns.Add("MyColumn4", "is generated")
		Dim c5 As Integer = dgv.Columns.Add("MyColumn5", "Model")
		Dim c6 As Integer = dgv.Columns.Add("MyColumn6", "Saw Model")
		Dim c7 As Integer = dgv.Columns.Add("MyColumn7", "Notes")
		Dim c8 As Integer = dgv.Columns.Add("MyColumn8", "UCS")

		' Add data from NumberSchema to DataGridView
		Dim n As Integer = 0
		For Each oVal In NumberSchema.Keys
			Dim riviN As Integer = dgv.Rows.Add()
			dgv.Rows(n).Cells(0).Value = oVal
			dgv.Rows(n).Cells(0).ReadOnly = True ' Key should be read-only
			dgv.Rows(n).Cells(1).Value = NumberSchema(oVal)(0)
			dgv.Rows(n).Cells(2).Value = NumberSchema(oVal)(1)
			dgv.Rows(n).Cells(3).Value = NumberSchema(oVal)(2)
			dgv.Rows(n).Cells(4).Value = NumberSchema(oVal)(3)
			dgv.Rows(n).Cells(5).Value = NumberSchema(oVal)(4)
			dgv.Rows(n).Cells(6).Value = NumberSchema(oVal)(5)
			dgv.Rows(n).Cells(7).Value = NumberSchema(oVal)(6)
			n += 1
		Next

		' Show the dialog
		If mf.ShowDialog() = DialogResult.OK Then
			' Update the NumberSchema dictionary with values from DataGridView
			For Each row As DataGridViewRow In dgv.Rows
				If Not Row.IsNewRow Then
					Dim key As String = Row.Cells(0).Value.ToString()
					Dim newValues As String() = {
					Row.Cells(1).Value.ToString(),
					Row.Cells(2).Value.ToString(),
					Row.Cells(3).Value.ToString(),
					Row.Cells(4).Value.ToString(),
					Row.Cells(5).Value.ToString(),
					Row.Cells(6).Value.ToString(),
					Row.Cells(7).Value.ToString()
					}

					' Update dictionary
					If NumberSchema.ContainsKey(key) Then
						NumberSchema(key) = newValues
					End If
				End If
			Next

			' Optional: Display updated dictionary to verify changes
			For Each key In NumberSchema.Keys
				Logger.Debug("Key: " & key & ", Values: " & String.Join(", ", NumberSchema(key)))
			Next
		End If
	End Using
	Return NumberSchema
End Function
Function AnalyzeConsDict(oDict As Dictionary(Of String, Object), oResultType As String, AllModules As ArrayList)
	Dim arrayList As New ArrayList()
	Dim Kentta1 As String: Dim Kentta2 As String
	Dim Arvo1 As String: Dim Arvo2 As String

	For Each oVal In oDict.Values
		Try
			Cons1 = oVal(1).Split(New Char() {"/"c })
			Kentta1 = Cons1(0) & "/" & Cons1(1)
			Arvo1 = Cons1(2)
			arrayList.Add(Tuple.Create(Kentta1, Arvo1))
		Catch ex As Exception

		End Try
		Try
			Cons2 = oVal(2).Split(New Char() {"/"c })
			Kentta2 = Cons2(0) & "/" & Cons2(1)
			Arvo2 = Cons2(2)
			arrayList.Add(Tuple.Create(Kentta2, Arvo2))
		Catch ex As Exception
		End Try
	Next


	' Step 2: Group items by the first column
	Dim groupedData As New Dictionary(Of String, List(Of String))()
	' Populate the dictionary
	For Each item As Tuple(Of String, String) In arrayList
		Dim key As String = item.Item1
		Dim value As String = item.Item2

		If Not groupedData.ContainsKey(key) Then
			groupedData(key) = New List(Of String)()
		Else
			koe = 1
		End If
		' Add the value to the list associated with the key
		groupedData(key).Add(value)
	Next
	' Step 3: Display the grouped result
	Dim resultList As New List(Of Tuple(Of String, List(Of String)))()

	For Each kvp As KeyValuePair(Of String, List(Of String)) In groupedData
		Dim key As String = kvp.Key
		Dim values As List(Of String) = kvp.Value
		resultList.Add(Tuple.Create(key, values))
		'Logger.Debug("Key: " & key & ", Values: " & String.Join(", ", values))
	Next

	Dim existingItems As New HashSet(Of String)()
	For Each tupleItem In resultList
		existingItems.Add(tupleItem.Item1)
	Next

	For Each moduleItem As String In AllModules
		If Not existingItems.Contains(moduleItem) Then
			' Add the missing item to resultList with the "plain" value
			Dim newList As New List(Of String)
			oSplit = moduleItem.Split(New Char() {"/"c })
			newList.Add(oSplit(1) & "_plain")
			resultList.Add(New Tuple(Of String, List(Of String))(moduleItem, newList))
		End If
	Next

	' Step 4: Check differences between groups
	Dim uniqueKeys As New List(Of String)()
	Dim duplicateGroups As New List(Of String)()

	For i As Integer = 0 To resultList.Count - 1
		Dim firstGroup = resultList(i)
		Dim isUnique = True

		For j As Integer = i + 1 To resultList.Count - 1
			Dim secondGroup = resultList(j)

			If firstGroup.Item2.SequenceEqual(secondGroup.Item2) Then
				duplicateGroups.Add(String.Format("{0} and {1} are same", firstGroup.Item1, secondGroup.Item1))
				isUnique = False
			End If
		Next

		If isUnique Then
			uniqueKeys.Add(firstGroup.Item1)
		End If
	Next

	For i As Integer = 0 To resultList.Count - 1 'duplikaatti datojen poisto
		Dim uniqueList As New List(Of String)()
		Dim seen As New HashSet(Of String)()

		' Iterate through inner list and add only unique values
		For Each item As String In resultList(i).Item2
			If Not seen.Contains(item) Then
				uniqueList.Add(item)
				seen.Add(item)
			End If
		Next
		' Replace the old list with the new unique list
		resultList(i) = New Tuple(Of String, List(Of String))(resultList(i).Item1, uniqueList)
	Next


	If oResultType = "result" Then
		resultList.Sort(Function(a, b) NaturalSort(a.Item1, b.Item1))
		Return resultList
	Else
		uniqueKeys.Sort
		Return uniqueKeys
	End If
End Function
Function NaturalSort(x As String, y As String) As Integer
	Dim regex As New System.Text.RegularExpressions.Regex("(\D+)(\d+)")
	Dim matchX = regex.Match(x)
	Dim matchY = regex.Match(y)
	' Compare string parts (non-numeric)
	Dim stringPartCompare As Integer = String.Compare(matchX.Groups(1).Value, matchY.Groups(1).Value)
	If stringPartCompare <> 0 Then
		Return stringPartCompare
	End If
	' Compare numeric parts if string parts are the same
	Dim numX As Integer = Integer.Parse(matchX.Groups(2).Value)
	Dim numY As Integer = Integer.Parse(matchY.Groups(2).Value)
	Return numX.CompareTo(numY)
End Function
Function CalcModuleNumbers(AllModules As ArrayList, ApuolenHash As Hashtable, oSimilarKeys As Hashtable)
	Dim GeneratedPNums As New Dictionary(Of String, String())
	Dim originalCount As Integer = AllModules.Count 'duplikoidaan 

	For i As Integer = originalCount - 1 To 0 Step -1
		' Append mirrored version of each item to the list
		oModuleName = AllModules(i)
		If Not oModuleName.contains("/h") Then
			AllModules.Add(oModuleName & "Mir")
		End If
	Next
	S1laskuri = 1 : K1laskuri = 1 : K2laskuri = 1 : K3laskuri = 1 : K4laskuri = 1
	K5laskuri = 1 : K6laskuri = 1 : K7laskuri = 1 : K8laskuri = 1 : H1laskuri = 1
	H1laskuri = 1

	For Each oVal In AllModules

		If oVal.contains("Mir") And oSimilarKeys.ContainsKey(oVal) 'peilauksessa osuma
			OnkoUusi = False
		ElseIf Not oVal.contains("Mir") And ApuolenHash.ContainsKey(oVal) 'sama a -puolella "plain"
			OnkoUusi = False
		Else
			OnkoUusi = True
		End If

		If OnkoUusi Then 'normijuoksutus ellei ole listassa 'Not ApuolenHash.ContainsKey(oVal) Or
			If oVal.contains("p1k") Then
				oNum = "K1" & K1laskuri : K1laskuri += 1
			ElseIf oVal.contains("p2k") Then
				oNum = "K2" & K2laskuri : K2laskuri += 1
			ElseIf oVal.contains("p3k") Then
				oNum = "K3" & K3laskuri : K3laskuri += 1
			ElseIf oVal.contains("p4k") Then
				oNum = "K4" & K4laskuri : K4laskuri += 1
			ElseIf oVal.contains("p5k") Then
				oNum = "K5" & K5laskuri : K5laskuri += 1
			ElseIf oVal.contains("p6k") Then
				oNum = "K6" & K6laskuri : K6laskuri += 1
			ElseIf oVal.contains("p7k") Then
				oNum = "K7" & K7laskuri : K7laskuri += 1
			ElseIf oVal.contains("p8k") Then
				oNum = "K8" & K8laskuri : K8laskuri += 1
			ElseIf oVal.contains("s") Then
				oNum = "S1" & S1laskuri : S1laskuri += 1
			ElseIf oVal.contains("h") Then
				oNum = "H1" & H1laskuri : H1laskuri += 1
			End If

		Else 'vastaavuus löytynyt
			If oVal.contains("Mir") Then
				VastaavusIDpo = oSimilarKeys(oVal)
				oNum = GeneratedPNums(VastaavusIDpo)(0)
			Else ' lähinnä plain haku
				VastaavusIDco = ApuolenHash(oVal)
				Try : oNum = GeneratedPNums(VastaavusIDco)(0) : Catch : oNum = "" : End Try
			End If
		End If

		GeneratedPNums.Add(oVal, {oNum, iProperties.Value("Project", "Project"), "no", "model", "sawmodel", "obs", GetUcsInfoByModule(oVal) })
		'Logger.Debug(oVal & " # " & oNum)
	Next
	Return GeneratedPNums
End Function
Function GetUcsInfoByModule(oModule As String)
	Try
		ConsCollection = SharedVariable("ConsCollection") 'oCollection As List(Of Tuple(Of String, List(Of String)))
		PointCollection = SharedVariable("PointCollection")
	Catch
		Logger.Debug("No shared variable for ConsCollection / PointCollection ")
	End Try
	Dim listString As String = ""
	If oModule.Contains("Mir") Then 'B peilaus puoli ilma rajoitteita
		For Each entry In PointCollection
			Dim oKey As String = entry.Item1 & "Mir" 'itse listassa näitä eritelty
			If oKey = oModule Then
				Dim oValueList As List(Of String) = entry.Item2
				listString = String.Join("#", oValueList.Distinct().OrderBy(Function(x) x))
				Exit For
			End If
		Next
	Else 'A puoli rajoitteineen
		For Each entry In ConsCollection
			Dim oKey As String = entry.Item1
			If oKey = oModule Then
				Dim oValueList As List(Of String) = entry.Item2
				listString = String.Join("#", oValueList.Distinct().OrderBy(Function(x) x))
				Exit For
			End If
		Next
	End If
	Return listString
End Function
Function ModifyValueInDictKey(ByRef dict As System.Collections.Generic.Dictionary(Of String, String()), key As String, newValue As String, Optional oldValue As String = Nothing)
	'ModifyValueInDictKey(myDict, "f1/p1k", "K12","K11")
	'ModifyValueInDictKey(myDict, "f1/s", "newValue3")
	If dict.ContainsKey(key) Then
		' Get the current array of values
		Dim currentArray As String() = dict(key)
		If oldValue Is Nothing Then
			' If oldValue is not provided, add the new value
			Dim updatedArray As String() = currentArray.Concat({newValue }).ToArray()
			dict(key) = updatedArray
		Else
			' If oldValue is provided, edit the existing value
			For i As Integer = 0 To currentArray.Length - 1
				If currentArray(i) = oldValue Then
					currentArray(i) = newValue
					Exit For ' Exit after changing the first occurrence
				End If
			Next
			dict(key) = currentArray
		End If
	End If
End Function
Function ListPrintResult(collection1 As List(Of Tuple(Of String, List(Of String))))

	Dim similarEntries As New Hashtable()
	' Dictionary to track seen Item2 lists (for comparison).
	Dim seenLists As New Dictionary(Of String, String)
	' Iterate over collection1 to find similar lists.
	For Each entry In collection1
		Dim key As String = entry.Item1
		Dim valueList As List(Of String) = entry.Item2
		' Create a unique representation of the list (use Join to create a comparable string).
		Dim listKey As String = String.Join(",", valueList)
		' Check if this listKey already exists in seenLists.
		If seenLists.ContainsKey(listKey) Then
			' If it exists, map the current key to the first-found representative.
			Dim originalKey As String = seenLists(listKey)
			similarEntries.Add(key, originalKey)
		Else
			' If it doesn't exist, add the current listKey to the seenLists with the current key.
			seenLists.Add(listKey, key)
		End If
	Next
	' Output the hashtable of direct similar mappings.
	For Each de As DictionaryEntry In similarEntries
		Dim currentKey As String = de.Key
		Dim representativeKey As String = de.Value
		Logger.Debug(currentKey & " maps to: " & representativeKey)
	Next
	Return similarEntries
End Function

Function FindSimilarKeys(collection1 As List(Of Tuple(Of String, List(Of String))), collection2 As List(Of Tuple(Of String, List(Of String))))
	Dim similarKeys As New List(Of String)
	Dim oHashtable As Hashtable = New Hashtable

	For Each tuple1 In collection1
		For Each tuple2 In collection2
			' Compare the values (Item2) of both tuples
			If CompareValues(tuple1.Item2, tuple2.Item2) Then
				Try
					oHashtable.Add(tuple2.Item1 & "Mir", tuple1.Item1)
				Catch
					If Not oHashtable.ContainsKey(tuple2.Item1) Then
						oHashtable.Add(tuple2.Item1, tuple1.Item1)
					End If
				End Try
				similarKeys.Add(tuple1.Item1 & ", mir : " & tuple2.Item1)
			End If
		Next
	Next
	If isEndSame Then
		AddManualSimilarKeys(oHashtable)
	End If

	If similarKeys.Count > 0 Then
		Return oHashtable
	End If
End Function
Function AddManualSimilarKeys(ByRef oHashtable As Hashtable)
	For i = 1 To e_roof_pcs
		oKey = "f" & totalFramePcs & "/p" & i & "kMir"
		If Not oHashtable.ContainsKey(oKey) Then
			oHashtable.Add(oKey, "f1/p" & i & "k")
		End If
	Next
End Function

Function CompareValues(list1 As List(Of String), list2 As List(Of String)) As Boolean
	' Convert both lists to sets to remove duplicates and ignore order
	Dim set1 As HashSet(Of String) = New HashSet(Of String)(list1)
	Dim set2 As HashSet(Of String) = New HashSet(Of String)(list2)
	' Compare the sets
	Return set1.SetEquals(set2)
End Function
Function ComparePointHash(JointHash As Hashtable, PointHash As Hashtable, AllModules As ArrayList, Optional Resultti As String = "")
	Dim threshold As Double = 10.0
	Laskuri0 = 0 : Laskuri1 = 0
	Dim arrayList As New ArrayList()

	For Each key1 In JointHash.Keys
		wp1 = JointHash(key1)
		' Get the coordinates of the first work point
		Dim coord1 As Point = wp1.Point
		' Inner loop to compare with every other work point
		For Each key2 In PointHash.Keys
			wp2 = PointHash(key2)
			' Get the coordinates of the second work point
			Dim coord2 As Point = wp2.Point
			' Calculate the distance between the two work points
			distance = coord1.DistanceTo(coord2)
			' Check if the distance is within the threshold
			If distance <= threshold Then
				ResulttiTest += key1 & " vs " & key2 & " dist: " & distance
				'Logger.Debug("WorkPoint 1: " & key1 & vbLf & " and WorkPoint " & key2 & vbLf & "Distance: " & distance)
				Laskuri0 += 1
				If key1.contains(": Center Point") Then
					arvo = key1.Replace(": Center Point", "")
					Cons1 = arvo.Split(New Char() {"/"c })
				Else
					arvo = key2.Replace(": Center Point", "")
					Cons1 = arvo.Split(New Char() {"/"c })
				End If
				Dim Kentta1 As String = Cons1(0) & "/" & Cons1(1)
				Dim Arvo1 As String = Cons1(2)
				arrayList.Add(Tuple.Create(Kentta1, Arvo1))
			Else
				'Logger.Debug("WorkPoint " & key1 & " and WorkPoint " & key2 & " are not near each other. Distance: " & distance)
				Laskuri1 += 1
			End If
		Next
	Next

	Dim groupedData As New Dictionary(Of String, List(Of String))()
	For Each item As Tuple(Of String, String) In arrayList
		Dim key As String = item.Item1
		Dim value As String = item.Item2

		If Not groupedData.ContainsKey(key) Then
			groupedData(key) = New List(Of String)()
		End If
		' Add the value to the list associated with the key
		groupedData(key).Add(value)
	Next

	Dim resultList As New List(Of Tuple(Of String, List(Of String)))()

	For Each kvp As KeyValuePair(Of String, List(Of String)) In groupedData
		Dim key As String = kvp.Key.Replace("Mir", "")
		Dim values As List(Of String) = kvp.Value
		resultList.Add(Tuple.Create(key, values))
	Next


	Dim existingItems As New HashSet(Of String)()
	For Each tupleItem In resultList
		existingItems.Add(tupleItem.Item1)
	Next

	For Each moduleItem As String In AllModules
		' If moduleItem is not in resultList
		If Not existingItems.Contains(moduleItem) Then
			' Add the missing item to resultList with the "plain" value
			Dim newList As New List(Of String)
			oSplit = moduleItem.Split(New Char() {"/"c })
			newList.Add(oSplit(1) & "_plain")
			resultList.Add(New Tuple(Of String, List(Of String))(moduleItem, newList))
		End If
	Next

	For i As Integer = 0 To resultList.Count - 1 'duplikaatit pois
		Dim uniqueList As New List(Of String)()
		Dim seen As New HashSet(Of String)()
		' Iterate through inner list and add only unique values
		For Each item As String In resultList(i).Item2
			If Not seen.Contains(item) Then
				uniqueList.Add(item)
				seen.Add(item)
			End If
		Next
		' Replace the old list with the new unique list
		resultList(i) = New Tuple(Of String, List(Of String))(resultList(i).Item1, uniqueList)
	Next


	If Resultti = "text" Then
		Return ResulttiTest
	Else
		resultList.Sort
		Return resultList
	End If
End Function
Function PointHash(sWPFilter As String(), oOccFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	' Helper function to modify wind brace keys


	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		If Not thisOcc.Suppressed Then
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					For Each oFilter As String In sWPFilter
						If InStr(1, currentWP.Name, oFilter) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)

							' Create and modify key if needed
							Dim hashKey As String = thisOcc.Name & "/" & oAsmPoint.name
							hashKey = ModifyWindBraceKey(hashKey)

							If Debuggaus Then
								oDataTeksti += hashKey & " : [" & X & "," & Y & "," & Z & "]" & vbLf
							End If
							oHashtable.Add(hashKey, oAsmPoint)
						End If
					Next
				Next
			End If

			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count
					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed And thisSubOcc.Name.Contains(oOccFilter) And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								For Each oFilter As String In sWPFilter
									If InStr(1, currentSubWP.Name, oFilter) Then
										Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
										X = oAsmPoint.point.X * 10
										Y = oAsmPoint.point.Y * 10
										Z = oAsmPoint.point.Z * 10

										' Create and modify key if needed
										Dim hashKey As String = thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name
										hashKey = ModifyWindBraceKey(hashKey)

										If Debuggaus Then
											oDataTeksti += hashKey & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
										End If
										oHashtable.Add(hashKey, oAsmPoint)
									End If
								Next
							Next
						End If

						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											For Each oFilter As String In sWPFilter
												If InStr(1, currentSubSubWP.Name, oFilter) Then
													Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
													X = oAsmPoint.point.X * 10
													Y = oAsmPoint.point.Y * 10
													Z = oAsmPoint.point.Z * 10

													' Create and modify key if needed
													Dim hashKey As String = thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name
													hashKey = ModifyWindBraceKey(hashKey)

													If Debuggaus Then
														oDataTeksti += hashKey & " : [" & X & "," & Y & "," & Z & "]" & vbLf
													End If
													oHashtable.Add(hashKey, oAsmPoint)
												End If
											Next
										Next
									End If
								End If
							Next
						End If
					End If
				Next
			End If
		End If
	Next

	Return oHashtable
End Function

Function ModifyWindBraceKey(key As String) As String
	If key.Contains("wind_brace_") Then
		Dim parts As String() = key.Split("/"c)
		If parts.Length >= 2 Then
			' Get prefix from second part (removing Mir if present)
			Dim prefix As String = parts(1).Replace("Mir", "")
			' Modify the last part
			parts(parts.Length - 1) = prefix & "_" & parts(parts.Length - 1)
			Return String.Join("/", parts)
		End If
	End If
	Return key
End Function
Function JointHash()
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition = oAsmDoc.ComponentDefinition
	'Dim BraceHash As Hashtable = New Hashtable : Dim CrossarmHash As Hashtable = New Hashtable
	Dim JointPartHash As Hashtable = New Hashtable : Dim FrameHash As Hashtable = New Hashtable
	Dim threshold As Double = 1.0
	oCounterBrace = 0 : oCountercrossarm = 0
	sWPFilter = {"Start", "End" }
	oDataTeksti = ""
	CounterCrossarm2 = 0 : CounterCrossarm3 = 0
	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'skip suppressed components
		If Not thisOcc.Suppressed And thisOcc.Constraints.Count = 0 And Not TypeOf thisOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisOcc.Definition Is WeldsComponentDefinition Then
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					Try : oCategory = iProperties.Value(thisOcc.Name, "Summary", "category") : Catch : oCategory = "" : End Try
					If Len(oCategory) > 2 Then

						If currentWP.Name = sWPFilter(0) Or currentWP.Name = sWPFilter(1) Then
							Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
							X = Round(oAsmPoint.point.X * 10, 1)
							Y = Round(oAsmPoint.point.Y * 10, 1)
							Z = Round(oAsmPoint.point.Z * 10, 1)
							JointPartHash.Add(thisOcc.Name & "#" & currentWP.Name & "#" & oCounterBrace, oAsmPoint)
							oCounterBrace += 1
						End If
						oDataTeksti += thisOcc.Name & "\" & currentWP.Name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
					End If
				Next
			End If 'defType
		End If 'suppressed
	Next
	Return JointPartHash
End Function
Function UniqueFrame(oResult)
	Dim myArrayList As New ArrayList

	For Each tupleItem In oResult
		oVal = tupleItem.Item1
		oList = tupleItem.Item2
		oListCount = oList.count

		If oList.count>1 Then 'tarkista tarviiko vielä tarkistaa että _plain -tieto
			KeySplit = oVal.Split(New Char() {"/"c })
			If Not myArrayList.Contains(KeySplit(0)) Then
				myArrayList.Add(KeySplit(0))
			End If
		End If
	Next

	myArrayList.Sort(New AlphanumComparator())
	'myArrayList.sort
	Logger.Debug("Total frames:" & myArrayList.Count) 'tarkista ettei plain sotke tätä!!

	Return myArrayList
End Function
Function GetConsInfo(Filtteri() As String)
	Dim oDictConsName As New Dictionary(Of String, Object)
	Dim oDict As New Dictionary(Of String, Object) : laskuri = 0
	Dim oDictoOcc As New Dictionary(Of String, Object)

	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences

		If Not TypeOf oOccurrence.Definition Is VirtualComponentDefinition And Not TypeOf oOccurrence.Definition Is WeldsComponentDefinition Then
			oFullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			oCategory = iProperties.Value(oOccurrence.Name, "Summary", "category")

			For Each oFilter In Filtteri
				If oCategory = oFilter Then

					For Each oConstraint In oOccurrence.Constraints
						oConstName = oConstraint.name

						oConstNameCal = oConstName.replace("_point", "").replace("_dir", "").replace("_ang", "").replace("_mir", "").replace("_end2", "")

						If oConstNameCal.contains("/h/") And isEndSame Then 'jos samoja harjalla ei väliä -/+ puolesta
							oConstNameCal = oConstNameCal.replace("-", "")
						End If

						oData = oConstNameCal.Split(New Char() {"#"c })

						For i As Integer = 1 To oData.Length - 1 'Katon tuulisiteisiin moduulitiedon lisääminen
							If oData(i).Contains("wind_brace") Then
								Dim parts As String() = oData(i).Split("/"c)
								If parts.Length > 2 Then
									Dim prefix As String = parts(1)
									parts(parts.Length - 1) = prefix & "_" & parts(parts.Length - 1)
									oData(i) = String.Join("/", parts)
								End If
							End If
						Next

						If oData.length>2 Then
							Try
								oDictoOcc.Add(laskuri, oData)
								laskuri += 1
							Catch ex As Exception
								oExMessage = ex.Message
							End Try
						End If
					Next 'cons
				End If
			Next 'filtteri
		End If 'virtu comps pois
	Next

	If isEndSame Then
		End1 = "end"
		End2 = "end"
		GableEnd1 = "gableend"
		GableEnd2 = "gableend"
	Else
		End1 = "end1"
		End2 = "end2"
		GableEnd1 = "gableend1"
		GableEnd2 = "gableend2"
	End If

	laskuri = oDictoOcc.Count + 1
	oDictoOcc.Add(laskuri, {End1, "f1/s/" & End1, "f1/s/" & End1 }) : laskuri += 1
	oDictoOcc.Add(laskuri, {End2, "f" & totalFramePcs & "/s/" & End2, "f" & totalFramePcs & "/s/" & End2 }) : laskuri += 1

	For i = 1 To e_roof_pcs
		oDictoOcc.Add(laskuri, {End1 & "_" & i, "f1/p" & i & "k" & End1 & "_" & i, "f1/p" & i & "k/" & End1 & "_" & i }) : laskuri += 1 'obs muut katot
		oDictoOcc.Add(laskuri, {GableEnd1 & "_" & i, "f1/p" & i & "k/" & GableEnd1 & "_" & i, "f1/p" & i & "k/" & GableEnd1 & "_" & i }) : laskuri += 1
		oDictoOcc.Add(laskuri, {End2 & "_" & i, "f" & totalFramePcs & "/p" & i & "k/" & End2 & "_" & i, "f" & totalFramePcs & "/p" & i & "k/" & End2 & "_" & i }) : laskuri += 1
		oDictoOcc.Add(laskuri, {GableEnd2 & "_" & i, "f" & totalFramePcs & "/p" & i & "k/" & GableEnd2 & "_" & i, "f" & totalFramePcs - 1 & "/p" & i & "k/" & GableEnd2 & "_" & i }) : laskuri += 1
		oDictoOcc.Add(laskuri, {GableEnd1 & "_" & i, "f" & GableEndBraceInsertFrame & "/p" & i & "k/" & GableEnd1 & "_" & i, "f" & GableEndBraceInsertFrame & "/p" & i & "k/" & GableEnd1 & "_" & i }) : laskuri += 1 'muut 
		oDictoOcc.Add(laskuri, {GableEnd2 & "_" & i, "f" & totalFramePcs - GableEndBraceInsertFrame + 1 & "/p" & i & "k/" & GableEnd2 & "_" & i, "f" & GableEndBraceInsertFrame & "/p" & i & "k/" & GableEnd2 & "_" & i }) : laskuri += 1
	Next

	If e_ridge_width > 0 Then
		oDictoOcc.Add(laskuri, {End1, "f1/h/" & End1 & "_h", "f1/h/" & End1 & "_h" }) : laskuri += 1
		oDictoOcc.Add(laskuri, {End2, "f" & totalFramePcs & "/h/" & End2 & "_h", "f" & totalFramePcs & "/h/" & End2 & "_h" }) : laskuri += 1
		oDictoOcc.Add(laskuri, {GableEnd1, "f" & GableEndBraceInsertFrame & "/h/" & GableEnd1 & "_h", "f" & GableEndBraceInsertFrame & "/h/" & GableEnd1 & "_h" }) : laskuri += 1
		oDictoOcc.Add(laskuri, {GableEnd2, "f" & totalFramePcs - GableEndBraceInsertFrame + 1 & "/h/" & GableEnd2 & "_h", "f" & GableEndBraceInsertFrame & "/h/" & GableEnd2 & "_h" }) : laskuri += 1
	End If

	Return oDictoOcc
End Function
Function GetAllModules() As ArrayList
	Try
		' Use List(Of String) for better type safety and performance
		Dim allModulesList As New List(Of String)(totalFramePcs * (e_roof_pcs + 1 + If (e_ridge_width > 10, 1, 0)))
		Dim roofModuleTypes As New List(Of String)(e_roof_pcs + 1 + If (e_ridge_width > 10, 1, 0))

		' Add roof module types (p1k to pNk) based on the number of roof pieces
		For i As Integer = 1 To e_roof_pcs
			roofModuleTypes.Add("p" & i & "k")
		Next

		' Add "s" (structure) module type
		roofModuleTypes.Add("s")

		' Add "h" (ridge) module type if ridge width is greater than 10
		If e_ridge_width > 10 Then
			roofModuleTypes.Add("h")
		End If

		' Pre-calculate all frame values (f1 to fN)
		Dim frameValues As New List(Of String)(totalFramePcs)
		For i As Integer = 1 To totalFramePcs
			frameValues.Add("f" & i)
		Next

		' Combine frame values with module types efficiently
		For Each frameValue As String In frameValues
			For Each moduleType As String In roofModuleTypes
				allModulesList.Add(frameValue & "/" & moduleType)
			Next
		Next

		' Convert to ArrayList for backward compatibility
		Dim result As New ArrayList(allModulesList.Count)
		For Each item As String In allModulesList
			result.Add(item)
		Next

		Return result
	Catch ex As Exception
		Logger.Debug("Error in GetAllModules: " & ex.Message)
		Return New ArrayList()
	End Try
End Function
Sub ExportTupleListToCSV(tupleList As List(Of Tuple(Of String, List(Of String))), filePath As String)
	Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
		' Write each tuple (Item1 as key and Item2 as list of values) to the CSV file
		For Each tuple In tupleList
			Dim key As String = Tuple.Item1
			Dim values As List(Of String) = Tuple.Item2
			' Combine the key with the values and write them as a CSV line
			Dim csvLine As String = String.Join(",", New String() {key }.Concat(values))
			writer.WriteLine(csvLine)
		Next
	End Using
End Sub
Function ImportCSVToTupleList(filePath As String) As List(Of Tuple(Of String, List(Of String)))
	'	koe=ImportCSVToTupleList(ThisDoc.Path & "\data.csv")
	Dim tupleList As New List(Of Tuple(Of String, List(Of String)))()
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As List(Of String) = parts.Skip(1).ToList()
			' Create a Tuple and add it to the list
			tupleList.Add(New Tuple(Of String, List(Of String))(key, values))
		Loop
	End Using
	Return tupleList
End Function
Function HandleObjAttribute(oCommand As String, oObject As Object, oAttSet As String, oAttribute As String, Optional oAttValue As Object = "", Optional oAttValueType As ValueTypeEnum = kStringType)
	'HandleObjAttribute("set", ThisApplication.ActiveDocument.ComponentDefinition, "Testi", "testi", ConsList)
	Dim oAttribSet As AttributeSet : Dim myAttNameList As New ArrayList
	Dim oAttrib As Attribute
	Dim oAttribSets As AttributeSets = oObject.AttributeSets
	Onko = oObject.AttributeSets.NameIsUsed(oAttSet)

	If oObject.AttributeSets.NameIsUsed(oAttSet) = True Then
		oAttribSet = oObject.AttributeSets.Item(oAttSet)
	Else
		oAttribSet = oObject.AttributeSets.Add(oAttSet)
	End If

	If oCommand = "set" Then
		Try
			oAttrib = oAttribSet.Add(oAttribute, oAttValueType, oAttValue)
			Logger.Debug("Creating oAttribute " & oAttribute)
		Catch
			'Logger.Debug("Updating " & oAttSet & "\" & oAttribute & " = " & oAttValue)
			Try : oAttrib = oAttribSet.Item(oAttribute) : Catch : Logger.Debug("Not found attribute named : " & oAttribute) : End Try
		oAttrib.Value = oAttValue
		End Try

		'Return "Set done"
	End If

	If oCommand = "del" Then
		Try : oAttrib = oAttribSets.Item(oAttSet).Item(oAttribute) : Catch : Logger.Debug("del: Not found attribute named : " & oAttribute) : End Try
		Try : oAttrib.Delete : Catch : End Try
		Return "Delete done"
	End If

	If oCommand = "get" Then
		Try : oAttrib = oAttribSets.Item(oAttSet).Item(oAttribute) : Catch : Logger.Debug("get: Not found attribute named : " & oAttribute) : End Try
		Try : oValue = oAttrib.Value : Catch : End Try : Logger.Debug("Getting oValue : " & oValue)
		Return oValue
	End If

	If oCommand = "getall" Then
		TulosteTeksti = oAttSet & vbLf

		For Each oAttr As Attribute In oAttribSet
			Dim attrName As String = oAttr.Name
			myAttNameList.Add(attrName)
			attrValue = oAttr.Value.tostring

			' Print the AttributeSet name, Attribute name, and Attribute value
			TulosteTeksti += "  " & attrName & " : " & attrValue & vbLf
		Next
		Return TulosteTeksti
	End If
End Function
Sub ExportDictionaryToCSV(dictionary As Dictionary(Of String, String()), filePath As String)
	Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
		' Write each key-value pair to the CSV file
		For Each kvp In dictionary
			Dim key As String = kvp.Key
			Dim values As String() = DirectCast(kvp.Value, String())
			' Combine the key with the values and write them as CSV
			Dim csvLine As String = String.Join(",", New String() {key }.Concat(values))
			writer.WriteLine(csvLine)
		Next
	End Using
End Sub
Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c) ' Split by comma
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function
Class AlphanumComparator
	Implements IComparer
	Public Function Compare(x As Object, y As Object) As Integer Implements IComparer.Compare
		Dim s1 As String = x.ToString()
		Dim s2 As String = y.ToString()
		' Use regular expressions to split strings into numerical and non-numerical parts
		Dim regex As New Regex("(\d+)|(\D+)")
		Dim s1Parts As MatchCollection = regex.Matches(s1)
		Dim s2Parts As MatchCollection = regex.Matches(s2)
		Dim length As Integer = Math.Min(s1Parts.Count, s2Parts.Count)
		For i As Integer = 0 To length - 1
			Dim s1Part As String = s1Parts(i).Value
			Dim s2Part As String = s2Parts(i).Value
			' Compare numeric parts as integers
			If IsNumeric(s1Part) AndAlso IsNumeric(s2Part) Then
				Dim s1Num As Integer = Convert.ToInt32(s1Part)
				Dim s2Num As Integer = Convert.ToInt32(s2Part)
				If s1Num <> s2Num Then
					Return s1Num.CompareTo(s2Num)
				End If
			Else
				' Compare non-numeric parts as strings
				Dim comparison As Integer = String.Compare(s1Part, s2Part)
				If comparison <> 0 Then
					Return comparison
				End If
			End If
		Next
		' If all parts are equal, compare by length (just in case)
		Return s1.Length.CompareTo(s2.Length)
	End Function
End Class
Function AddToPointCollection(ByRef pointCollection As List(Of Tuple(Of String, List(Of String))), key As String, value As String)
	' Try to find existing tuple
	Dim existingTuple = pointCollection.Find(Function(t) t.Item1 = key)
	If existingTuple IsNot Nothing Then
		' Get module type from key (e.g., "p2k" from "f20/p2k")
		Dim moduleType = key.Split("/"c)(1)
		' Remove only the specific "_plain" entry for this module type
		existingTuple.Item2.RemoveAll(Function(x) x = moduleType & "_plain")
		' Add new value
		existingTuple.Item2.Add(value)
	Else
		' Create new tuple with new list
		Dim newList As New List(Of String)
		newList.Add(value)
		pointCollection.Add(New Tuple(Of String, List(Of String))(key, newList))
	End If
End Function