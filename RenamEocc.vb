Sub main()
	RenameOccu("e")
End Sub


Function RenameOccu(Filter As String)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		'FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
		oOccName=oOccurrence.Name
		If Left(oOccName,Len(Filter))=Filter Then : 'Logger.Debug(FullFileName)
		oOccurrence.Name="u_" &  oOccName
		End If
		
	Next
End Function