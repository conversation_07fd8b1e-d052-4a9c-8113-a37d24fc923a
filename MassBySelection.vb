AddReference "System.Windows.Forms"
AddReference "System.Drawing"
Imports System.Windows.Forms
Imports System.Drawing
Imports System.IO
Imports System.Text
Imports System
Imports System.Collections.Generic
Imports System.Linq

Sub Main()
	
CustomWeightCheck = False  ' False/True add sovelia props weigth end of line
TestSovelia = False 'False/True project num override case of test environment	
	
Dim oAsm As AssemblyDocument = ThisDoc.Document
Dim oSelSet As SelectSet = oAsm.SelectSet
Dim UoM As UnitsOfMeasure = oAsm.UnitsOfMeasure

' Get the string representation of the mass units (e.g., "kg", "lbmass") - removing the number part
Dim MassUnitsString As String = UoM.GetStringFromValue(1, UoM.MassUnits)
' Try to remove common numeric prefixes generated by GetStringFromValue
MassUnitsString = System.Text.RegularExpressions.Regex.Replace(MassUnitsString, "^\d*[\.,]?\d+\s*", "").Trim()
If String.IsNullOrWhiteSpace(MassUnitsString) Then MassUnitsString = "units" ' Fallback if regex fails

' Check if anything is selected
If oSelSet.Count = 0 Then
	i = MessageBox.Show("Please select one or more components first." & vbLf & "Would you like to perform a Sovelia search?", "No Pre Selection", MessageBoxButtons.YesNo)
	If i = vbYes Then ManualSelectionByFrameNumber(TestSovelia)
	Exit Sub
End If

Dim totalMassRaw As Double = 0 ' Sum raw mass for accuracy
Dim displayData As New List(Of String) ' List to hold formatted strings for the ListBox
Dim occurrencesToProcess As New List(Of ComponentOccurrence) ' List of occurrences that should be counted

' --- Step 1: Get all selected ComponentOccurrences ---
Dim selectedOccurrences As New List(Of ComponentOccurrence)
For Each selectedObject As Object In oSelSet
	If TypeOf selectedObject Is ComponentOccurrence Then
		selectedOccurrences.Add(CType(selectedObject, ComponentOccurrence))
	End If
Next

If selectedOccurrences.Count = 0 Then
	MessageBox.Show("No component occurrences found in the selection.", "Selection Error", MessageBoxButtons.OK, MessageBoxIcon.Warning)
	Exit Sub
End If

' --- Step 2: Filter out occurrences whose selected parent is also selected ---
For Each currentOcc As ComponentOccurrence In selectedOccurrences
	Dim isSubComponentOfSelectedParent As Boolean = False
	Dim parent As ComponentOccurrence = Nothing
	Try
		' Start checking from the immediate parent upwards
		If Not currentOcc.IsTopLevel Then ' Avoid error on top-level occs trying to get parent
			parent = currentOcc.ParentOccurrence
		End If
	Catch ex As Exception
		Logger.Warn("Error getting parent for " & currentOcc.Name & ": " & ex.Message)
		parent = Nothing
	End Try

	While parent IsNot Nothing
		If selectedOccurrences.Contains(parent) Then
			isSubComponentOfSelectedParent = True
			Exit While ' Found a selected parent
		End If
		' Move to the next parent up the chain
		Try
			If Not parent.IsTopLevel Then
				parent = parent.ParentOccurrence
			Else
				parent = Nothing ' Reached the top
			End If
		Catch ex As Exception
			Logger.Warn("Error getting next parent for " & parent.Name & ": " & ex.Message)
			parent = Nothing
		End Try
	End While

	If Not isSubComponentOfSelectedParent Then
		occurrencesToProcess.Add(currentOcc)
	Else
		Logger.Info("Skipping '" & currentOcc.Name & "' because a selected parent was found.")
	End If
Next

If occurrencesToProcess.Count = 0 Then
	MessageBox.Show("The selection consists entirely of components whose parent assemblies were also selected, or no processable components were found." & vbCrLf & _
	"Only top-level selected items (or items whose parents weren't selected) are included in the mass calculation.", _
	"Filtering Result", MessageBoxButtons.OK, MessageBoxIcon.Information)
	Exit Sub
End If

' --- Step 3 & 4: Collect Data and Format for Display ---
' Define column widths (adjust as needed)
Dim PnWidth As Integer = 15
Dim DescWidth As Integer = 45 ' Increased width for description
Dim MassWidth As Integer = 18 ' Adjusted for formatted mass
' Create Header Row using Padding
displayData.Add("Part Number".PadRight(PnWidth) & "| Description".PadRight(DescWidth) & "| Mass".PadRight(MassWidth))
' Create Separator Row
displayData.Add(New String("-"c, PnWidth) & "+-" & New String("-"c, DescWidth) & "+-" & New String("-"c, MassWidth))

For Each oOcc As ComponentOccurrence In occurrencesToProcess
	Dim partNum As String = "N/A"
	Dim description As String = "N/A"
	Dim itemMassRaw As Double = 0
	Dim massStringFormatted As String = "Error"

	Try
		Dim oRefDoc As Document = Nothing
		Try
			oRefDoc = oOcc.Definition.Document
		Catch docEx As Exception
			Logger.Warn("Could not get Definition.Document for Occurrence: " & oOcc.Name & ". Skipping. Error: " & docEx.Message)
			Continue For ' Skip if definition is inaccessible
		End Try

		' Get Properties
		Try
			partNum = CStr(oRefDoc.PropertySets("Design Tracking Properties")("Part Number").Value)
		Catch ex As Exception
			Logger.Debug("Part Number property not found or error for " & oOcc.Name & ": " & ex.Message)
		End Try
		If String.IsNullOrWhiteSpace(partNum) Then partNum = "<blank>" ' Indicate blank vs N/A

		Try
			description = CStr(oRefDoc.PropertySets("Design Tracking Properties")("Description").Value)
		Catch ex As Exception
			Logger.Debug("Description property not found or error for " & oOcc.Name & ": " & ex.Message)
		End Try
		If String.IsNullOrWhiteSpace(description) Then description = "<blank>"

		
		If CustomWeightCheck Then
			Try
				CustomWeight = CStr(oRefDoc.PropertySets("User Defined Properties")("Weight").Value)
			Catch ex As Exception
				Logger.Debug("Weight property not found or error for " & oOcc.Name & ": " & ex.Message)
			End Try
			If String.IsNullOrWhiteSpace(CustomWeight) Then CustomWeight = "<blank>"
		End If

		' Get Mass
		Try
			itemMassRaw = oOcc.MassProperties.Mass
			' Check for non-physical components
			If itemMassRaw <= 1.0E-9 Then ' Use a small tolerance for zero mass
				itemMassRaw = 0
				massStringFormatted = 0.0.ToString("F1") & " " & MassUnitsString ' Format zero explicitly
			Else
				' Sum the raw mass for total accuracy
				totalMassRaw = totalMassRaw + itemMassRaw
				' Round the value for display
				Dim itemMassRounded As Double = Math.Round(itemMassRaw, 1)
				' Format the rounded value to exactly one decimal place
				massStringFormatted = itemMassRounded.ToString("F1") & " " & MassUnitsString
			End If
		Catch ex As Exception
			massStringFormatted = "Calc Error"
			Logger.Warn("Could not calculate mass for " & oOcc.Name & ". Error: " & ex.Message)
		End Try

		' Add formatted string to the display list using padding and truncation
		Dim pnDisplay As String = If (partNum.Length > PnWidth, partNum.Substring(0, PnWidth - 1) & "…", partNum).PadRight(PnWidth)
		Dim descDisplay As String = If (description.Length > DescWidth, description.Substring(0, DescWidth - 1) & "…", description).PadRight(DescWidth)
		Dim massDisplay As String = If (massStringFormatted.Length > MassWidth, massStringFormatted.Substring(0, MassWidth - 1) & "…", massStringFormatted).PadRight(MassWidth)

		displayData.Add(pnDisplay & "| " & descDisplay & "| " & massDisplay & "| " & CustomWeight)

	Catch ex As Exception
		Logger.Error("General error processing occurrence: " & oOcc.Name & ". Error: " & ex.Message)
		displayData.Add(oOcc.Name.PadRight(PnWidth) & "| " & "*** ERROR ***".PadRight(DescWidth) & "| " & ex.Message.Split(vbCrLf)(0).PadRight(MassWidth))
	End Try
Next
Try
	SMass = Round(SharedVariable("SoveliaSearchMass"), 1)
Catch
	SMass = 0
End Try

If SMass>10 Then
	displayData.Add("Total (Sovelia Search Included): " & Round(totalMassRaw, 1) & " + " & SMass & " = " & Round(totalMassRaw + SMass, 1))
	Else
		displayData.Add("Total: " & Round(totalMassRaw, 1))
End If


If displayData.Count > 2 Then ' Check if we have more than just headers
	' Format the total mass to one decimal place for the title
	Dim totalMassFormatted As String = Math.Round(totalMassRaw, 1).ToString("F1") & " " & MassUnitsString
	Dim dialogTitle As String = "Selected Components Mass | Total: " & totalMassFormatted
	' Use the standard iLogic InputListBox function
	MultiValue.List("ComponentMassData") = displayData
	' Display the ListBox.
	InputListBox("Component Mass Breakdown:", MultiValue.List("ComponentMassData"), displayData(0), Title := dialogTitle, ListName := "ComponentMassData")
Else
	MessageBox.Show("No component data could be collected or displayed after filtering.", "Processing Complete", MessageBoxButtons.OK, MessageBoxIcon.Information)
End If

Dim clipboardText As String = String.Join(System.Environment.NewLine, displayData)
My.Computer.Clipboard.SetText(clipboardText)

End Sub

Function ManualSelectionByFrameNumber(TestSovelia As Boolean)
	Dim filteredValues As New List(Of String)()

	Dim form As New Form
	form.Text = "Insert Detailed Frames"
	form.Size = New System.Drawing.Size(300, 400)
	form.StartPosition = FormStartPosition.CenterScreen

	' Create a CheckedListBox for multiple selection
	Dim checkedListBox As New CheckedListBox()
	checkedListBox.Location = New System.Drawing.Point(50, 50)
	checkedListBox.Size = New System.Drawing.Size(200, 250)
	checkedListBox.CheckOnClick = True

	' Add "Select All" checkbox
	Dim selectAllCheckBox As New CheckBox()
	selectAllCheckBox.Text = "Select All"
	selectAllCheckBox.Location = New System.Drawing.Point(50, 20)

	' Add items to the CheckedListBox
	For i As Integer = 1 To SDA_pcs
		checkedListBox.Items.Add(i)
	Next

	' Handle Select All checkbox
	AddHandler selectAllCheckBox.CheckedChanged, Sub(sender, e)
	For i As Integer = 0 To checkedListBox.Items.Count - 1
		checkedListBox.SetItemChecked(i, selectAllCheckBox.Checked)
	Next
	End Sub

	' Create OK button
	Dim okButton As New Button
	okButton.Text = "OK"
	okButton.Location = New System.Drawing.Point(50, 320)
	okButton.DialogResult = DialogResult.OK

	' Create Cancel button
	Dim cancelButton As New Button
	cancelButton.Text = "Cancel"
	cancelButton.Location = New System.Drawing.Point(150, 320)
	cancelButton.DialogResult = DialogResult.Cancel

	' Add controls to the form
	form.Controls.Add(selectAllCheckBox)
	form.Controls.Add(checkedListBox)
	form.Controls.Add(okButton)
	form.Controls.Add(cancelButton)

	' Set the form's Accept and Cancel buttons
	form.AcceptButton = okButton
	form.CancelButton = cancelButton

	' Show the form as a dialog and capture the result
	Dim result As DialogResult = form.ShowDialog()

	' Handle the result
	If result = DialogResult.OK Then
		' Get selected values
		Dim selectedValues As New List(Of Integer)
		For i As Integer = 0 To checkedListBox.Items.Count - 1
			If checkedListBox.GetItemChecked(i) Then
				selectedValues.Add(CInt(checkedListBox.Items(i)))
			End If
		Next

		If selectedValues.Count > 0 Then
			Try
				NumberListEdit = SharedVariable("NumberListEdit")
			Catch
				BUfile = ThisDoc.Path & "\data.csv"
				Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
				NumberListEdit = ImportCSVToDictionary(BUfile)
				SharedVariable("NumberListEdit") = NumberListEdit
			End Try

			For Each selectedValue In selectedValues
				Dim oGeneratedDetModelsFiltered As New ArrayList
				For Each kvp As KeyValuePair(Of String, String()) In NumberListEdit
					If kvp.Key.StartsWith("f" & selectedValue & "/") Then
						idNum = kvp.Value(0)
						
						If TestSovelia Then
							PNum = "5411-10"
						Else
							PNum = kvp.Value(1)
						End If
						
						filteredValues.Add(idNum & "_" & PNum)
					End If
				Next
			Next
		End If
	End If


	resultCriteria = "WEIGHT"
	Teksti = ""
	For Each oSearchValue In filteredValues
		ResultList = SoveliaSearh(oSearchValue, resultCriteria)
		If ResultList Is Nothing Then Exit Function
		Dim TulosData() As String = ResultList(0).Split(":"c)
		Try
			Massa += CDbl(TulosData(1))
			Teksti += oSearchValue & " : " & Round(CDbl(TulosData(1)), 1) & vbLf
		Catch
			Teksti += "Error in mass calculation in :" & oSearchValue & vbLf
			Logger.Debug("Error in mass calculation in :" & oSearchValue)
			Massa += 0
		End Try
		
	Next

	j = MessageBox.Show(Teksti & vbLf & "Copy value?", "Mass total: " & Massa, MessageBoxButtons.YesNo)
	If j = vbYes  Then
		My.Computer.Clipboard.SetText(Teksti)
		SharedVariable("SoveliaSearchMass") = Massa
	End If
End Function

Function SoveliaSearh(SearchID As String, resultCriteria As String)
	Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
	_addInInterface = addin.Automation
	Dim searchCriteria As New List(Of String)()
	Dim resultLimit As Integer
	searchCriteria.Add("DocID:" & SearchID)
	resultLimit = 1 : Dim task As System.Threading.Tasks.Task(Of String)
	Try
	task  = _addInInterface.Search(searchCriteria, resultCriteria, resultLimit)
	Catch
		Logger.Debug("Errror connecting Sovelia with id " & SearchID)
		Exit Function
	End Try
	Dim timeout As Integer = 15000
	Dim oResult As String
	If task.Wait(timeout) Then
		oResult = task.Result
		' Use the result
	Else
		' Timeout occurred
		Throw New TimeoutException("The search operation timed out.")
	End If
	'	Dim awaiter As System.Runtime.CompilerServices.TaskAwaiter(Of String) = task.GetAwaiter()
	'	awaiter.GetResult()
	'Dim oResult As String = awaiter.GetResult()
	Dim TxtData() As String
	Try
		TxtData = oResult.Split(";"c)
	Catch
		Logger.Debug("Error in search :" & SearchID)
	End Try
	Logger.Debug(SearchID & TxtData(0))
	Return TxtData
End Function

Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c)
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function