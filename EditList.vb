Imports System.IO
Imports System.Text
Imports System.Windows.Forms
AddReference "C:\Vault_BH\ExternalRules_BH_Vault\MyTableForm.dll"

Sub Main()
If InitialRun Then
	iLogicVb.RunRule("getAllModules")
End If

Try
	NumberListEdit = SharedVariable("NumberListEdit")
Catch
	Dim BUfile As String = ThisDoc.Path & "\data.csv"
	Logger.Debug("No shared variable NumberListEdit defined using file :" & BUfile)
	NumberListEdit = ImportCSVToDictionary(BUfile)
	SharedVariable("NumberListEdit") = NumberListEdit
End Try

AddModelNameCheckGen(NumberListEdit)
NumberListEdit = ShowEditlist(NumberListEdit)

If NumberListEdit Is Nothing Then
	Logger.Debug("Canceling Command...")
	Exit Sub
End If

AddModelNameCheckGen(NumberListEdit)
GenerateMissingDetModel(NumberListEdit)

ExportDictionaryToCSV(NumberListEdit, ThisDoc.Path & "\data.csv")
End Sub

Function GenerateMissingDetModel(ByRef dict As Dictionary(Of String, String()))
	Dim StartTime As DateTime = Now
	Dim oFilesGenS As New List(Of String)
	Dim oFilesGenK As New List(Of String)
	Dim oFilesGenH As New List(Of String)
	Dim teksti As String = ""

	For Each oVal In dict
		Dim oKey As String = oVal.Key
		Dim oValue As String() = oVal.Value
		Dim PNum As String = oValue(0)
		Dim IsGenerated As String = oValue(2)
		Dim DetModelPath As String = oValue(3)

		Dim GenAllForNo As String = If (GenerateAllDetailedModels, "-", "NO")

		If IsGenerated.ToUpper() <> "YES" AndAlso IsGenerated.ToUpper() <> GenAllForNo Then
			Select Case Left(PNum, 1)
				Case "S"
					If Not oFilesGenS.Contains(DetModelPath) Then oFilesGenS.Add(DetModelPath)
				Case "H"
					If Not oFilesGenH.Contains(DetModelPath) Then oFilesGenH.Add(DetModelPath)
				Case "K"
					If Not oFilesGenK.Contains(DetModelPath) Then oFilesGenK.Add(DetModelPath)
			End Select
		End If
	Next

	Dim oSrcPath As String = "C:\Vault_BH\Designs\Src\"
	Dim SrcFiluM As String = "adet.iam"

	Dim attributes As FileAttributes

	CopyFiles(oFilesGenS, oSrcPath, SrcFiluM, teksti)
	CopyFiles(oFilesGenK, oSrcPath, SrcFiluM, teksti)
	CopyFiles(oFilesGenH, oSrcPath, SrcFiluM, teksti)

	Logger.Debug("Copying modules : " & Math.Round((Now().Subtract(StartTime)).TotalSeconds, 1))
	If teksti.Length > 5 Then
		MessageBox.Show(teksti, "Detail Model copy")
	End If
End Function

Sub CopyFiles(fileList As List(Of String), srcPath As String, srcFile As String, ByRef logText As String)
	For Each newModel In fileList
		If IO.File.Exists(newModel) Then
			Logger.Debug("File already exists : " & newModel)
		Else
			IO.File.Copy(srcPath & srcFile, newModel, True)
			Logger.Debug("New part File being created : " & newModel)
			logText += "Copied " & newModel & vbLf

			Dim attributes As FileAttributes = IO.File.GetAttributes(newModel)
			If (attributes And FileAttributes.ReadOnly) = FileAttributes.ReadOnly Then
				attributes = attributes And Not FileAttributes.ReadOnly
				IO.File.SetAttributes(newModel, attributes)
			End If
		End If
	Next
End Sub

Function AddModelNameCheckGen(ByRef dict As Dictionary(Of String, String()))
	Dim folderPath As String = ThisDoc.Path
	Dim DetModelList As New List(Of String)
	Dim fileExtension As String = "*.iam"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)

	Dim Subfiles As String() = Directory.GetFiles(folderPath, fileExtension, SearchOption.AllDirectories)

	Dim filteredFilesSawModel As New Dictionary(Of String, String)
	For Each File As String In Subfiles
		Dim fileDirectory As String = IO.Path.GetDirectoryName(File)
		If fileDirectory.Length > folderPath.Length AndAlso Not File.Contains("OldVersions") Then
			Dim Tunniste As String = Nothing
			If File.Contains("_s\") Then
				Tunniste = "s"
			ElseIf File.Contains("_h\") Then
				Tunniste = "h"
			ElseIf File.Contains("k\") OrElse File.Contains("k2\") Then
				Dim FNamePos As Integer = InStrRev(File, "_p", -1) + 1
				Tunniste = Mid(File, FNamePos, 3)
			ElseIf File.Contains("_s_b\") Then 'b -tyyppi erikseen
				Tunniste = "s"
			End If
			If Tunniste IsNot Nothing AndAlso Not filteredFilesSawModel.ContainsKey(Tunniste) Then
				filteredFilesSawModel.Add(Tunniste, File)
				filteredFilesSawModel.Add(Tunniste & "Mir", File)
			End If
		End If
	Next

	For Each oFile As String In files
		If oFile.Contains("_adet") Then
			DetModelList.Add(oFile)
		End If
	Next

	Dim keysToModify As New List(Of String)(dict.Keys)
	For Each oKey In keysToModify
		Dim oValue As String() = dict(oKey)
		Dim oKeySplit As String() = oKey.Split("/"c)
		Dim DetModelFullName As String = IO.Path.Combine(ThisDoc.Path, oValue(0) & "_" & oValue(1) & "_adet.iam")
		ModifyValueInDictKey(dict, oKey, DetModelFullName, "model")

		Dim oSawmodel As String = If (filteredFilesSawModel.TryGetValue(oKeySplit(1), oSawmodel), oSawmodel, "-")
		ModifyValueInDictKey(dict, oKey, oSawmodel, "sawmodel")
	Next
End Function

Function ModifyValueInDictKey(ByRef dict As Dictionary(Of String, String()), key As String, newValue As String, Optional oldValue As String = Nothing)
	If dict.ContainsKey(key) Then
		Dim currentArray As String() = dict(key)
		If oldValue Is Nothing Then
			Dim updatedArray As String() = currentArray.Concat({newValue }).ToArray()
			dict(key) = updatedArray
		Else
			For i As Integer = 0 To currentArray.Length - 1
				If currentArray(i) = oldValue Then
					currentArray(i) = newValue
					Exit For
				End If
			Next
			dict(key) = currentArray
		End If
	End If
End Function

Function ShowEditlist(NumberSchema As Dictionary(Of String, String())) As Dictionary(Of String, String())
	Dim oGeneratedDetModels As ArrayList = ReturnListDetmodels()
	Using mf As New MyTableForm.MyForm
		mf.Text = "Module numbering"
		Dim dgv As DataGridView = mf.dgvTable

		dgv.Columns.Add("MyColumn1", "Module")
		dgv.Columns.Add("MyColumn2", "Number")
		dgv.Columns.Add("MyColumn3", "Suffix")
		dgv.Columns.Add("MyColumn4", "is generated")
		dgv.Columns.Add("MyColumn5", "Model")
		dgv.Columns.Add("MyColumn6", "Saw Model")
		dgv.Columns.Add("MyColumn7", "Notes")
		dgv.Columns.Add("MyColumn8", "UCS")

		Dim n As Integer = 0

		For Each oVal In NumberSchema.Keys
			Dim riviN As Integer = dgv.Rows.Add()
			dgv.Rows(n).Cells(0).Value = oVal
			dgv.Rows(n).Cells(0).ReadOnly = True
			dgv.Rows(n).Cells(1).Value = NumberSchema(oVal)(0)
			dgv.Rows(n).Cells(2).Value = NumberSchema(oVal)(1)

			Dim ModelName As String = IO.Path.Combine(ThisDoc.Path, NumberSchema(oVal)(0) & "_" & NumberSchema(oVal)(1) & "_adet.iam")

			Dim isGenerated As String = If (oGeneratedDetModels.Contains(ModelName.ToUpper()), "yes", "no")
			dgv.Rows(n).Cells(3).Value = isGenerated
			dgv.Rows(n).Cells(4).Value = ModelName
			dgv.Rows(n).Cells(5).Value = NumberSchema(oVal)(4)
			dgv.Rows(n).Cells(6).Value = NumberSchema(oVal)(5)
			dgv.Rows(n).Cells(7).Value = NumberSchema(oVal)(6)
			n += 1
		Next

		If mf.ShowDialog() = DialogResult.OK Then
			For Each row As DataGridViewRow In dgv.Rows
				If Not Row.IsNewRow Then
					Dim key As String = Row.Cells(0).Value.ToString()
					Dim newValues As String() = {
					Row.Cells(1).Value.ToString(),
					Row.Cells(2).Value.ToString(),
					Row.Cells(3).Value.ToString(),
					Row.Cells(4).Value.ToString(),
					Row.Cells(5).Value.ToString(),
					Row.Cells(6).Value.ToString(),
					Row.Cells(7).Value.ToString()
					}
					If NumberSchema.ContainsKey(key) Then
						NumberSchema(key) = newValues
					End If
				End If
			Next

			If dgv.SelectedRows.Count > 0 Then
				Dim i As DialogResult = MessageBox.Show("Open & InitialReset " & oOpenModel & vbLf & " No -option just for opening", "Open & InitialReset", MessageBoxButtons.YesNo)

				For Each row As DataGridViewRow In dgv.SelectedRows
					oOpenModel = dgv.Rows(Row.Index).Cells(4).Value

					If i = DialogResult.Yes Then
						openRuniLogicRule("InitialReset", oOpenModel)
					Else
						Try
							Dim oDoc As Document = ThisApplication.Documents.Open(oOpenModel, True)
						Catch
							Logger.Debug("Model is not generated: " & oOpenModel)
						End Try
					End If
				Next
			End If
		Else
			Return Nothing
		End If
	End Using
	Return NumberSchema
End Function

Function openRuniLogicRule(RuleName As String, oFullName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation

	Try
		oDoc = ThisApplication.Documents.Open(oFullName, True)
		Call iLogic.RunRule(oDoc, RuleName)
	Catch ex As Exception
		Logger.Debug("Unable to run the rule." & vbLf & ex.Message)
	Finally
		oDoc = Nothing
	End Try
End Function

Sub ExportDictionaryToCSV(dictionary As Dictionary(Of String, String()), filePath As String)
	Using writer As New StreamWriter(filePath, False, Encoding.UTF8)
		For Each kvp In dictionary
			Dim key As String = kvp.Key
			Dim values As String() = DirectCast(kvp.Value, String())
			Dim csvLine As String = String.Join(",", New String() {key }.Concat(values))
			writer.WriteLine(csvLine)
		Next
	End Using
End Sub

Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, String())
	Dim dictionary As New Dictionary(Of String, String())
	Using reader As New StreamReader(filePath)
		Dim line As String
		Do While (reader.Peek() >= 0)
			line = reader.ReadLine()
			Dim parts As String() = line.Split(","c)
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function

Function ReturnListDetmodels() As ArrayList
	Dim folderPath As String = ThisDoc.Path
	Dim DetModelList As New ArrayList
	Dim fileExtension As String = "*.iam"
	Dim files As String() = Directory.GetFiles(folderPath, fileExtension)

	For Each oFile As String In files
		If oFile.Contains("_adet.iam") Then
			DetModelList.Add(oFile.ToUpper())
		End If
	Next

	Return DetModelList
End Function
