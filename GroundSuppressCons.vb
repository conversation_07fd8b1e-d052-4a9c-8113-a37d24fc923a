Dim oAssDoc As AssemblyDocument = ThisApplication.ActiveDocument

Dim oConstraint As AssemblyConstraint
Dim oJoint As AssemblyJoint
qGround = True

qGround = InputRadioBox("Prompt", "Suppress Constrains + Ground all", "Unsupress Constrains + Un-Ground all", booleanParam, Title := "Unofficial Inventor ")

	i = 0
	For Each oConstraint In oAssDoc.ComponentDefinition.Constraints
		oConstraint.Suppressed = qGround
		i = i + 1
	Next

	ii = 0
	For Each oJoint In oAssDoc.ComponentDefinition.Joints
		oJoint.Suppressed = qGround
		ii = ii + 1
	Next

'get the active assembly
Dim oAsmCompDef As AssemblyComponentDefinition = ThisApplication.ActiveDocument.ComponentDefinition

'set the Master LOD active
Dim oLODRep As LevelOfDetailRepresentation
oLODRep = oAsmCompDef.RepresentationsManager.LevelOfDetailRepresentations.Item("Master")
oLODRep.Activate

'Iterate through all of the top level occurrences
Dim oOccurrence As ComponentOccurrence
For Each oOccurrence In oAsmCompDef.Occurrences
	If oOccurrence.DefinitionDocumentType = DocumentTypeEnum.kAssemblyDocumentObject Then

		'Iterate through all of the 2nd level occurrences
		Dim oSub1Occ As ComponentOccurrence
		For Each oSub1Occ In oOccurrence.SubOccurrences

			'ground everything in the 2nd level
			oSub1Occ.Grounded = qGround
			If oSub1Occ.DefinitionDocumentType = DocumentTypeEnum.kAssemblyDocumentObject Then

				'Iterate through all of the 3nd level occurrences
				Dim oSub2Occ As ComponentOccurrence
				For Each oSub2Occ In oSub1Occ.SubOccurrences
					'ground everything in the 3rd level
					oSub2Occ.Grounded = qGround
				Next
			Else
			End If
		Next
	Else
	End If
	'ground everything in the top level
	oOccurrence.Grounded = qGround
Next

'Ground the first node
Dim doc = ThisDoc.Document
Dim oDef As AssemblyComponentDefinition = doc.ComponentDefinition
Dim oCompOcc As Inventor.ComponentOccurrence
Dim oNames As New ArrayList

For Each oCompOcc In oDef.Occurrences
	GetOccName = oCompOcc.Name
	oNames.Add(GetOccName)
Next

Dim bob As String = oNames(0)
For Each oCompOcc In oDef.Occurrences
	If oCompOcc.Name = bob Then
		oCompOcc.Grounded = True
	End If
Next

MessageBox.Show("Successfully processed " & i & " constraints " & ii & " joints", "iLogic")