Sub main() 'InsertComponent
Dim myArrayList As New ArrayList
myArrayList.Add("Wall Brace")
myArrayList.Add("Purlin tube")
myArrayList.Add("Purlin tube with LY08")
myArrayList.Add("Buckling crossarm")
myArrayList.Add("Brace")
myArrayList.Add("Wind Brace")
myArrayList.Add("Gable End Brace")

EkaUCS = GetUCS()

If EkaUCS Is Nothing Then
	Logger.Debug("No 1st ucs - exiting function..")
	Exit Sub
End If
Logger.Debug("Selectected 1st UCS:" & EkaUCS.ContainingOccurrence.Name & "\" & EkaUCS.Name)
TokaUCS = GetUCS()

If TokaUCS Is Nothing Then
	Logger.Debug("No 2nd ucs - exiting function..")
	Exit Sub
End If
Logger.Debug("Selectected 2nd UCS:" & TokaUCS.ContainingOccurrence.Name & "\" & TokaUCS.Name)
oG_L = CalcUcsDistance(EkaUCS, TokaUCS)
Logger.Debug("oG_L:" & oG_L)

Try : mainComp1 = EkaUCS.ContainingOccurrence.ContainingOccurrence.Name : Catch : End Try
Try : oFromInfo = mainComp1 & "/" & EkaUCS.ContainingOccurrence.Name & "/" & EkaUCS.Name : Catch : Logger.Debug("Error getting 1st ucs info:") : End Try
Try : oToInfo = TokaUCS.ContainingOccurrence.ContainingOccurrence.Name & "/" & TokaUCS.ContainingOccurrence.Name & "/" & TokaUCS.Name : Catch : Logger.Debug("Error getting 2nd ucs info:") : End Try

If EkaUCS.name.contains("joint_purlin_") Then
	FiluType = InputListBox("Selected " & oFromInfo & " to " & oToInfo, myArrayList, myArrayList.Item(0), "Manual Insertion", "Component")

ElseIf EkaUCS.name.contains("joint_crossarm_") Then
	FiluType = InputListBox("Selected " & oFromInfo & " to " & oToInfo, myArrayList, myArrayList.Item(1), "Attribute hangling", "Rule")
Else
	FiluType = InputListBox("Selected " & oFromInfo & " to " & oToInfo, myArrayList, myArrayList.Item(4), "Manual Insertion", "Component")
End If

If FiluType = "Purlin tube" Then
	oFileName = "purlin_tube.ipt"
ElseIf FiluType = "Purlin tube with LY08" Then
	oFileName = "purlin_tube_LY08.ipt"
ElseIf FiluType = "Buckling crossarm" Then
	wfBoolean = InputRadioBox("Select type", "1 x brace ", "Normal", True, "Brace type")
	If wfBoolean = True Then
		oFileName = "brace.ipt"
	Else
		oFileName = "brace3.ipt"
	End If
ElseIf FiluType = "Brace" Then
	oFileName = "brace.ipt"
ElseIf FiluType = "Wind Brace" Then
	oFileName = "wind_brace.ipt"
ElseIf FiluType = "Wall Brace" Then
	oFileName = "wall_brace.ipt"	
ElseIf FiluType = "Gable End Brace" Then
	oFileName = "gable_end_pillar_brace.ipt"
End If


oSuffix = Abs(Now.GetHashCode)
oNewfileName = Left(oFileName, Len(oFileName) -4) & "_useradded_" & oSuffix
oOccName = FiluType & "_" & oFromInfo & "_" & oToInfo


SavePlaceFilu(oFileName, oNewfileName, oOccName, EkaUCS, TokaUCS)
MakeConstrain(EkaUCS, TokaUCS, oOccName, FiluType, oG_L)

'PointtiHash = PointCoorHash("joint_")
End Sub

Function MakeConstrain(StartUCS As UserCoordinateSystem, EndUCS As UserCoordinateSystem, occName As String, FiluType As String, Length As Double)
	Parameter(occName, "G_L") = Length
	If FiluType = "Purlin tube" Or FiluType = "Purlin tube with LY08" Or FiluType = "Buckling crossarm" Then
		EkaTaso = StartUCS.ContainingOccurrence.Name
		TokaTaso = StartUCS.ContainingOccurrence.ContainingOccurrence.Name
		If FiluType = "Buckling crossarm" Then
			oSDA_xoffset = 0
			oSDA_yoffset = 0
			oSDA_zoffset = 0
			SiirtoUCS = "ucs_start"
		Else 'purlin
			oSDA_xoffset = 0
			oSDA_yoffset = 0
			oSDA_zoffset = 0
			SiirtoUCS = "UCS"

		End If
		oConsName = occName & "#" & TokaTaso & "/" & EkaTaso & "/" & StartUCS.Name '&  "#" & TokaTaso_End & "/" & EkaTaso_End & "/" & EndUCS.Name
		Constraints.AddUcsToUcs(oConsName, {TokaTaso, EkaTaso }, StartUCS.Name _
		, ConvertStr_CArgu(occName), SiirtoUCS, xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
	Else
		EkaTaso_Start = StartUCS.ContainingOccurrence.Name
		TokaTaso_Start = StartUCS.ContainingOccurrence.ContainingOccurrence.Name
		EkaTaso_End = EndUCS.ContainingOccurrence.Name
		TokaTaso_End = EndUCS.ContainingOccurrence.ContainingOccurrence.Name

		oConsName = occName & "#" & TokaTaso_Start & "/" & EkaTaso_Start & "/" & StartUCS.Name & "#" & TokaTaso_End & "/" & EkaTaso_End & "/" & EndUCS.Name

		Dim CompoArguStartPoint As ComponentArgument = {TokaTaso_Start, EkaTaso_Start }
		Dim CompoArguEndPoint As ComponentArgument = {TokaTaso_End, EkaTaso_End }
		Dim CompoArguJointOcc As ComponentArgument = occName.ToString
		'		Try
		Constraints.AddMate(oConsName & "#point", CompoArguJointOcc, "ucs_start: Center Point", CompoArguStartPoint, StartUCS.Name & ": Center Point")
		Constraints.AddMate(oConsName & "#dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, EndUCS.Name & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
		'		Catch

		'		End Try

	End If

End Function

Function ConvertStr_CArgu(OccName As String) As ComponentArgument
	ConvertStr_CArgu = OccName
End Function

Function SavePlaceFilu(SrcFilu As String, NewFilu As String, occName As String, StartUCS As UserCoordinateSystem, EndUCS As UserCoordinateSystem)
	oSrcPath = "C:\Vault_BH\Designs\Src\"
	Try
		oDoc = ThisApplication.Documents.Open(oSrcPath & SrcFilu, False)
	Catch
		Logger.Debug("Error not found base part :" & oSrcPath & SrcFilu)
	End Try
	If oDoc Is Nothing Then
		Exit Function
	End If

	oNewname = ThisDoc.Path & "\" & NewFilu & ".ipt"
	If IO.File.Exists(oNewname) Then
		Logger.Debug("File already exists : " & oNewname)
	Else
		Logger.Debug("New part File being created : " & oNewname)
		oDoc.SaveAs(oNewname, True)
	End If

	oStartPoint = StartUCS.Origin.Point
	oEndPoint = EndUCS.Origin.Point

	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	XaxisPoint = oTG.CreatePoint(StartUCS.XAxis.Line.Direction.X, oStartPoint.Y, oStartPoint.Z)

	Dim oEdgeYVector As Vector = oStartPoint.VectorTo(oEndPoint)
	N_oEdgeYVector = NormalizeVector(oEdgeYVector)
	Dim oEdgeXVector As Vector = oStartPoint.VectorTo(XaxisPoint)
	N_oEdgeXVector = NormalizeVector(oEdgeXVector)
	'Dim oEdgeZVector As Vector = oEdgeYVector.CrossProduct(oEdgeXVector)
	Dim oEdgeZVector As Vector = oEdgeXVector.CrossProduct(oEdgeYVector)
	N_oEdgeZVector = NormalizeVector(oEdgeZVector)

	Dim oMatrix As Matrix = oTG.CreateMatrix
	Dim oXAxis As Vector
	Dim oYAxis As Vector
	Dim oZAxis As Vector
	oXAxis = oTG.CreateVector(1, 0, 0)
	oYAxis = oTG.CreateVector(0, 1, 0)
	oZAxis = oTG.CreateVector(0, 0, 1)

	oMatrix.SetCoordinateSystem(oStartPoint, oXAxis, oYAxis, oZAxis)

	'oMatrix.SetToRotateTo(oEdgeXVector, oEdgeYVector, oEdgeZVector)
	oMatrix.SetToRotateTo(oEdgeYVector, oEdgeXVector, oEdgeZVector)

	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)

	Dim Pos = ThisAssembly.Geometry.Matrix(cells(0), cells(1), cells(2), oStartPoint.X * 10,
	cells(4), cells(5), cells(6), oStartPoint.Y * 10,
	cells(8), cells(9), cells(10), oStartPoint.Z * 10,
	0, 0, 0, 1)

	Components.Add(occName, oNewname, position := Pos)
End Function


Function CalcUcsDistance(StartPoint As UserCoordinateSystem, EndPoint As UserCoordinateSystem, Optional Debuggaus As Boolean = False)
	'oStartPoint = StartPoint.Transformation.Translation
	oStartPoint = StartPoint.Origin.Point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & StartPoint.Name & " using zero point instead")
	End If
	oEndPoint = EndPoint.Origin.Point
	If oEndPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & EndPoint.Name & " using zero point instead")
	End If
	CalcUcsDistance = 10 * ((oEndPoint.X - oStartPoint.X) ^ 2 + (oEndPoint.Y - oStartPoint.Y) ^ 2 + (oEndPoint.Z - oStartPoint.Z) ^ 2) ^ 0.5
End Function


Function GetUCS(Optional Dialog As Boolean = False)
	'oUcs = ThisApplication.TransientObjects.CreateObjectCollection
	oUcs = ThisApplication.CommandManager.Pick(
	SelectionFilterEnum.kUserCoordinateSystemFilter,
	"Select a UCS point")
	If Dialog Then
		Try : oParentti = oUcs.ContainingOccurrence.ParentOccurrence.name : Catch : End Try

		MessageBox.Show("Selected UCS: " & oParentti & "/" & oUcs.ContainingOccurrence.Name & "/" & oUcs.Name & vbLf & "[" & round(oUcs.Origin.Point.X) _
		& "," & Round(oUcs.Origin.Point.Y) & "," & Round(oUcs.Origin.Point.Z) & "]", "UCS")
		My.Computer.Clipboard.SetText(oParentti & "/" & oUcs.ContainingOccurrence.Name & "/" & oUcs.Name)
	End If
	Return oUcs
End Function


Function PointCoorHash(sWPFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument
	oAsmDoc = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition
	oAsmDef = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'Debug.Print thisOcc.Name
		'skip suppressed components
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					If InStr(1, currentWP.Name, sWPFilter) Then
						Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
						X = Round(oAsmPoint.point.X * 10, 1)
						Y = Round(oAsmPoint.point.Y * 10, 1)
						Z = Round(oAsmPoint.point.Z * 10, 1)
						If Debuggaus Then
							Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
						End If
						oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
					End If
				Next
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								'check if pointname contains underscore
								If InStr(1, currentSubWP.Name, sWPFilter) Then
									Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
									X = oAsmPoint.point.X * 10
									Y = oAsmPoint.point.Y * 10
									Z = oAsmPoint.point.Z * 10
									If Debuggaus Then
										Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
										oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
									End If
									oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
								End If
							Next
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = oAsmPoint.point.X * 10
												Y = oAsmPoint.point.Y * 10
												Z = oAsmPoint.point.Z * 10

												If Debuggaus Then
													Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
													oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												End If
												oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, {X, Y, Z })
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next
	If Debuggaus Then
		CoordDataa = oDataTeksti
		My.Computer.Clipboard.SetText(oDataTeksti)
	End If
	Return oHashtable
End Function

Function ShowMatrix(oMatrix As Matrix)
	Dim i As Integer
	Logger.Debug(vbLf & "###")
	For i = 1 To 4
		Logger.Debug( _
		Round(oMatrix.Cell(i, 1), 2) & ", " & _
		Round(oMatrix.Cell(i, 2), 2) & ", " & _
		Round(oMatrix.Cell(i, 3), 2) & ", " & _
		Round(oMatrix.Cell(i, 4)), 1)
	Next
End Function

Function NormalizeVector(oVector As Vector)
	Pituus = (oVector.X ^ 2 + oVector.Y ^ 2 + oVector.Z ^ 2) ^ 0.5
	oNormalVector = ThisApplication.TransientGeometry.CreateVector(oVector.X / Pituus, oVector.Y / Pituus, oVector.Z / Pituus)
	Return oNormalVector
End Function