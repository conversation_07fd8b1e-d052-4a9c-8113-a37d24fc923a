Sub Main() 'FaceInfo

'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
'testi = GetUcsByNames("ex1/UCS2")
If Not testi Is Nothing
	Logger.Debug("testi: " & testi.name)
End If
End Sub

Function GetUcsByNames(UCSinfo As String)
	'testi = GetUcsByNames("f6/sMir/wind_brace_purlin_k1_3")
	'testi = GetUcsByNames("ex1/UCS2")
	oData = UCSinfo.Split(New Char() {"/"c })
	If oData.Length = 3 Then
		Dim oName As String() = {oData(0), oData(1) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(2))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
		End Try
		Exit Function
	ElseIf oData.Length = 2 Then
		Dim oName As String() = {oData(0) }
		Try
			oOcc = Component.InventorComponent(oName)
		Catch
			Logger.Debug("Not found occ in " & UCSinfo)
			Exit Function
		End Try
		Try
			oUcs = oOcc.Definition.UserCoordinateSystems(oData(1))
		Catch
			Logger.Debug("Not found UCS in " & UCSinfo)
			Exit Function
		End Try
	End If
	Return oUcs
End Function
