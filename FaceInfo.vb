Sub Main() 'FaceInfo

ShowAllPartsFaces()

End Sub

Function ShowAllPartsFaces(Optional PreventDup As Boolean = True)
	Dim myArrayList As New ArrayList

	Dim oAssemblyDoc As AssemblyDocument = ThisApplication.ActiveDocument
	
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry

	Dim oOccs As ComponentOccurrences =
	oAssemblyDoc.ComponentDefinition.Occurrences
	
	For Each oOcc As ComponentOccurrence In oOccs
		FileName = oOcc.ReferencedDocumentDescriptor.FullDocumentName
		If Not myArrayList.Contains(FileName) Then
			If oOcc.DefinitionDocumentType = 12291 Then
				Iterate(oTG,oOcc.SubOccurrences)
			Else
				GetEntityByName(oTG, oOcc)
			End If
		End If
		If PreventDup Then
			myArrayList.Add(FileName)
		End If
	Next
	'oDrawingView.ViewStyle = oldStyle
End Function

Function GetEntityByName(oTG As TransientGeometry, oOcc As ComponentOccurrence)
	Dim oModelDoc As Inventor.PartDocument = oOcc.Definition.Document
	Try
		Dim myNamedEntities As NameValueMap = iLogicVb.Automation.GetNamedEntities(oModelDoc).Entities
		For i = 1 To myNamedEntities.Count
			oEntType = CType(myNamedEntities.Value(myNamedEntities.Name(i)).Type, ObjectTypeEnum).ToString
			If oEntType = "kWorkPlaneObject" Or oEntType = "kWorkAxisObject" Or oEntType = "kWorkPointObject" Then
				oWorkFeature = True
			Else
				oWorkFeature = False
			End If
			If oWorkFeature = False Then
				Logger.Debug(oOcc.Name & ":" & myNamedEntities.Name(i) & " : " & oEntType)
			End If
		Next
	Catch
	End Try
End Function

Function Iterate(oTG As TransientGeometry, oOccs As ComponentOccurrences)
	For Each oOcc As ComponentOccurrence In oOccs
		If oOcc.DefinitionDocumentType = 12291 Then
			Iterate(oTG, oOcc.SubOccurrences)
		Else
			GetEntityByName(oTG, oOcc)
		End If
	Next
End Function