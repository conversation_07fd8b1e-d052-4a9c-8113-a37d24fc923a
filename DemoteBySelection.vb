Sub Main()
	
Dim targetLevel As Integer = 1 ' Adjust this to the desired level (e.g., 2 means parent of the parent, etc.)

oMoveOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyOccurrenceFilter, "Select Moved occurrence")	

Dim oLeafOccurrence As ComponentOccurrence
Dim oTargetSubAssembly As ComponentOccurrence


oLeafOccurrence = ThisApplication.CommandManager.Pick(SelectionFilterEnum.kAssemblyLeafOccurrenceFilter, "Select target assembly (leaf 2 up)")

If oLeafOccurrence Is Nothing Then
    MsgBox("Selection was canceled or invalid.")
    Return
End If
' Step 2: Traverse up the assembly hierarchy

oTargetSubAssembly = GetParentOccurrenceAtLevel(oLeafOccurrence, targetLevel)

If oTargetSubAssembly Is Nothing Or oMoveOccurrence Is Nothing  Then
    MsgBox("No subassembly found at the desired level.")
Else
	Logger.Debug(oMoveOccurrence.name & " -> " & oTargetSubAssembly.Name)
End If
DemoteOcc(oTargetSubAssembly,oMoveOccurrence)

End Sub



Function DemoteOcc(oSubAssyOcc As ComponentOccurrence, oMoveOcc As ComponentOccurrence)
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
		If oSubAssyOcc Is Nothing Or oMoveOcc Is Nothing Then
		Logger.Debug("Error!! not found Occurence " & Kohde & " or " & SiirtoOcc)
		Exit Function
	End If
	' Get the model browser
	Dim oPane As BrowserPane = oDoc.BrowserPanes.Item("Model")
	Dim oSourceNode As BrowserNode = oPane.GetBrowserNodeFromObject(oMoveOcc)
	Dim oSubAssyNode As BrowserNode = oPane.GetBrowserNodeFromObject(oSubAssyOcc)
	Dim oTargetNode As BrowserNode
	Dim i As Long
	For i = oSubAssyNode.BrowserNodes.Count To 1 Step -1
		oTargetNode = oSubAssyNode.BrowserNodes.Item(i)
		oTargetNodeFN = oTargetNode.FullPath
		If oTargetNodeFN.Contains("Origin") Then
			oTargetNode = oSubAssyNode.BrowserNodes.Item(i)
			Logger.Debug("oTargetNodeFN" & oTargetNodeFN)
			Exit For
		End If
	Next
	Logger.Debug(oSourceNode.FullPath & " -> " & oTargetNode.FullPath)
	' Demote the occurrence
	Call oPane.Reorder(oTargetNode, False, oSourceNode)
End Function

Function GetParentOccurrenceAtLevel(ByVal leafOccurrence As ComponentOccurrence, ByVal levelUp As Integer) As ComponentOccurrence
    Dim currentOccurrence As ComponentOccurrence = leafOccurrence
    ' Traverse upward
    For i As Integer = 1 To levelUp
        If currentOccurrence.ParentOccurrence Is Nothing Then
            ' We've reached the top-level assembly
            Return Nothing
        End If
        currentOccurrence = currentOccurrence.ParentOccurrence
    Next
    Return currentOccurrence
End Function

Function PromoteSubAssy(oAssyName As String, Optional DeleteOpt As Boolean = False)
'PromoteSubAssy("K1_1:1,true")
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oDef As AssemblyComponentDefinition = oDoc.ComponentDefinition
	' Get the top level occurrence of an assembly
	Dim oSubAssyOcc As ComponentOccurrence
	' Insert the name of the subassembly as shown in the modeltree
	oSubAssyOcc = oDef.Occurrences.ItemByName(oAssyName)
	' Get the 2nd level occurrence under the assembly occurrence
	Dim oSubOcc As ComponentOccurrenceProxy
	Dim oPane As BrowserPane = oDoc.BrowserPanes.Item("Model")
	' Get the browser nodes corresponding to the two occurrences
	Dim oTargetNode As BrowserNode
	oTargetNode = oPane.GetBrowserNodeFromObject(oSubAssyOcc)
	For Each oSubOcc In oDef.Occurrences.ItemByName(oAssyName).SubOccurrences
		Dim oSourceNode As BrowserNode
		oSourceNode = oPane.GetBrowserNodeFromObject(oSubOcc)
		Call oPane.Reorder(oTargetNode, True, oSourceNode)
	Next

	If DeleteOpt Then
		oSubAssyOcc.Delete()
	End If
End Function