Sub Main() 'Finalize
'MessageBox.Show("ajetaanko main fin", "obs")
SharedVariable("PreventPartAccCalc") = 1 'esto aliosien eqal/update verticals / pakotus päälle 'SharedVariable.Remove("PreventPartAccCalc")
Logger.Debug("Starting Finalize -rule from main level: " & Now)
ThisApplication.UserInterfaceManager.UserInteractionDisabled = True
'ThisApplication.CommandManager.ControlDefinitions.Item("AppAllWorkfeaturesCmd").Execute
StartTime = Now : auto = iLogicVb.Automation
addIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
Dim iLogic As Object = addIn.Automation
Dim myArrayList As New ArrayList

Dim ruleName As String = "FinalizeEnd" ' Set Rule name
Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument
For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
	On Error Resume Next
	If Not myArrayList.Contains(oRefDoc.FullFileName)
		myArrayList.Add(oRefDoc.FullFileName)
	End If
	Dim rule As Object
	rule = auto.GetRule(oRefDoc, ruleName)
	If (rule Is Nothing) Then
	Else
		Logger.Debug(oRefDoc.DisplayName & " : " & oRefDoc.FullFileName & " :" & Now)
		Dim i As Integer
		i = auto.RunRuleDirect(rule)
	End If
Next

MultiValue.List("Filelist") = myArrayList
'iLogicVb.UpdateWhenDone = True

ThisApplication.UserInterfaceManager.UserInteractionDisabled = False
iLogicVb.RunRule("ParametrinHaku")

If initRun = 0 Then 'tarvitaanko muita?!
	iLogicVb.RunRule("iProps")
	initRun = 1
End If


'[listakäsittely	
Dim PurlinsA As New ArrayList : Dim PurlinsS As New ArrayList
Dim CrossarmsA As New ArrayList : Dim CrossarmsS As New ArrayList
Dim BracesA As New ArrayList : Dim BracesS As New ArrayList
Dim WindBracesA As New ArrayList : Dim WindBracesS As New ArrayList
Dim WallBracesA As New ArrayList : Dim WallBracesS As New ArrayList
Dim RidgeTubeA As New ArrayList : Dim EaveTubeA As New ArrayList
Dim AllLines As New ArrayList

Dim oReadTextPara As String = SDA_PLACEMENT
Dim lines() As String = oReadTextPara.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)

For i = 0 To lines.Length - 1
	oLine = lines(i)
	AllLines.Add(oLine)
	If oLine.Contains("purlin_auto") Then
		PurlinsA.Add(oLine)
	ElseIf oLine.Contains("purlin_single") Then
		PurlinsS.Add(oLine)
	ElseIf oLine.Contains("crossarm_auto") Then
		CrossarmsA.Add(oLine)
	ElseIf oLine.Contains("crossarm_single") Then
		CrossarmsS.Add(oLine)
	ElseIf oLine.Contains("=brace_auto") Then
		BracesA.Add(oLine)
	ElseIf oLine.Contains("=brace_single") Then
		BracesS.Add(oLine)
	ElseIf oLine.Contains("windbrace_auto") Then
		WindBracesA.Add(oLine)
	ElseIf oLine.Contains("windbrace_single") Then
		WindBracesS.Add(oLine)
	ElseIf oLine.Contains("wallbrace_auto") Then
		WallBracesA.Add(oLine)
	ElseIf oLine.Contains("wallbrace_single") Then
		WallBracesS.Add(oLine)
	ElseIf oLine.Contains("ridgetube_auto") Then
		RidgeTubeA.Add(oLine)
	ElseIf oLine.Contains("eavetube_auto") Then
		EaveTubeA.Add(oLine)
	End If
Next
MultiValue.List("Lines") = AllLines

If cui_jp_updateLines Then
	PurlinsA_txt = PurlinsA(0)
	CrossarmsA_txt = CrossarmsA(0)
	BracesA_txt = BracesA(0)
	WindBracesA_txt = WindBracesA(0)
	WallBracesA_txt = WallBracesA(0)
	RidgeTubeA_txt = RidgeTubeA(0)
	EaveTubeA_txt = EaveTubeA(0)
End If
']



Logger.Debug("Total Time is all Finalize rules [main] : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub


Function ReturnFullName(oSearch As String)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Try
			FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			If FullFileName.contains(oSearch) Then : 'Logger.Debug(FullFileName)
				Return FullFileName
			End If
		Catch
			Logger.Debug("Error!! getting filename: " & oSearch)
		End Try
	Next
End Function