Sub main()
FixRotation("brace.ipt")
DeleteBrokenCons()
End Sub
Function DeleteBrokenCons()
	For Each oConstraint In ThisApplication.ActiveDocument.ComponentDefinition.Constraints
		Try : Eka = oConstraint.OccurrenceOne.Name : Catch : Eka = "-" : End Try
		Try : Toka = oConstraint.OccurrenceTwo.Name : Catch : Toka = "-" : End Try
		Logger.Debug("3d rajoite " & Eka & " vs " & Toka)
		Logger.Debug(oConstraint.HealthStatus) 'rikki 11786, kunnossa 11778
		If oConstraint.HealthStatus = 11786 Or oConstraint.HealthStatus = 11780 Then
			oConstraint.Delete
		End If
	Next
End Function

Function FixRotation(oSearch As String)
	For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		If Not TypeOf oOccurrence.Definition Is VirtualComponentDefinition And Not TypeOf oOccurrence.Definition Is WeldsComponentDefinition Then
			FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			If FullFileName.contains(oSearch) Then : 'Logger.Debug(FullFileName)
				YakselinX = GetOccTransMatrix(oOccurrence.Name, {1, 2 })
				If YakselinX>0 Then
					Logger.Debug("rotation comp :" & oOccurrence.Name & "y:" & YakselinX)
					RotateOccTransMatrix(oOccurrence.Name, 180, "Z")
				End If
			End If
		End If
	Next
End Function
Function RotateOccTransMatrix(oOccuName As String, Kulma As Double, akseli As String)
	Dim oComp As ComponentOccurrence = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccuName)
	Dim Pos As Matrix = oComp.Transformation
	oX = Pos.Cell(1, 4)
	oY = Pos.Cell(2, 4)
	oZ = Pos.Cell(3, 4)
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	If akseli = "X" Then
		oXX = 1
	ElseIf akseli = "Y" Then
		oYY = 1
	Else
		oZZ = 1
	End If
	Dim oTempMatrix As Matrix = oTG.CreateMatrix
	oTempMatrix.SetToRotation(Kulma * PI / 180, oTG.CreateVector(oXX, oYY, oZZ), oTG.CreatePoint(oX, oY, oZ))
	Dim oTransMatrix As Matrix = oTG.CreateMatrix
	oTransMatrix.TransformBy(oTempMatrix)
	Pos.TransformBy(oTransMatrix)
	oComp.Transformation = Pos
End Function
Function GetOccTransMatrix(oOccuName As String, Optional oReturnSolu As String() = Nothing, Optional Debuggaus As Boolean = False)
	Dim oComp1 As ComponentOccurrence
	Try
		oComp = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccuName)
	Catch
		Logger.Debug("GetOccTransMatrix: not occ :" & oOccuName)
		Exit Function
	End Try
	Dim oMatrix As Matrix = oComp.Transformation
	Dim i As Integer
	'	If Debuggaus Then
	'		For i = 1 To 4
	'			Logger.Debug( _
	'			Format(oMatrix.Cell(i, 1), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 2), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 3), "0.00") & ", " & _
	'			Format(oMatrix.Cell(i, 4), "0.00"))
	'		Next
	'	End If
	If Not oReturnSolu Is Nothing Then
		Return oMatrix.Cell(oReturnSolu(0), oReturnSolu(1))
	End If
End Function