Sub main()
DeleteAllByNme("Mirrored ", "start")
End Sub

Function DeleteAllByNme(oFilter As String, Optional oType As String = "")
	
	For Each oOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Dim oName As String = oOccurrence.Name

		If oType = "start" Then
			testi = Left(oName, Len(oFilter))
			Logger.Debug(oFilter & " vs " & testi)
			If Left(oName, Len(oFilter)) = oFilter Then
				oDelete = True
			Else
				oDelete = False
			End If
		End If

		If oDelete Then
			Logger.Debug("Deleting by filter " & oFilter & " occ: " & oName)
			Try : Component.InventorComponent(oName).Delete() : Catch : End Try
		End If

		'		If oName.Contains(ID) Then
		'			Logger.Debug("Deleting ID " & ID & " occ: " & oName)
		'			Try : Component.InventorComponent(oName).Delete() : Catch : End Try
		'		End If
	Next
End Function