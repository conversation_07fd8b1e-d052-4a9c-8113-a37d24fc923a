Imports System.IO
Sub main()
Try
	NumberListEdit = SharedVariable("NumberListEdit")
Catch
	BUfile = ThisDoc.Path & "\data.csv"
	Logger.Debug("Error no shared variable NumberListEdit using file instead :" & BUfile)
	NumberListEdit = ImportCSVToDictionary(BUfile)
End Try

Dim filteredValues As New List(Of String)()
Yvalues = GetYCoordList()

Txt2filu = ThisDoc.PathAndFileName(True) & "|1.0|0.0|0.0|0.0|0.0|1.0|0.0|0.0|0.0|0.0|1.0|0.0|0|0|0|1.0"
'Txt2filu += vbLf & "C:\Vault_BH\Designs\Src\floor.ipt|1.0|0.0|0.0|0.0|0.0|1.0|0.0|0.0|0.0|0.0|1.0|0.0|0|0|0|1.0"

For Each kvp As KeyValuePair(Of String, Object) In NumberListEdit
	oModuleName = kvp.Key
	ModuleNum = kvp.Key.Split(New Char() {"/"c })
	FrameNum = CInt(ModuleNum(0).Replace("f", ""))
	oFileFullName = kvp.Value(3)
	oMatrix = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, oModuleName)
	Yvalue = Yvalues(FrameNum - 1)
	oText = ConvInvMatrix2Navis(oMatrix, Yvalue)
	Txt2filu += vbLf & oFileFullName & oText
Next

System.IO.File.WriteAllText("C:\TEMP\assemblies.txt", Txt2filu)


Logger.Debug("Txt2filu:" & Txt2filu)

'matrix_S = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/s") ': ShowMatrix(matrix_S,"S")
'matrix_K1 = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/p1k") ': ShowMatrix(matrix_K1, "matrix_K1")
'matrix_H = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/h") ' : ShowMatrix(matrix_H,"matrix_H")
'matrix_K1Mir = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/p1kMir") ': ShowMatrix(matrix_K1Mir,"matrix_K1Mir")
'matrix_SMir = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/sMir")' : ShowMatrix(matrix_SMir,"Smir")
'matrix_St = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/S11_0075-92_adet:1")' : ShowMatrix(matrix_St,"S t")
'matrix_K1t = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/K11_0075-92_adet:1")': ShowMatrix(matrix_K1t,"matrix_K1t")
'matrix_Ht = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/H11_0075-92_adet:1") ' : ShowMatrix(matrix_Ht,"matrix_Ht")
'matrix_K1Mirt = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/K19_0075-92_adet:1") ': ShowMatrix(matrix_K1Mirt, "matrix_K1Mirt")
'matrix_SMirt = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1/S112_0075-92_adet:1")' : ShowMatrix(matrix_SMirt,"Smir t")

End Sub
Function ConvInvMatrix2Navis(oMatrix As Matrix, Y As Double)
	Erotin = "|"
	oXAxisX = oMatrix.Cell(1, 1)
	oXAxisY = oMatrix.Cell(2, 1)
	oXAxisZ = oMatrix.Cell(3, 1)
	oYAxisX = oMatrix.Cell(1, 2)
	oYAxisY = oMatrix.Cell(2, 2)
	oYAxisZ = oMatrix.Cell(3, 2)
	oZAxisX = oMatrix.Cell(1, 3)
	oZAxisY = oMatrix.Cell(2, 3)
	oZAxisZ = oMatrix.Cell(3, 3)
	oOriginX = oMatrix.Cell(1, 4)
	oOriginY = Y 'oMatrix.Cell(2, 4)
	oOriginZ = oMatrix.Cell(3, 4)
	oText = Erotin & oXAxisX & Erotin & oXAxisY & Erotin & oXAxisZ & Erotin & 0 & Erotin & oYAxisX & Erotin & oYAxisY & Erotin & oYAxisZ & Erotin & 0 _
	& Erotin & oZAxisX & Erotin & oZAxisY & Erotin & oZAxisZ & Erotin & 0 & Erotin & oOriginX & Erotin & oOriginY & Erotin & oOriginZ & Erotin & 1
	'Logger.Debug("oText:" & oText)
	Return oText
End Function

Function ShowMatrix(oMatrix As Matrix, Optional oMDisplayName As String = "T")
	Dim i As Integer
	Logger.Debug(oMDisplayName & ":")
	For i = 1 To 4
		Logger.Debug( _
		Format(oMatrix.Cell(i, 1), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 2), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 3), "0.00") & ", " & _
		Format(oMatrix.Cell(i, 4), "0.00"))
	Next
End Function
Function GetOccurrenceTransMatrixByPath(oCompDef As ComponentDefinition, FullPath As String, Optional Result As String = "")
	Dim Occ As ComponentOccurrence
	Dim SubOcc As ComponentOccurrence
	Dim SubCompDef As ComponentDefinition
	Dim PathParts() As String
	' Split the full path into parts
	PathParts = Split(FullPath, "/")
	' Initialize the current component definition
	Dim CurrentCompDef As ComponentDefinition = oCompDef
	' Iterate through the path parts
	For i As Integer = 0 To UBound(PathParts)
		Dim Found As Boolean = False
		' Iterate through occurrences in the current component definition
		For Each Occ In CurrentCompDef.Occurrences
			If Occ.Name = PathParts(i) Then
				If i = UBound(PathParts) Then

					If Result = "X" Then
						Return Occ.Transformation.Translation.X
					ElseIf Result = "Y" Then
						Return Occ.Transformation.Translation.Y
					ElseIf Result = "Z" Then
						Return Occ.Transformation.Translation.Z
					Else
						Return Occ.Transformation
					End If

				Else
					' Otherwise, traverse into the subassembly
					SubCompDef = Occ.Definition
					CurrentCompDef = SubCompDef
					Found = True
					Exit For
				End If
			End If
		Next
		' If the part of the path is not found, exit the loop
		If Not Found Then Exit Function
	Next
	' If no matching occurrence is found, return Nothing
	Return Nothing
End Function
Function GetYCoordList()
	Dim subAsmOccurrence As ComponentOccurrence
	Dim partOccurrence As ComponentOccurrence
	Dim mainAsm As AssemblyDocument = ThisApplication.ActiveDocument
	Dim myArrayList As New ArrayList
	For i = 1 To totalFramePcs
		partOccurrence = mainAsm.ComponentDefinition.Occurrences.ItemByName("f" & i)
		Dim partMatrix As Matrix = partOccurrence.Transformation
		Y = partMatrix.Translation.Y
		myArrayList.Add(Y)
	Next
	Return myArrayList
End Function
Function GetTransMatrixOcc(oComp1 As ComponentOccurrence, Optional Result As String = "")
	If oComp1 Is Nothing Then
		Exit Function
	End If

	Dim pos1 As Vector = oComp1.Transformation.Translation

	If Result = "X" Then
		Return Round(pos1.X, 1)
	ElseIf Result = "Y" Then
		Round(pos1.Y, 1)
	ElseIf Result = "Z" Then
		Round(pos1.Z, 1)
	End If


End Function
Function GetSelectedOccTransMatrix(Occu As ComponentOccurrence)
	Dim showText As String = ""
	Dim oMatrix As Matrix = Occu.Transformation
	Dim oParent As ComponentOccurrence = Occu
	Dim oTopMatrix As Matrix = Occu.Transformation

	' Traverse up the hierarchy to the root assembly
	While Not oParent.ParentOccurrence Is Nothing
		oParent = oParent.ParentOccurrence
		oMatrix = oParent.Transformation
		oTopMatrix.TransformBy(oMatrix)
	End While

	' Extract the final transformation matrix and display it
	Dim i As Integer
	For i = 1 To 4
		Logger.Debug(
		Round(oTopMatrix.Cell(i, 1), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 2), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 3), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 4), 4))

		showText += Round(oTopMatrix.Cell(i, 1), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 2), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 3), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 4), 4) & ", " & vbLf
	Next

	' Copy the final transformation matrix to the clipboard
	My.Computer.Clipboard.SetText(showText)
	Return showText
End Function
Function GetOccByName(filter1 As String, filter2 As String, oAllOccur As ObjectCollection)
	For Each oOcc As ComponentOccurrence In oAllOccur
		oOccName = oOcc.Name
		oOccPathName1 = oOcc.OccurrencePath.Item(1).Name
		Try
			oOccPathName2 = oOcc.OccurrencePath.Item(2).Name
		Catch
			oOccPathName2 = ""
		End Try
		Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
		If oOccPathName1 = filter1 Then
			'
			Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
			Return oOcc

		End If
	Next
End Function
Function GetMinimumDistanceByOcc(Occ1 As ComponentOccurrence, Occ2 As ComponentOccurrence, DirectionFilter As String)

	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = pos1.X - pos2.X
		Dim deltaY As Double = pos1.Y - pos2.Y
		Dim deltaZ As Double = pos1.Z - pos2.Z
		Dim distance As Double = Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)
		' Print or use the distance value as needed
		MsgBox("Distance between Component1 and Component2: " & distance)
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function
Function GetAllOccurances(Optional debugMode As Boolean = False) As ObjectCollection
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Try
		' Top-level occurrences
		For Each oOcc As ComponentOccurrence In oDoc.ComponentDefinition.Occurrences
			ProcessOccurrence(oOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Log or handle the exception
		If debugMode Then Logger.Error("Error in GetAllOccurances: " & ex.Message)
	End Try
	Return oAllOccur
End Function
Sub ProcessOccurrence(ByVal oOcc As ComponentOccurrence, ByRef oAllOccur As ObjectCollection, Optional debugMode As Boolean = False)
	Try
		' Check for invalid or unresolved references
		If oOcc.Definition Is Nothing Then
			If debugMode Then Logger.Warn("Skipping occurrence due to invalid definition: " & oOcc.Name)
			Exit Sub
		End If
		' Skip Virtual and Weld Component Definitions
		If TypeOf oOcc.Definition Is VirtualComponentDefinition OrElse TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If debugMode Then Logger.Info("Skipping virtual or weld component: " & oOcc.Name)
			Exit Sub
		End If
		' Add the occurrence to the collection
		oAllOccur.Add(oOcc)
		If debugMode Then Logger.Debug("Added occurrence: " & oOcc.Name)
		' Process sub-occurrences recursively
		For Each oSubOcc As ComponentOccurrence In oOcc.SubOccurrences
			ProcessOccurrence(oSubOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Handle errors gracefully
		If debugMode Then Logger.Error("Error processing occurrence '" & oOcc.Name & "': " & ex.Message)
	End Try
End Sub
Function GetMinimumDistance(Occ1 As String, Occ2 As String, DirectionFilter As String)
	Dim oDoc As Document = ThisApplication.ActiveDocument
	Dim oComp1 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ1)
	Dim oComp2 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ2)

	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = pos1.X - pos2.X
		Dim deltaY As Double = pos1.Y - pos2.Y
		Dim deltaZ As Double = pos1.Z - pos2.Z
		Dim distance As Double = Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)
		' Print or use the distance value as needed
		MsgBox("Distance between Component1 and Component2: " & distance)
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function

Function ImportCSVToDictionary(filePath As String) As Dictionary(Of String, Object)
	'koe=ImportCSVToDictionary(ThisDoc.Path & "\data.csv")
	Dim dictionary As New Dictionary(Of String, Object)
	Using reader As New StreamReader(filePath)
		Dim Line As String
		Do While (reader.Peek() >= 0)
			Line = reader.ReadLine()
			Dim parts As String() = Line.Split(","c)
			Dim key As String = parts(0)
			Dim values As String() = parts.Skip(1).ToArray()
			dictionary.Add(key, values)
		Loop
	End Using
	Return dictionary
End Function