        Public Function ShowLargeMessageDialog(message As String, title As String, buttons As MessageBoxButtons, icon As MessageBoxIcon) As DialogResult
            Dim form As New Form()
            form.Text = title
            form.Size = New Size(800, 600)
            form.StartPosition = FormStartPosition.CenterParent
            form.MinimizeBox = False
            form.MaximizeBox = True
            form.FormBorderStyle = FormBorderStyle.Sizable

            ' Create a panel to hold all controls
            Dim panel As New TableLayoutPanel()
            panel.Dock = DockStyle.Fill
            panel.RowCount = 3
            panel.ColumnCount = 1
            panel.RowStyles.Add(New RowStyle(SizeType.Percent, 10))  ' Icon/Title row
            panel.RowStyles.Add(New RowStyle(SizeType.Percent, 80))  ' Text area
            panel.RowStyles.Add(New RowStyle(SizeType.Percent, 10))  ' Buttons row

            ' Add icon and summary label
            Dim topPanel As New Panel()
            topPanel.Height = 50
            topPanel.Dock = DockStyle.Fill

            Dim iconPictureBox As New PictureBox()
            iconPictureBox.Size = New Size(32, 32)
            iconPictureBox.Location = New System.Drawing.Point(10, 10)
            iconPictureBox.SizeMode = PictureBoxSizeMode.StretchImage

            ' Set icon based on MessageBoxIcon
            Select Case icon
                Case MessageBoxIcon.Question
                    iconPictureBox.Image = SystemIcons.Question.ToBitmap()
                Case MessageBoxIcon.Warning
                    iconPictureBox.Image = SystemIcons.Warning.ToBitmap()
                Case MessageBoxIcon.Error
                    iconPictureBox.Image = SystemIcons.Error.ToBitmap()
                Case MessageBoxIcon.Information
                    iconPictureBox.Image = SystemIcons.Information.ToBitmap()
            End Select

            Dim summaryLabel As New System.Windows.Forms.Label()
            summaryLabel.Text = $"Found {message.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries).Length} items to display"
            summaryLabel.Location = New System.Drawing.Point(50, 15)
            summaryLabel.AutoSize = True

            topPanel.Controls.Add(iconPictureBox)
            topPanel.Controls.Add(summaryLabel)

            ' Create scrollable text box
            Dim textBox As New System.Windows.Forms.TextBox()
            textBox.Multiline = True
            textBox.ScrollBars = ScrollBars.Both
            textBox.ReadOnly = True
            textBox.Text = message
            textBox.Dock = DockStyle.Fill
            textBox.Font = New Font("Consolas", 9)
            textBox.WordWrap = False

            ' Create button panel
            Dim buttonPanel As New FlowLayoutPanel()
            buttonPanel.FlowDirection = FlowDirection.RightToLeft
            buttonPanel.Dock = DockStyle.Fill
            buttonPanel.Padding = New Padding(10)

            ' Add buttons based on MessageBoxButtons
            Select Case buttons
                Case MessageBoxButtons.YesNo
                    Dim yesButton As New System.Windows.Forms.Button()
                    yesButton.Text = "Yes"
                    yesButton.DialogResult = DialogResult.Yes
                    yesButton.Size = New Size(75, 30)

                    Dim noButton As New System.Windows.Forms.Button()
                    noButton.Text = "No"
                    noButton.DialogResult = DialogResult.No
                    noButton.Size = New Size(75, 30)

                    buttonPanel.Controls.Add(noButton)
                    buttonPanel.Controls.Add(yesButton)
                    form.AcceptButton = yesButton
                    form.CancelButton = noButton

                Case MessageBoxButtons.OK
                    Dim okButton As New System.Windows.Forms.Button()
                    okButton.Text = "OK"
                    okButton.DialogResult = DialogResult.OK
                    okButton.Size = New Size(75, 30)

                    buttonPanel.Controls.Add(okButton)
                    form.AcceptButton = okButton
            End Select

            ' Add copy to clipboard button
            Dim copyButton As New System.Windows.Forms.Button()
            copyButton.Text = "Copy to Clipboard"
            copyButton.Size = New Size(120, 30)
            AddHandler copyButton.Click, Sub() Clipboard.SetText(message)
            buttonPanel.Controls.Add(copyButton)

            ' Add controls to table layout
            panel.Controls.Add(topPanel, 0, 0)
            panel.Controls.Add(textBox, 0, 1)
            panel.Controls.Add(buttonPanel, 0, 2)

            form.Controls.Add(panel)
            Debugger.Break()
            Return form.ShowDialog()
        End Function

    End Class
End Namespace