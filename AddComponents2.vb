Sub main() 'AddComponents2 
StartTime = Now : CounterTime = Now

If initRun = 0 Then 'ei ole aina ajettu?!
	MessageBox.Show("Run finalize rule before generation", "Error")
	Exit Sub
End If

'[listakäsittely	
Dim PurlinsA As New ArrayList : <PERSON><PERSON> Pur<PERSON>S As New ArrayList
Dim CrossarmsA As New ArrayList : Dim CrossarmsS As New ArrayList
Dim BracesA As New ArrayList : Dim BracesS As New ArrayList
Dim WindBracesA As New ArrayList : Dim WindBracesS As New ArrayList
Dim WallBracesA As New ArrayList : Dim WallBracesS As New ArrayList
Dim RidgeTubeA As New ArrayList : Dim EaveTubeA As New ArrayList
Dim AllLines As New ArrayList

Dim oReadTextPara As String = SDA_PLACEMENT
Dim lines() As String = oReadTextPara.Split(vbCrLf.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)

For i = 0 To lines.Length - 1
	oLine = lines(i)
	AllLines.Add(oLine)
	If oLine.Contains("purlin_auto") Then
		PurlinsA.Add(oLine)
	ElseIf oLine.Contains("purlin_single") Then
		PurlinsS.Add(oLine)
	ElseIf oLine.Contains("crossarm_auto") Then
		CrossarmsA.Add(oLine)
	ElseIf oLine.Contains("crossarm_single") Then
		CrossarmsS.Add(oLine)
	ElseIf oLine.Contains("=brace_auto") Then
		BracesA.Add(oLine)
	ElseIf oLine.Contains("=brace_single") Then
		BracesS.Add(oLine)
	ElseIf oLine.Contains("windbrace_auto") Then
		WindBracesA.Add(oLine)
	ElseIf oLine.Contains("windbrace_single") Then
		WindBracesS.Add(oLine)
	ElseIf oLine.Contains("wallbrace_auto") Then
		WallBracesA.Add(oLine)
	ElseIf oLine.Contains("wallbrace_single") Then
		WallBracesS.Add(oLine)
	ElseIf oLine.Contains("ridgetube_auto") Then
		RidgeTubeA.Add(oLine)
	ElseIf oLine.Contains("eavetube_auto") Then
		EaveTubeA.Add(oLine)
	End If
Next
MultiValue.List("Lines") = AllLines


If cui_jp_updateLines Then
	PurlinsA_txt = PurlinsA(0)
	CrossarmsA_txt = CrossarmsA(0)
	BracesA_txt = BracesA(0)
	WindBracesA_txt = WindBracesA(0)
	WallBracesA_txt = WallBracesA(0)
	RidgeTubeA_txt = RidgeTubeA(0)
	EaveTubeA_txt = EaveTubeA(0)
End If
']
Dim oProgressBar As Inventor.ProgressBar
oProgressBarSteps = 7
oProgressBar = ThisApplication.CreateProgressBar(False, oProgressBarSteps, "Jointing parts group generation")
oProgressBarStep = 0

GroundCompos("f") 'kiinnitä kehät
'SharedVariable.Remove("PointtiHash")

PointtiHash = PointCoorHash("joint_") 'pakko päivitys
PointtiHashCORNER = PointCoorHash("CORNER_NEXT")
'Try
'	PointtiHash = SharedVariable("PointtiHash")
'Catch
'	PointtiHash = PointCoorHash("joint_")
'	SharedVariable("PointtiHash") = PointtiHash
'End Try
'Try
'	PointtiHashCORNER = SharedVariable("PointtiHashCORNER")
'Catch
'	PointtiHashCORNER = PointCoorHash("CORNER_NEXT")
'	SharedVariable("PointtiHashCORNER") = PointtiHashCORNER
'End Try
'PointtiHashCORNER = PointCoorHash("CORNER_NEXT")
'cui_jp_gen_list = AnalyzeGenList("Purlin", PurlinsA_txt)

If cui_jp_boolean_all = False Or cui_jp_boolean_purlin = True Then
	'Automatic Purlins
	oProgressBarStep += 1 : oProgressBar.Message = ("Purlins  " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss")) : oProgressBar.UpdateProgress
	PlaceAutoPurlin(PurlinsA_txt, PointtiHash)
	PurlinsTotTime = "  [Purlins " & Round((Now().Subtract(StartTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If

'Automatic Crossarms
If cui_jp_boolean_all = False Or cui_jp_boolean_crossarm = True Then
	oProgressBarStep += 1 : oProgressBar.Message = ("Crossarms  " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & PurlinsTotTime & "]") : oProgressBar.UpdateProgress
	PlaceAutoCrossarm(CrossarmsA_txt, PointtiHash)
	CrossarmsTotTime = PurlinsTotTime & " Crossarms " & Round((Now().Subtract(CounterTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If

'Automatic Braces
If cui_jp_boolean_all = False Or cui_jp_boolean_brace = True Then
	oProgressBarStep += 1 : oProgressBar.Message = ("Braces  " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & CrossarmsTotTime & "]") : oProgressBar.UpdateProgress
	PlaceAutoBrace(BracesA_txt, PointtiHash)
	BracesTotTime = CrossarmsTotTime & " Braces " & Round((Now().Subtract(CounterTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If

If cui_jp_boolean_all = False Or cui_jp_boolean_windbrace = True Then
	'	Automatic Wind braces
	oProgressBarStep += 1 : oProgressBar.Message = ("Wind braces  " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & BracesTotTime & "]") : oProgressBar.UpdateProgress
	oCal = 0

	For Each oVal In ReturnListausWindBraceSingle(WindBracesA_txt)
		If oCal = 0 Then 'piste avaruus eri nimillä
			PlaceWindBraceCompoByTxtPara(oVal)
		Else
			PlaceWindBraceCompoByTxtPara(oVal, oCal)
		End If
		oCal += 1
	Next 'AnalyzeGenList("WindBrace", WindBracesA_txt)
	WindTotTime = BracesTotTime & " Wind braces " & Round((Now().Subtract(CounterTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If


'Automatic Wall braces
If cui_jp_boolean_all = False Or cui_jp_boolean_wallbrace = True Then
	oProgressBarStep += 1 : oProgressBar.Message = ("Wall braces  " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & WindTotTime & "]") : oProgressBar.UpdateProgress
	oCal = 0
	'	WallBracesA_txt, WallBracesA(0)
	For Each oValB In ReturnListausWallBraceSingle(WallBracesA_txt)
		If oCal = 0 Then
			PlaceoneTxtLine(oValB, PointtiHash)
		Else
			
			PlaceoneTxtLine(oValB, PointtiHash, oCal)
		End If
		oCal += 1
	Next
	WallTotTime = WindTotTime & " Wall braces " & Round((Now().Subtract(CounterTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If


'Automatic Ridge tubes
If cui_jp_boolean_all = False Or cui_jp_boolean_ridgetube = True Then
	oProgressBarStep += 1 : oProgressBar.Message = ("Ridge tubes " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & WallTotTime & "]") : oProgressBar.UpdateProgress
	oCal = 0 : RidgeList = ReturnListausSingle(RidgeTubeA_txt, 1)
	SharedVariable.Remove("DuplikaattiLista")
	Dim DuplikaattiLista As Hashtable = New Hashtable
	SharedVariable("DuplikaattiLista") = DuplikaattiLista

	For Each oValRT In RidgeList
		If oCal = 0 Then
			PlaceoneTxtLineuCS(oValRT, PointtiHashCORNER)
		Else
			PlaceoneTxtLineuCS(oValRT, PointtiHashCORNER, oCal)
		End If
		oCal += 1
	Next
	RidgeTotTime = WallTotTime & " Ridge tubes " & Round((Now().Subtract(CounterTime)).TotalSeconds, 1) & "s /" : CounterTime = Now
End If

'Automatic Eave tubes
If cui_jp_boolean_all = False Or cui_jp_boolean_eavetube = True Then
	oProgressBarStep += 1 : oProgressBar.Message = ("Eave tubes " & " Step " & oProgressBarStep & " Of " & oProgressBarSteps & " " & Now.ToString("HH:mm:ss") & RidgeTotTime & "]") : oProgressBar.UpdateProgress
	oCal = 0
	SharedVariable.Remove("DuplikaattiLista")
	Dim DuplikaattiLista As Hashtable = New Hashtable
	SharedVariable("DuplikaattiLista") = DuplikaattiLista

	For Each oValRT In ReturnListausSingle(EaveTubeA_txt, 2)
		If oCal = 0 Then
			PlaceoneTxtLineuCS(oValRT, PointtiHash)
		Else
			PlaceoneTxtLineuCS(oValRT, PointtiHash, oCal)
		End If
		oCal += 1
	Next
	'ei viimeistä
End If

If cui_jp_boolean_all = False Or cui_jp_boolean_pocketxt = True Then
	e_s_up_G_B = ParaValue("e_s_up_G_B", SharedVariable("AllParamsDict"), 0)
	SiirtymäY = e_s_up_G_B / 2 + 10
	XTList = ReturnListausSingle("e_xt;SDA_from=f1/s/joint_xt;SDA_to=f2/s/joint_xt;SDA_size=100x50x3x0;G_L=3000;SDA_xoffset=0;SDA_yoffset=" & SiirtymäY & ";SDA_zoffset=102;totalpcs=" & SDA_pcs, 1)
	LaskuriXT = 1 : parttiSrc = "C:\Vault_BH\Designs\Src\gable_end_pillar\pocket_profile.ipt"
	SharedVariable.Remove("DuplikaattiListaXT")
	Dim DuplikaattiLista As Hashtable = New Hashtable
	SharedVariable("DuplikaattiListaXT") = DuplikaattiLista
	For Each oVal In XTList
		PlaceTxtLineByFile_UCS(oVal, PointtiHash, parttiSrc, LaskuriXT)
		LaskuriXT += 1
	Next
End If

If cui_jp_boolean_all = True And cui_jp_boolean_purlin = False And cui_jp_boolean_crossarm = False And cui_jp_boolean_brace = False And cui_jp_boolean_windbrace = False _
	And cui_jp_boolean_wallbrace = False And cui_jp_boolean_ridgetube = False And cui_jp_boolean_eavetube = False Then

	Dim Options() As String = {"Purlin", "Crossarm", "WindBrace", "WallBrace", "Ridge", "Eave", "Brace" }
	Result1 = InputListBox("Please select", Options, Options(0), Title := "Generate list", ListName := "List")
	'perform actions on basis of multiple choice. First all documents in the assembly are counted, then the objects are turned off whilst the user is presented with a progress bar
	Select Case Result1
		Case options(0)
			cui_jp_gen_list = AnalyzeGenList("Purlin", PurlinsA_txt)
		Case options(1)
			cui_jp_gen_list = AnalyzeGenList("Crossarm", CrossarmsA_txt)
		Case options(2)
			cui_jp_gen_list = AnalyzeGenList("WindBrace", WindBracesA_txt)
		Case options(3)
			cui_jp_gen_list = AnalyzeGenList("WallBrace", WallBracesA_txt)
		Case options(4)
			cui_jp_gen_list = AnalyzeGenList("Ridge", RidgeTubeA_txt)
		Case options(5)
			cui_jp_gen_list = AnalyzeGenList("Eave", EaveTubeA_txt)
		Case options(6)
			'cui_jp_gen_list = AnalyzeGenList("Brace", BracesA_txt)
			PlaceAutoBrace(BracesA_txt, PointtiHash)
	End Select
End If

oProgressBar.Close

Logger.Debug("Add Components time : " & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Sub

Function PlaceWindBraceCompoByTxtPara(TxtPara As String, Optional oNewOccName As String = "")

	Dim PituusTaulu As Hashtable = New Hashtable : laskuriOccPcs = 1
	oOccName = ReturnTXT(TxtPara, "oOccName")
	oSDA_from = ReturnTXT(TxtPara, "SDA_from") 'oPointDataArraysis
	Dim oData() As String = oSDA_from.Split("/"c)

	oSDA_to = ReturnTXT(TxtPara, "SDA_to")
	If oSDA_to Is Nothing Then : Exit Function : End If
	Dim oDataTo() As String = oSDA_to.Split("/"c)

	oFrame = oData(0)
	oFrameTo = oDataTo(0)

	oVrt1 = CDbl(oFrame.Replace("f", ""))
	oVrt2 = CDbl(oFrameTo.Replace("f", ""))
	If oVrt2 > oVrt1 Then
		LisäysFrom = ""
		LisäysTo = "-"
	Else
		LisäysFrom = "-"
		LisäysTo = ""
	End If

	oModule = oData(1)
	oSDA_from_wp = oData(2) & LisäysFrom
	oSDA_from_wp_end2 = oData(2) & LisäysTo 'toisessa päädyssä toisin päin

	oModuleTo = oDataTo(1)
	oSDA_to_wp = oDataTo(2) & LisäysTo
	oSDA_to_wp_end2 = oDataTo(2) & LisäysFrom  'toisessa päädyssä toisin päin

	oSDA_from_mir = ReturnTXT(TxtPara, "SDA_from_mir")
	Dim oDataMir() As String = oSDA_from_mir.Split("/"c)
	oFrameMir = oDataMir(0)
	oModuleMir = oDataMir(1)
	oSDA_from_wp_mir = oDataMir(2) & LisäysTo 'obs toisin päin aina
	oSDA_from_wp_mir_end2 = oDataMir(2) & LisäysFrom

	oSDA_to_mir = ReturnTXT(TxtPara, "SDA_to_mir")
	Dim oDataToMir() As String = oSDA_to_mir.Split("/"c)
	oFrameToMir = oDataToMir(0)
	oModuleToMir = oDataToMir(1)
	oSDA_to_wp_mir = oDataToMir(2) & LisäysFrom 'obs toisin päin aina
	oSDA_to_wp_mir_end2 = oDataToMir(2) & LisäysTo 'obs toisin päin aina

	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")

	oSDA_end2generate = ReturnTXT(TxtPara, "SDA_end2generate")
	ototalpcs = ReturnTXT(TxtPara, "totalpcs")

	PointtiHash = PointCoorHash("wind_brace_") 'obs muissa 

	'[initial position calculation
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	oStartPoint = oTG.CreatePoint(0, 0, 0)
	Dim oMatrix As Matrix = oTG.CreateMatrix
	Dim oXAxis As Vector
	Dim oYAxis As Vector
	Dim oZAxis As Vector
	oXAxis = oTG.CreateVector(0, 0, 1)
	oYAxis = oTG.CreateVector(1, 0, 0)
	oZAxis = oTG.CreateVector(0, 1, 0)

	oMatrix.SetCoordinateSystem(oStartPoint, oXAxis, oYAxis, oZAxis)
	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)
	Dim Pos = ThisAssembly.Geometry.Matrix(cells(0), cells(1), cells(2), oStartPoint.X * 10,
	cells(4), cells(5), cells(6), oStartPoint.Y * 10,
	cells(8), cells(9), cells(10), oStartPoint.Z * 10,
	0, 0, 0, 1)

	Try
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	Catch
		Dim ArrayOcc() As String = oOccName.Split(New Char() {"_"c })
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(ArrayOcc(0))
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End Try


	oPointsDistance = Round(CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash))

	oDeltaZ = DeltaZ(oSDA_from, oSDA_to, PointtiHash)
	'Logger.Debug("delta:" & oDeltaZ & " # " & oSDA_from & " # " & oSDA_to)
	'Logger.Debug("dist:" & oPointsDistance & " # " & uusiName)

	If oNewOccName = "" Then 'ekassa aina tarkistetaan koffan occ pituus
		If oPointsDistance>500 Then 'pieni muotoinen virheen korjaus jos ei saa pisteen pituutta
			Parameter(oOccName, "G_L") = oPointsDistance
		Else
			Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
		End If
		PituusTaulu.Add(Round(oPointsDistance), oFullFileName) 'millin tarkkkuus
	Else 'kaikki muut
		uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_" & oModule & "_" & oSDA_from_wp & "_" & laskuriOccPcs & "_" & oFrameTo
		Logger.Debug("oDeltaZ:" & oDeltaZ)

		If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
			UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
			SaveFilu(oFullFileName, UusiFullFileName)
			PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)

			If oDeltaZ<-1000 Then
				Components.Add(uusiName, UusiFullFileName, position := Pos)
			Else
				Components.Add(uusiName, UusiFullFileName, position := Pos)
			End If
			If oPointsDistance>500 Then
				Parameter(uusiName, "G_L") = oPointsDistance
			Else
				Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
			End If
			'CompoArguJointOcc = uusiName.ToString
		Else 'käytetään vanhaa
			If oDeltaZ<-1000 Then

				Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
			Else
				Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
			End If

		End If
		oOccName = uusiName
	End If

	oOccName_mir = oOccName & "_mir" 'aina peilaus

	Components.Add(oOccName_mir, PituusTaulu(oPointsDistance), position := Pos)


	If oSDA_end2generate = 1 Then 'pääty 2:n tuulisiteet
		oOccName_end2 = oOccName & "_end2"
		Components.Add(oOccName_end2, PituusTaulu(oPointsDistance), position := Pos)
		oOccName_end2_mir = oOccName & "_end2_mir" 'aina peilaus
		Components.Add(oOccName_end2_mir, PituusTaulu(oPointsDistance), position := Pos)
	End If

	oPointsDistance = CalcPointsDistance(oSDA_from & LisäysFrom, oSDA_to & LisäysTo, PointtiHash)
	If Abs(oPointsDistance - oSDA_length) >100 Then
		Parameter(oOccName, "G_L") = oPointsDistance
		Logger.Debug("Changing G_L to :" & oPointsDistance & " in " & oOccName)
	End If
	n = 0 ' obs tarkista assy level

	oConsName = oOccName & "#" & oSDA_from & LisäysFrom & "#" & oSDA_to & LisäysTo

	Dim CompoArguStartPoint As ComponentArgument = {oFrame, oModule }
	Dim CompoArguEndPoint As ComponentArgument = {oFrameTo, oModuleTo }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString
	Dim CompoArguStartPoint_mir As ComponentArgument = {oFrameMir, oModuleMir }
	Dim CompoArguEndPoint_mir As ComponentArgument = {oFrameToMir, oModuleToMir }
	Dim CompoArguJointOcc_mir As ComponentArgument = oOccName_mir.ToString

	If oDeltaZ<-1000 Then
		sijoitusPiste = "ucs_start2: Center Point"
	Else
		sijoitusPiste = "ucs_start: Center Point"
	End If

	Try
		Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, sijoitusPiste, CompoArguStartPoint, oSDA_from_wp & ": Center Point")
		Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, oSDA_to_wp & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara & vbLf & ex.Message)
	End Try

	oConsName_mir = oOccName & "#" & oFrameMir & "/" & oModuleMir & "/" & oSDA_from_wp_mir & "#" & oFrameToMir & "/" & oModuleToMir & "/" & oSDA_to_wp_mir

	Try
		Constraints.AddMate(oConsName_mir & "_point_mir", CompoArguJointOcc_mir, sijoitusPiste, CompoArguStartPoint_mir, oSDA_from_wp_mir & ": Center Point")
		Constraints.AddMate(oConsName_mir & "_dir_mir", CompoArguJointOcc_mir, "ucs_start: Y Axis", CompoArguEndPoint_mir, oSDA_to_wp_mir & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch ex As Exception
		Logger.Debug("Error in mir :" & TxtPara & vbLf & ex.Message)
	End Try

	If oSDA_end2generate = 1 Then
		Frame_End2_From = "f" & ototalpcs + 1 - CInt(oFrame.Replace("f", ""))
		Frame_End2_To = "f" & ototalpcs + 1 - CInt(oFrameTo.Replace("f", ""))
		Frame_End2_mir_From = "f" & ototalpcs + 1 - CInt(oFrameMir.Replace("f", ""))
		Frame_End2_mir_to = "f" & ototalpcs + 1 - CInt(oFrameToMir.Replace("f", ""))
		Dim CompoArguStartPoint_End2 As ComponentArgument = {Frame_End2_From, oModule }
		Dim CompoArguEndPoint_End2 As ComponentArgument = {Frame_End2_To, oModuleTo }
		Dim CompoArguJointOcc_End2 As ComponentArgument = oOccName_end2.ToString
		Dim CompoArguStartPoint_End2_mir As ComponentArgument = {Frame_End2_mir_From, oModuleMir }
		Dim CompoArguEndPoint_End2_mir As ComponentArgument = {Frame_End2_mir_to, oModuleToMir }
		Dim CompoArguJointOcc_End2_mir As ComponentArgument = oOccName_end2_mir.ToString

		oConsName = oOccName & "#" & Frame_End2_From & "/" & oModule & "/" & oSDA_from_wp_end2 & "#" & Frame_End2_To & "/" & oModuleTo & "/" & oSDA_to_wp_end2

		Try
			Constraints.AddMate(oConsName & "_point_end2", CompoArguJointOcc_End2, sijoitusPiste, CompoArguStartPoint_End2, oSDA_from_wp_end2 & ": Center Point")
			Constraints.AddMate(oConsName & "_dir_end2", CompoArguJointOcc_End2, "ucs_start: Y Axis", CompoArguEndPoint_End2, oSDA_to_wp_end2 & ": Center Point")
		Catch ex As Exception
			Logger.Debug("Error in end2: " & TxtPara & vbLf & ex.Message)
		End Try
		oConsName = oOccName & "#" & Frame_End2_mir_From & "/" & oModule & "/" & oSDA_from_wp_mir_end2 & "#" & Frame_End2_mir_to & "/" & oModuleTo & "/" & oSDA_to_wp_mir_end2

		Try
			Constraints.AddMate(oConsName & "_point_end2_mir", CompoArguJointOcc_End2_mir, sijoitusPiste, CompoArguStartPoint_End2_mir, oSDA_from_wp_mir_end2 & ": Center Point")
			Constraints.AddMate(oConsName & "_dir_end2_mir", CompoArguJointOcc_End2_mir, "ucs_start: Y Axis", CompoArguEndPoint_End2_mir, oSDA_to_wp_mir_end2 & ": Center Point")
		Catch ex As Exception
			Logger.Debug("Error in end2 mir :" & TxtPara & vbLf & ex.Message)
		End Try
	End If
End Function
Function PlaceAutoBrace(TxtPara As String, PointtiHash As Hashtable)
	Dim ConsNameList As New ArrayList
	'[initial position calculation 
	Dim k1_NodeTauluByRoof As Hashtable = New Hashtable
	Dim k2_NodeTauluByRoof As Hashtable = New Hashtable
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	oStartPoint = oTG.CreatePoint(0, 0, 0)
	Dim oMatrix As Matrix = oTG.CreateMatrix
	Dim oXAxis As Vector
	Dim oYAxis As Vector
	Dim oZAxis As Vector
	oXAxis = oTG.CreateVector(0, 1, 0)
	oYAxis = oTG.CreateVector(0, 0, 1)
	oZAxis = oTG.CreateVector(1, 0, 0)
	oMatrix.SetCoordinateSystem(oStartPoint, oXAxis, oYAxis, oZAxis)
	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)
	Dim Pos = ThisAssembly.Geometry.Matrix(cells(0), cells(1), cells(2), oStartPoint.X * 10,
	cells(4), cells(5), cells(6), oStartPoint.Y * 10,
	cells(8), cells(9), cells(10), oStartPoint.Z * 10,
	0, 0, 0, 1)
	']
	SDA_pcs = ReturnTXT(TxtPara, "totalpcs")

	If SDA_pcs = 0 Then
		Logger.Debug("Txt input is crossarm data, exiting from brace function")
		Exit Function
	End If
	oOccName = ReturnTXT(TxtPara, "oOccName")
	SDA_from = ReturnTXT(TxtPara, "SDA_from")
	oSDA_from = SDA_from 'tieto talteen korvauksien takia
	Dim oData() As String = SDA_from.Split("/"c)
	oFrame = oData(0)
	oModule = oData(1)
	oSDA_from_wp = oData(2)
	oFramNumTo = oFrame.Replace("f", "")
	loppuRaami = CDbl(oFramNumTo) + 1
	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")
	oSDA_size = ReturnTXT(TxtPara, "SDA_size")

	Dim PituusTaulu As Hashtable = New Hashtable
	oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
	oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	laskuriOccPcs = 1
	ekaOcc = 1

	If e_roof_pcs>0 Then 'tee loopppaus entä jos eri tahtia alapuolen kanssa tai siis ei ole
		k1_NodeTauluByRoof.Add(1, r1_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(1, r1_k2_brace_pcs) ' obs jos suorakatto niin arvo 1!
	End If
	If e_roof_pcs>1 Then
		k1_NodeTauluByRoof.Add(2, r2_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(2, r2_k2_brace_pcs)
	End If

	If e_roof_pcs>2 Then
		k1_NodeTauluByRoof.Add(3, r3_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(3, r3_k2_brace_pcs)
	End If

	For k As Integer = 1 To e_roof_pcs
		'[k1
		For NodeNum = 1 To k1_NodeTauluByRoof(k) 'k1
			LinjaLaskuri = 1
			If ekaOcc > 1 Then 'entä K2 & Node1
				SDA_from = oSDA_from.Replace("_1", "_" & NodeNum) 'obs nimeäminen pisteille f3/p1k/joint_crossarm_k1_1
			End If

			ekaOcc += 1 'konffa sijoittaa yhden occ paikalleen

			If SDA_from.contains("_up_") Then
				oSDA_to = SDA_from.replace("_up_", "_")
			Else
				oSDA_to = Left(SDA_from, InStrRev(SDA_from, "_", -1)) & "up_" & Right(SDA_from, Len(SDA_from) -InStrRev(SDA_from, "_", -1))
			End If

			oPointDataArray_to = oSDA_to.Split(New Char() {"/"c })
			oSDA_to_wp = oPointDataArray_to(2)
			laskuri = 0

			For l = 1 To CInt(SDA_pcs) -1
				StartFrameoNum = oFrame.Replace("f", "") + laskuri
				EndFrameoNum = StartFrameoNum + 1

				If k>1 Then 'kattoelementien määrä
					oRoofName = oModule.Replace("p1", "p" & k)
				Else
					oRoofName = oModule
				End If

				Dim CompoArguStartPoint As ComponentArgument = {"f" & StartFrameoNum, oRoofName }
				Dim CompoArguEndPoint As ComponentArgument = {"f" & EndFrameoNum, oRoofName }

				Dim CompoArguJointOcc As ComponentArgument

				oSDA_from_wp = SDA_from.Split(New Char() {"/"c })(2) 'k1:n nodet
				oSDA_to_wp = oSDA_to.Split(New Char() {"/"c })(2)

				oPointsDistance = Round(CalcPointsDistance("f" & StartFrameoNum & "/" & oRoofName & "/" & oSDA_from_wp, "f" & EndFrameoNum & "/" & oRoofName & "/" & oSDA_to_wp, PointtiHash))

				If laskuriOccPcs = 1 Then 'ekassa aina tarkistetaan koffan occ pituus
					If oPointsDistance>500 Then 'pieni muotoinen virheen korjaus jos ei saa pisteen pituutta
						Parameter(oOccName, "G_L") = oPointsDistance
						Parameter(oOccName, "SDA_size") = oSDA_size
					Else
						Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
					End If
					PituusTaulu.Add(Round(oPointsDistance), oFullFileName) 'millin tarkkkuus
					laskuriOccPcs = 2
					CompoArguJointOcc = oOccName.ToString
				Else 'kaikki muut
					uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_p" & k & "k1_joint_brace_" & NodeNum & "_" & laskuri & "_f" & l & "_k1"
					If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
						UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
						SaveFilu(oFullFileName, UusiFullFileName)
						PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)
						Components.Add(uusiName, UusiFullFileName, position := Pos)
						If oPointsDistance>500 Then
							Parameter(uusiName, "G_L") = oPointsDistance
							Parameter(uusiName, "SDA_size") = oSDA_size
						Else
							Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
						End If
						CompoArguJointOcc = uusiName.ToString
					Else 'käytetään vanhaa
						Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
						CompoArguJointOcc = uusiName.ToString
					End If
				End If

				Flippaus = False ' aina tässä niin kuten konffassa määrätty
				If laskuri Mod 2 = 0 And Flippaus Then 'parillinen ja kääntö 
					lahtoPiste = oSDA_to_wp
					loppuPiste = oSDA_from_wp
				ElseIf laskuri Mod 2 = 1 And Flippaus = False '(=pariton ei kääntöä siis myös)
					lahtoPiste = oSDA_to_wp
					loppuPiste = oSDA_from_wp
				Else
					lahtoPiste = oSDA_from_wp
					loppuPiste = oSDA_to_wp
				End If

				oConsName = "Brace_k" & k & "_" & laskuri & "#" & "f" & StartFrameoNum & "/" & oRoofName & "/" & lahtoPiste & "#" & "f" & EndFrameoNum & "/" & oRoofName & "/" & loppuPiste & "_L" & LinjaLaskuri

				ConsNameList.Add(oConsName)

				Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				laskuri += 1

			Next'frame
			LinjaLaskuri += 1
		Next'node alias linja k1
		']

		'[k2
		For NodeNum = 1 To k2_NodeTauluByRoof(k)

			If k = 2 And NodeNum = 1 Then

			End If

			SDA_from = oSDA_from.Replace("_1", "_" & NodeNum)
			LinjaLaskuri = 1

			'oSDA_from = oSDA_from.Replace("_k1_", "_k" & NodeNum & "_")

			ekaOcc += 1 'konffa sijoittaa yhden occ paikalleen

			If SDA_from.contains("_up_") Then
				oSDA_to = SDA_from.replace("_up_", "_")
			Else
				oSDA_to = Left(SDA_from, InStrRev(SDA_from, "_", -1)) & "up_" & Right(SDA_from, Len(SDA_from) -InStrRev(SDA_from, "_", -1))
			End If

			oPointDataArray_to = oSDA_to.Split(New Char() {"/"c })
			oSDA_to_wp = oPointDataArray_to(2)

			laskuri = 0

			For l = 1 To CInt(SDA_pcs) -1 'RoofNum

				StartFrameoNum = oFrame.Replace("f", "") + laskuri
				EndFrameoNum = StartFrameoNum + 1

				If k>1 Then 'kattoelementien määrä
					oRoofName = oModule.Replace("p1", "p" & k)
				Else
					oRoofName = oModule
				End If

				Dim CompoArguStartPoint As ComponentArgument = {"f" & StartFrameoNum, oRoofName }
				Dim CompoArguEndPoint As ComponentArgument = {"f" & EndFrameoNum, oRoofName }


				Dim CompoArguJointOcc As ComponentArgument

				oSDA_from_wp = SDA_from.Split(New Char() {"/"c })(2).replace("_crossarm_k1_", "_crossarm_k2_")
				oSDA_to_wp = oSDA_to.Split(New Char() {"/"c })(2).replace("_crossarm_k1_", "_crossarm_k2_")


				oPointsDistance = Round(CalcPointsDistance("f" & StartFrameoNum & "/" & oRoofName & "/" & oSDA_from_wp, "f" & EndFrameoNum & "/" & oRoofName & "/" & oSDA_to_wp, PointtiHash))

				If laskuriOccPcs = 1 Then 'ekassa aina tarkistetaan koffan occ pituus
					If oPointsDistance>500 Then 'pieni muotoinen virheen korjaus jos ei saa pisteen pituutta
						Parameter(oOccName, "G_L") = oPointsDistance
						Parameter(oOccName, "SDA_size") = oSDA_size
					Else
						Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
					End If
					PituusTaulu.Add(Round(oPointsDistance), oFullFileName) 'millin tarkkkuus
					laskuriOccPcs = 2
					CompoArguJointOcc = oOccName.ToString
				Else 'kaikki muut
					uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_p" & k & "k2_joint_brace_" & NodeNum & "_" & laskuri & "_f" & l & "_k2"
					If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
						UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
						SaveFilu(oFullFileName, UusiFullFileName)
						PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)
						Components.Add(uusiName, UusiFullFileName, position := Pos)
						If oPointsDistance>500 Then
							Parameter(uusiName, "G_L") = oPointsDistance
							Parameter(uusiName, "SDA_size") = oSDA_size
						Else
							Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
						End If
						CompoArguJointOcc = uusiName.ToString
					Else 'käytetään vanhaa
						Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
						CompoArguJointOcc = uusiName.ToString
					End If
				End If

				Flippaus = False ' aina tässä niin kuten konffassa määrätty
				If laskuri Mod 2 = 0 And Flippaus Then 'parillinen ja kääntö 
					lahtoPiste = oSDA_to_wp
					loppuPiste = oSDA_from_wp
				ElseIf laskuri Mod 2 = 1 And Flippaus = False '(=pariton ei kääntöä siis myös)
					lahtoPiste = oSDA_to_wp
					loppuPiste = oSDA_from_wp
				Else
					lahtoPiste = oSDA_from_wp
					loppuPiste = oSDA_to_wp
				End If

				oConsName = "Brace_k2" & k & "_" & laskuri & "#" & "f" & StartFrameoNum & "/" & oRoofName & "/" & lahtoPiste & "#" & "f" & EndFrameoNum & "/" & oRoofName & "/" & loppuPiste & "_L" & LinjaLaskuri

				ConsNameList.Add(oConsName)

				Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
				laskuri += 1

			Next'frame
			LinjaLaskuri += 1
		Next'node alias linja k2
		']
	Next 'kattopcs

	'[harj
	If e_ridge_width>0 Then
		laskuri = 0
		If SDA_from.contains("_up_") Then
			oSDA_to = SDA_from.replace("_up_", "_")
		Else
			oSDA_to = Left(SDA_from, InStrRev(SDA_from, "_", -1)) & "up_" & Right(SDA_from, Len(SDA_from) -InStrRev(SDA_from, "_", -1))
		End If

		oPointDataArray_to = oSDA_to.Split(New Char() {"/"c })
		oSDA_to_wp = oPointDataArray_to(2)

		For l = 1 To CInt(SDA_pcs) -1
			StartFrameoNum = oFrame.Replace("f", "") + laskuri
			EndFrameoNum = StartFrameoNum + 1

			oRoofName = oModule.Replace("p1k", "h")

			Dim CompoArguStartPoint As ComponentArgument = {"f" & StartFrameoNum, oRoofName }
			Dim CompoArguEndPoint As ComponentArgument = {"f" & EndFrameoNum, oRoofName }

			Dim CompoArguJointOcc As ComponentArgument

			oSDA_from_wp = SDA_from.Split(New Char() {"/"c })(2)
			oSDA_to_wp = oSDA_to.Split(New Char() {"/"c })(2)

			oPointsDistance = Round(CalcPointsDistance("f" & StartFrameoNum & "/" & oRoofName & "/" & oSDA_from_wp, "f" & EndFrameoNum & "/" & oRoofName & "/" & oSDA_to_wp, PointtiHash))

			uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_h_k1_joint_brace_" & "h" & "_" & laskuri & "_f" & l & "_k1"

			If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
				UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
				SaveFilu(oFullFileName, UusiFullFileName)
				PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)
				Components.Add(uusiName, UusiFullFileName, position := Pos)
				If oPointsDistance>500 Then
					Parameter(uusiName, "G_L") = oPointsDistance
					Parameter(uusiName, "SDA_size") = oSDA_size
				Else
					Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
				End If
				CompoArguJointOcc = uusiName.ToString
			Else 'käytetään vanhaa
				Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
				CompoArguJointOcc = uusiName.ToString
			End If

			Flippaus = False ' aina tässä niin kuten konffassa määrätty
			If laskuri Mod 2 = 0 And Flippaus Then 'parillinen ja kääntö 
				lahtoPiste = oSDA_to_wp
				loppuPiste = oSDA_from_wp
			ElseIf laskuri Mod 2 = 1 And Flippaus = False '(=pariton ei kääntöä siis myös)
				lahtoPiste = oSDA_to_wp
				loppuPiste = oSDA_from_wp
			Else
				lahtoPiste = oSDA_from_wp
				loppuPiste = oSDA_to_wp
			End If

			oConsName = "Brace_h_" & laskuri & "#" & "f" & StartFrameoNum & "/" & oRoofName & "/" & lahtoPiste & "#" & "f" & EndFrameoNum & "/" & oRoofName & "/" & loppuPiste & "_L" & LinjaLaskuri

			ConsNameList.Add(oConsName)

			Try : Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start_2: Center Point", CompoArguStartPoint, lahtoPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
			Try : Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start_2: Y Axis", CompoArguEndPoint, loppuPiste & ": Center Point") : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
			Try : Constraints.AddAngle(oConsName & "_ang", CompoArguJointOcc, "ucs_start_2: X Axis", CompoArguStartPoint, lahtoPiste & ": X Axis", 0.00 deg) : Catch ex As Exception : Logger.Debug("Error:" & ex.Message) : End Try
			laskuri += 1

		Next'frame harjalla

		']
	End If
	']
End Function
Function PlaceTxtLineByFile_UCS(TxtPara As String, PointtiHash As Hashtable, parttiSrc As String, laskuri As Integer)
	oOccName = ReturnTXT(TxtPara, "oOccName") & "_" & laskuri
	oSDA_from = ReturnTXT(TxtPara, "SDA_from")
	Dim oData() As String = oSDA_from.Split("/"c)
	oFrame = oData(0)
	oModule = oData(1)
	oSDA_from_wp = oData(2)

	oSDA_to = ReturnTXT(TxtPara, "SDA_to")
	If oSDA_to Is Nothing Then : Exit Function : End If
	Dim oDataTo() As String = oSDA_to.Split("/"c)
	oFrameTo = oDataTo(0)
	oModuleTo = oDataTo(1)
	oSDA_to_wp = oDataTo(2)

	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")
	oSDA_size = ReturnTXT(TxtPara, "SDA_size")
	oSDA_sizeTxt = oSDA_size.Split(New Char() {"x"c })
	oPointsDistance = Round(CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash))

	uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_" & oFrame & "_" & oFrameTo

	If Not SharedVariable("DuplikaattiListaXT").ContainsKey(Round(oPointsDistance) & "_" & oSDA_size) Then 'uusi tiedosto
		UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
		SaveFilu(parttiSrc, UusiFullFileName)
		SharedVariable("DuplikaattiListaXT").Add(Round(oPointsDistance) & "_" & oSDA_size, UusiFullFileName)
		Components.Add(uusiName, UusiFullFileName, position := Pos)

		If oPointsDistance>500 Then
			Parameter(uusiName, "G_L") = oPointsDistance - oSDA_yoffset * 2
			Parameter(uusiName, "G_H") = oSDA_sizeTxt(0)
			Parameter(uusiName, "G_W") = oSDA_sizeTxt(1)
			Parameter(uusiName, "lock_hole_dist") = oSDA_sizeTxt(3)
		Else
			Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
		End If
	Else 'käytetään vanhaa
		Components.Add(uusiName, SharedVariable("DuplikaattiListaXT")(Round(oPointsDistance) & "_" & oSDA_size), position := Pos)
	End If

	oConsName = uusiName & "#" & oSDA_from & "#" & oSDA_to

	Dim CompoArguStartPoint As ComponentArgument = {oFrame, oModule }
	Dim CompoArguJointOcc As ComponentArgument = uusiName.ToString

	Try
		Constraints.AddUcsToUcs(oConsName, CompoArguStartPoint, oSDA_from_wp, CompoArguJointOcc, "insert_s", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara & " err:" & ex.Message)
	End Try
End Function

Function ParaValue(oParaName, oParaDict, Optional oIns = 0) 'täytyy lisätä
	Dim myArrayList As New ArrayList
	Dim duplicateList As New ArrayList
	Dim ParaKey() As String

	For Each oEntry As KeyValuePair(Of String, Object) In oParaDict
		ParaKey = oEntry.Key.Split(New Char() {"#"c })
		ParaKey0 = ParaKey(0)
		If ParaKey0.Contains(oParaName) Then
			'If Not myArrayList.Contains(oEntry.Value) Then
			myArrayList.Add(oEntry.Value)
			duplicateList.Add(ParaKey(1))
			'End If
		End If
		'Logger.Debug(oEntry.Value)
	Next

	oCount = myArrayList.Count

	If oCount = 1 Then
		arvo = myArrayList(0)
	Else
		Try
			arvo = myArrayList(oIns)
		Catch
			arvo = 0 '"N/A"
		End Try
	End If

	Return arvo
End Function
Function ReturnListausWindBraceSingle(TextLine As String)
	Dim oDict As New Dictionary(Of String, Object) : Dim WBlist As New ArrayList
	If TextLine Is Nothing Then : Logger.Debug("txt input not valid:" & TextLine) : Exit Function : End If
	laskuri = 0 : Dim TxtData() As String = TextLine.Split(";"c)
	'to wall

	WBlist.Add(TextLine)

	For Each oVal In TxtData 'seinän nurkasta alas seinään
		Try
			If laskuri>0 'occ eka
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				Try
					oArvo = ParaData(1).Split(New Char() {"/"c })
				Catch
					oArvo = oVal 'yksittäinen tietue
				End Try
				oDict.Add(ParaData(0), oArvo)
			End If
		Catch
			'oDict.Add(TxtData(0), TxtData.Count) 'aina occ nimi ilman on -merkkiä
		End Try
		laskuri += 1
	Next

	WallRivi = TxtData(0) & "_w;"
	For Each oEntry As KeyValuePair(Of String, Object) In oDict
		WallRivi += oEntry.Key & "="
		If oEntry.Value.length = 1 Then
			WallRivi += oEntry.Value(0) & ";"
		ElseIf oEntry.Value.length = 3 Then
			If oEntry.Value(2).contains("wind_brace_corner") Then 'seinällä käyetään alempaa nurkka juttua
				oWorkPonttiValue = "wind_brace_corner_lo"

			Else
				oWorkPonttiValue = oEntry.Value(2)
			End If

			If oEntry.Key = "SDA_to"
				WallRivi += oEntry.Value(0) & "/" & "s" & "/" & "wind_brace_s_1;" ' staattinen from samoja
			ElseIf oEntry.Key = "SDA_to_mir"
				WallRivi += oEntry.Value(0) & "/" & "s" & "/" & "wind_brace_s_1;" ' staattinen
			Else
				WallRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oWorkPonttiValue & ";"
			End If
		End If
	Next

	WBlist.Add(Left(WallRivi, Len(WallRivi) -1)) 'seinän juoksettaminen

	oPcs = CInt(oDict("br_zigzag_pcs")(0))
	wind_br_start = CInt(oDict("wind_br_start_purlin")(0))

	If oPcs>1 Then
		'[ orsilista
		Dim WindBraceCalList As New ArrayList
		WindBraceCalList.Add("s/wind_brace_corner")
		For i = 1 To e_roof_pcs
			ok1_purlin_pcs = Parameter("r" & i & "_k1_purlin_pcs")
			ok2_purlin_pcs = Parameter("r" & i & "_k2_purlin_pcs")
			For j = 1 To ok1_purlin_pcs
				WindBraceCalList.Add("p" & i & "k/wind_brace_k1_" & j)
			Next
			For k = 1 To ok2_purlin_pcs
				WindBraceCalList.Add("p" & i & "k/wind_brace_k2_" & k)
			Next
		Next

		WindBraceCalList.Add("p" & e_roof_pcs & "k/wind_brace_end")

		If e_ridge_width>0 Then 'miten purlin lisäys
			WindBraceCalList.Add("h/wind_brace_end")
		End If

		']
		For i = 2 To oPcs

			alkuNum = wind_br_start + (i - 2) * 2 'joka toiseen oletuksena, tarve vaihtaa?!
			VertartailuNodelkm = WindBraceCalList.Count - 1 'katon osuus
			Try
				AlkuNode = WindBraceCalList(alkuNum)
			Catch
				Logger.Debug("Error nodes count: " & VertartailuNodelkm & " vs br_zigzag_pcs: " & oPcs)
				Continue For
			End Try
			'tarkista miten toimii jos ei tasan!!

			ero_k = VertartailuNodelkm - alkuNum  'jäljellä
			'LoppuNum = alkuNum + 2

			If ero_k = 1 Then 'yksi jää väliin kun ei ole riitävästi nodea
				LoppuNum = alkuNum + 1
			ElseIf ero_k = 3 Then 'turha' väli hypätään yli kun viimeisen täytyy olla viimeinne
				LoppuNum = alkuNum + 3
			Else 'normi tilannne
				LoppuNum = alkuNum + 2
			End If

			Try
				LoppuNode = WindBraceCalList(LoppuNum)
			Catch
				LoppuNode = WindBraceCalList(alkuNum + 1) 'ei mene tasan kahden askelluksella
			End Try

			RoofRivi = TxtData(0) & "_" & i & ";"
			For Each oEntry As KeyValuePair(Of String, Object) In oDict
				RoofRivi += oEntry.Key & "="
				If oEntry.Value.length = 1 Then
					RoofRivi += oEntry.Value(0) & ";"
				ElseIf oEntry.Value.length = 3 Then
					oModuuli = oDict("SDA_to")(1) 'miten useampi katto vaihto 
					oSDA_to_node = oDict("SDA_to")(2)

					oNode = Left(oSDA_to_node, Len(oSDA_to_node) -1) 'miten k2 vaihto

					If oEntry.Key = "SDA_from"
						RoofRivi += oEntry.Value(0) & "/" & AlkuNode & ";"
					ElseIf oEntry.Key = "SDA_to"

						RoofRivi += oEntry.Value(0) & "/" & LoppuNode & ";"
					ElseIf oEntry.Key = "SDA_from_mir"

						RoofRivi += oEntry.Value(0) & "/" & AlkuNode & ";"
					ElseIf oEntry.Key = "SDA_to_mir"

						RoofRivi += oEntry.Value(0) & "/" & LoppuNode & ";"

					Else
						RoofRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
					End If
				End If
			Next
			WBlist.Add(Left(RoofRivi, Len(RoofRivi) -1))
		Next 'pcs vaihto
	End If
	'Logger.Debug("ero_k:" & ero_k)
	Return WBlist
End Function
Function AnalyzeGenList(oType As String, StartLine As String)
	oTeksti = ""
	'oTeksti += StartLine & vbLf
	If oType = "Purlin" Then
		oLista = ReturnListausPurlinsSingle(StartLine, totalFramePcs)
		'ListaPurlin = ReturnListausPurlinsSingle(ReturnTXT(TxtPara, "SDA_from"), totalFramePcs)
	ElseIf oType = "WindBrace" Then
		oLista = ReturnListausWindBraceSingle(StartLine)
	ElseIf oType = "WallBrace"
		oLista = ReturnListausWallBraceSingle(StartLine)
	ElseIf oType = "Ridge"
		oLista = ReturnListausSingle(StartLine, 1)
	ElseIf oType = "Eave"
		oLista = ReturnListausSingle(StartLine, 2)
	ElseIf oType = "Crossarm"
		oLista = ReturnListausCrossarmsSingle(ReturnTXT(StartLine, "SDA_from"), totalFramePcs)
	ElseIf oType = "Brace"
		'oLista = ReturnListausSingle(StartLine)
	End If

	If oLista Is Nothing Then
		Logger.Debug("list is empty :" & oType)
		Exit Function
	End If

	For Each oVal In oLista
		oTeksti += oVal & vbLf
	Next
	Try
		My.Computer.Clipboard.SetText(oTeksti)
		Return oTeksti
	Catch
		Logger.Debug("List is empty :" & oType)
	End Try
End Function
Function PlaceoneTxtLineuCS(TxtPara As String, PointtiHash As Hashtable, Optional oNewOccName As String = "")
	oOccName = ReturnTXT(TxtPara, "oOccName")
	oSDA_from = ReturnTXT(TxtPara, "SDA_from")
	Dim oData() As String = oSDA_from.Split("/"c)
	oFrame = oData(0)
	oModule = oData(1)
	oSDA_from_wp = oData(2)

	oSDA_to = ReturnTXT(TxtPara, "SDA_to")
	If oSDA_to Is Nothing Then : Exit Function : End If
	Dim oDataTo() As String = oSDA_to.Split("/"c)
	oFrameTo = oDataTo(0)
	oModuleTo = oDataTo(1)
	oSDA_to_wp = oDataTo(2)

	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")
	oSDA_size = ReturnTXT(TxtPara, "SDA_size")

	Try
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	Catch 'back up korjaus 
		Dim ArrayOcc() As String = oOccName.Split(New Char() {"_"c })
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(ArrayOcc(0))
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End Try

	oPointsDistance = Round(CalcPointsDistance(oSDA_from, oSDA_to, PointtiHash))

	If oSDA_from_wp.Contains("_eave_") Then
		raystasputki = True
	Else
		raystasputki = False
	End If

	If raystasputki Then
		oSDA_to_name_mid = "f" & CInt(oFrameTo.Replace("f", "")) -1 & "/" & oModuleTo & "/" & oSDA_to_wp
		oPointsDistance_mid = CalcPointsDistance(oSDA_from, oSDA_to_name_mid, PointtiHash)
	End If

	If oNewOccName = "" Then 'ekassa aina tarkistetaan koffan occ pituus
		If oPointsDistance>500 Then 'pieni muotoinen virheen korjaus jos ei saa pisteen pituutta
			Parameter(oOccName, "G_L") = oPointsDistance - oSDA_yoffset * 2
			Parameter(oOccName, "SDA_size") = oSDA_size
			If raystasputki Then
				Parameter(oOccName, "G_L1") = oPointsDistance_mid - oSDA_yoffset
			End If
		Else
			Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
		End If
		SharedVariable("DuplikaattiLista").Add(Round(oPointsDistance) & "_" & oSDA_size & "_" & oPointsDistance_mid, oFullFileName) 'millin tarkkkuus
	Else 'kaikki muut obs läheteävä oNewOccName muille

		If oSDA_from_wp.Contains("CORNER_NEXT") Then
			oModule = oModule.Replace("/p1", "/p" & e_roof_pcs)
			ModuulinNimi = "_ridge_tube"
		Else
			ModuulinNimi = "_eave_tube"
		End If

		uusiName = oOccName & iProperties.Value("Project", "Project") & "_" & oModule & "_" & ModuulinNimi & "_" & oNewOccName & "_" & oFrame

		If Not SharedVariable("DuplikaattiLista").ContainsKey(Round(oPointsDistance) & "_" & oSDA_size & "_" & oPointsDistance_mid) Then 'uusi tiedosto
			UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
			SaveFilu(oFullFileName, UusiFullFileName)
			SharedVariable("DuplikaattiLista").Add(Round(oPointsDistance) & "_" & oSDA_size & "_" & oPointsDistance_mid, UusiFullFileName)
			Components.Add(uusiName, UusiFullFileName, position := Pos)

			If oPointsDistance>500 Then
				Parameter(uusiName, "G_L") = oPointsDistance - oSDA_yoffset * 2
				Parameter(uusiName, "SDA_size") = oSDA_size

				If raystasputki Then
					Parameter(uusiName, "G_L1") = oPointsDistance_mid - oSDA_yoffset
				Else 'harja
					Parameter(uusiName, "ridge_frame_code") = ridge_frame_code
					Parameter(uusiName, "ridge_tube_code") = ridge_tube_code
				End If
			Else
				Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
			End If
		Else 'käytetään vanhaa
			Components.Add(uusiName, SharedVariable("DuplikaattiLista")(Round(oPointsDistance) & "_" & oSDA_size & "_" & oPointsDistance_mid), position := Pos)
		End If
		oOccName = uusiName
	End If

	oConsName = oOccName & "#" & oSDA_from & "#" & oSDA_to

	Dim CompoArguStartPoint As ComponentArgument = {oFrame, oModule }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString

	Try
		Constraints.AddUcsToUcs(oConsName, CompoArguStartPoint, oSDA_from_wp, CompoArguJointOcc, "UCS", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara & " err:" & ex.Message)
	End Try
End Function
Function PlaceoneTxtLine(TxtPara As String, PointtiHash As Hashtable, Optional oNewOccName As String = "")
	Dim PituusTaulu As Hashtable = New Hashtable 'vain wall brace käyttää tätä
	oOccName = ReturnTXT(TxtPara, "oOccName")
	oSDA_from = ReturnTXT(TxtPara, "SDA_from") 'oPointDataArraysis
	Dim oData() As String = oSDA_from.Split("/"c)
	oSDA_to = ReturnTXT(TxtPara, "SDA_to")
	If oSDA_to Is Nothing Then : Exit Function : End If
	Dim oDataTo() As String = oSDA_to.Split("/"c)

	oFrame = oData(0)
	oFrameTo = oDataTo(0)

	oVrt1 = CDbl(oFrame.Replace("f", ""))
	oVrt2 = CDbl(oFrameTo.Replace("f", ""))
	If oVrt2 > oVrt1 Then
		LisäysFrom = ""
		LisäysTo = "-"
	Else
		LisäysFrom = "-"
		LisäysTo = ""
	End If

	oModule = oData(1)
	oSDA_from_wp = oData(2) & LisäysFrom

	oModuleTo = oDataTo(1)
	oSDA_to_wp = oDataTo(2) & LisäysTo

	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_length = ReturnTXT(TxtPara, "SDA_length")

	'[initial position calculation
	Dim oTG As TransientGeometry = ThisApplication.TransientGeometry
	oStartPoint = oTG.CreatePoint(0, 0, 0)
	Dim oMatrix As Matrix = oTG.CreateMatrix
	Dim oXAxis As Vector
	Dim oYAxis As Vector
	Dim oZAxis As Vector
	oXAxis = oTG.CreateVector(0, 0, 1)
	oYAxis = oTG.CreateVector(1, 0, 0)
	oZAxis = oTG.CreateVector(0, 1, 0)
	oMatrix.SetCoordinateSystem(oStartPoint, oXAxis, oYAxis, oZAxis)
	Dim cells(15) As Double
	oMatrix.GetMatrixData(cells)
	Dim Pos = ThisAssembly.Geometry.Matrix(cells(0), cells(1), cells(2), oStartPoint.X * 10,
	cells(4), cells(5), cells(6), oStartPoint.Y * 10,
	cells(8), cells(9), cells(10), oStartPoint.Z * 10,
	0, 0, 0, 1)

	Dim oMatrixNeg As Matrix = oTG.CreateMatrix
	Dim oXAxisNeg As Vector
	Dim oYAxisNeg As Vector
	Dim oZAxisNeg As Vector
	oXAxisNeg = oTG.CreateVector(0, -0.5, 0.9)
	oYAxisNeg = oTG.CreateVector(0.7, -0.6, 0.3)
	oZAxisNeg = oTG.CreateVector(-0.7, -0.6, 0.3)

	oMatrixNeg.SetCoordinateSystem(oStartPoint, oXAxisNeg, oYAxisNeg, oZAxisNeg)
	Dim cellsNeg(15) As Double
	oMatrixNeg.GetMatrixData(cells)
	Dim PosNeg = ThisAssembly.Geometry.Matrix(cellsNeg(0), cellsNeg(1), cellsNeg(2), oStartPoint.X * 10,
	cellsNeg(4), cellsNeg(5), cellsNeg(6), oStartPoint.Y * 10,
	cellsNeg(8), cellsNeg(9), cellsNeg(10), oStartPoint.Z * 10,
	0, 0, 0, 1)
	']

	Try
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	Catch 'jos jotain e5_w -tyyppinen käyttää tätä - ei pitäi tulla uudella koodilla
		Dim ArrayOcc() As String = oOccName.Split(New Char() {"_"c })
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(ArrayOcc(0))
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
	End Try

	oPointsDistance = Round(CalcPointsDistance(oSDA_from & LisäysFrom, oSDA_to & LisäysTo, PointtiHash))

	If oNewOccName = "" Then 'ekassa aina tarkistetaan koffan occ pituus
		If oPointsDistance>500 Then 'pieni muotoinen virheen korjaus jos ei saa pisteen pituutta
			Parameter(oOccName, "G_L") = oPointsDistance
		Else
			Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
		End If
		PituusTaulu.Add(Round(oPointsDistance), oFullFileName) 'millin tarkkkuus
	Else 'kaikki muut obs lähetteävä oNewOccName muille
		uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_wab_" & oModule & "_" & oSDA_from_wp & "_" & oNewOccName & "_" & oFrameTo

		If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
			UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
			SaveFilu(oFullFileName, UusiFullFileName)
			PituusTaulu.Add(Round(oPointsDistance), UusiFullFileName)
			'			If oDeltaZ<-1000 Then
			'				Components.Add(uusiName, UusiFullFileName, position := PosNeg)
			'			Else
			Components.Add(uusiName, UusiFullFileName, position := Pos)
			'			End If
			If oPointsDistance>500 Then
				Parameter(uusiName, "G_L") = oPointsDistance
			Else
				Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
			End If
			'CompoArguJointOcc = uusiName.ToString

		Else 'käytetään vanhaa
			'			If oDeltaZ<-1000 Then
			'				Components.Add(uusiName, PituusTaulu(oPointsDistance), position := PosNeg)
			'			Else
			Components.Add(uusiName, PituusTaulu(oPointsDistance), position := Pos)
			'			End If
		End If
		oOccName = uusiName
	End If

	oConsName = oOccName & "#" & oSDA_from & LisäysFrom & "#" & oSDA_to & LisäysTo

	Dim CompoArguStartPoint As ComponentArgument = {oFrame, oModule }
	Dim CompoArguEndPoint As ComponentArgument = {oFrameTo, oModuleTo }
	Dim CompoArguJointOcc As ComponentArgument = oOccName.ToString

	Try
		Constraints.AddMate(oConsName & "_point", CompoArguJointOcc, "ucs_start: Center Point", CompoArguStartPoint, oSDA_from_wp & ": Center Point")
		Constraints.AddMate(oConsName & "_dir", CompoArguJointOcc, "ucs_start: Y Axis", CompoArguEndPoint, oSDA_to_wp & ": Center Point")
		'mistäköhän hyvä kulma
		'Constraints.AddAngle(oConsName & "ang", CompoArguJointOcc, "ucs_start: X Axis", CompoArguStartPoint, oSDA_from_wp & ": X Axis", 0.00 deg)
	Catch ex As Exception
		Logger.Debug("Error in :" & TxtPara & vbLf & ex.Message)
	End Try
End Function
Function ReturnListausSingle(TextLine As String, askellus As Integer)
	If TextLine Is Nothing Then : Logger.Debug("txt input not valid:" & TextLine) : Exit Function : End If
	Dim oDict As New Dictionary(Of String, Object) : Dim RtubeList As New ArrayList
	laskuri = 0 : Dim TxtData() As String = TextLine.Split(";"c)

	If cui_jp_range_all = True And cui_jp_range_start<2 Or cui_jp_range_all = False Then 'eka rivi lisätään jos lasketaan kaikki tai aloitus numero 1 - vähän väärinpäin käyttöliittymän takia
	Else
		RtubeList.Add(TextLine)
	End If

	For Each oVal In TxtData
		Try
			If laskuri>0 'occ eka
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				Try
					oArvo = ParaData(1).Split(New Char() {"/"c })
				Catch
					oArvo = oVal 'yksittäinen tietue
				End Try
				oDict.Add(ParaData(0), oArvo)
			End If
		Catch
		End Try
		laskuri += 1
	Next


	If cui_jp_range_all Then 'valinta väliltä!
		oPcs = cui_jp_range_end
		alku = cui_jp_range_start + (cui_jp_range_start - 1) Mod askellus 'hyppää seuraavan jos askellus valittu 'väärin'
	Else
		oPcs = CInt(oDict("totalpcs")(0))
		alku = 1
	End If

	For i = alku To oPcs - 1 Step askellus 'totalframe
		RoofRivi = TxtData(0) & ";" 'täällä ei muuteta occ -koska siitä lasketaan fullname

		For Each oEntry As KeyValuePair(Of String, Object) In oDict
			RoofRivi += oEntry.Key & "="
			If oEntry.Value.length = 1 Then
				RoofRivi += oEntry.Value(0) & ";"
			ElseIf oEntry.Value.length = 3 Then
				Frame_to = oDict("SDA_to")(0)
				Frame_from = oDict("SDA_from")(0)
				oModuuli = oDict("SDA_to")(1)
				oNode = oDict("SDA_to")(2)
				oSDA_to_node = oDict("SDA_to")(2)
				oFromF = "f" & i

				If i>oPcs - askellus Then
					oToF = "f" & oPcs
				Else
					oToF = "f" & i + askellus
				End If

				If oEntry.Key = "SDA_from"
					RoofRivi += oFromF & "/" & oModuuli & "/" & oNode & ";"
				ElseIf oEntry.Key = "SDA_to"
					RoofRivi += oToF & "/" & oModuuli & "/" & oNode & ";"
				Else
					RoofRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
				End If

			End If
		Next

		RtubeList.Add(Left(RoofRivi, Len(RoofRivi) -1))
	Next
	Return RtubeList
End Function
Function ReturnListausWallBraceSingle(TextLine As String)
	If TextLine Is Nothing Then : Logger.Debug("txt input not valid:" & TextLine) : Exit Function : End If
	
	Dim oDict As New Dictionary(Of String, Object) : Dim WallBlist As New ArrayList
	laskuri = 0 : Dim TxtData() As String = TextLine.Split(";"c)

	For Each oVal In TxtData
		Try
			If laskuri>0 'occ eka
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				Try
					oArvo = ParaData(1).Split(New Char() {"/"c })
				Catch
					oArvo = oVal 'yksittäinen tietue
				End Try
				oDict.Add(ParaData(0), oArvo)
			End If
		Catch
			'oDict.Add(TxtData(0), TxtData.Count) 'aina occ nimi ilman on -merkkiä
		End Try
		laskuri += 1
	Next

	Pos_ka = (CInt(oDict("SDA_from")(0).replace("f", "")) + CInt(oDict("SDA_to")(0).replace("f", ""))) / 2

	If Pos_ka > cui_jp_range_start And Pos_ka < cui_jp_range_end Then
		WallBlist.Add(TextLine)
	End If

	oPcs = CInt(oDict("br_zigzag_pcs")(0))

	If oPcs>1 And Pos_ka > cui_jp_range_start And Pos_ka < cui_jp_range_end Then
		For i = 2 To oPcs
			RoofRivi = TxtData(0) & ";" 'täällä ei muuteta occ -koska siitä lasketaan fullname

			For Each oEntry As KeyValuePair(Of String, Object) In oDict
				RoofRivi += oEntry.Key & "="

				If oEntry.Value.length = 1 Then
					RoofRivi += oEntry.Value(0) & ";"
				ElseIf oEntry.Value.length = 3 Then
					Frame_to = oDict("SDA_to")(0)
					Frame_from = oDict("SDA_from")(0)
					oModuuli = oDict("SDA_to")(1) 'miten useampi katto vaihto 
					oSDA_to_node = oDict("SDA_to")(2)
					oNode = Left(oSDA_to_node, Len(oSDA_to_node) -1) 'miten k2 vaihto

					If i Mod 2 = 0 Then 'parillinen -> flippaus
						oFromF = Frame_to
						oToF = Frame_from
					Else
						oToF = Frame_to
						oFromF = Frame_from
					End If

					If oEntry.Key = "SDA_from"

						RoofRivi += oFromF & "/" & oModuuli & "/" & oNode & i & ";"
					ElseIf oEntry.Key = "SDA_to"
						RoofRivi += oToF & "/" & oModuuli & "/" & oNode & i + 1 & ";"
					Else
						RoofRivi += oEntry.Value(0) & "/" & oEntry.Value(1) & "/" & oEntry.Value(2) & ";"
					End If

				End If
			Next
			WallBlist.Add(Left(RoofRivi, Len(RoofRivi) -1))
		Next
	Else
		Frame_to = oDict("SDA_to")(0)
		Frame_from = oDict("SDA_from")(0)
	End If

	If TextLine.Contains("SDA_end2generate=1") And Pos_ka > cui_jp_range_start And Pos_ka < cui_jp_range_end Then 'obs generoidaan jos alkupää mukana!
		Dim WallBlistEnd2 As New ArrayList
		ototalpcs = oDict("totalpcs")(0)
		Frame_End2_From = "f" & ototalpcs + 1 - CInt(Frame_from.Replace("f", ""))
		Frame_End2_To = "f" & ototalpcs + 1 - CInt(Frame_to.Replace("f", ""))
		k = 1
		For Each oLine In WallBlist
			oSDA_from = ReturnTXT(oLine, "SDA_from") 'oPointDataArraysis
			Dim oData() As String = oSDA_from.Split("/"c)
			oFrame = oData(0)
			Logger.Debug("oFrame:" & oFrame & " vs " & Frame_End2_From)
			oSDA_to = ReturnTXT(oLine, "SDA_to")
			Dim oDataTo() As String = oSDA_to.Split("/"c)
			oFrameTo = oDataTo(0)
			Logger.Debug("oFrameTo:" & oFrameTo & " vs " & Frame_End2_To)

			If k Mod 2 = 0 Then 'parillinen -> flippaus
				oFromF = Frame_End2_To
				oToF = Frame_End2_From
			Else
				oToF = Frame_End2_To
				oFromF = Frame_End2_From
			End If
			UusiRivi = oLine.replace(oFrame & "/", oFromF & "/").replace(oFrameTo & "/", oToF & "/")
			WallBlistEnd2.Add(UusiRivi)
			k += 1
		Next

		WallBlist.AddRange(WallBlistEnd2)
	End If
	Return WallBlist
End Function

Function ReturnTXT(TxtPara As String, Optional returnValue As String = "")
	If TxtPara Is Nothing Then : Logger.Debug("Error txt input not valid ReturnTXT :") : Exit Function : End If
	Dim TxtData() As String = TxtPara.Split(";"c)
	Dim oParameterName As String: Dim oPointDataArray As String()
	For Each oDatum In TxtData
		Dim oDatumSis() As String = oDatum.Split("="c)
		If oDatumSis.Length = 1 Then
			oOccName = oDatum
		ElseIf oDatumSis.Length = 2
			oParameterName = oDatumSis(0)
			oPointDataArraysis = oDatumSis(1)
			If oParameterName = "SDA_from" Then
				oSDA_from = oDatumSis(1)
			ElseIf oParameterName = "SDA_to" Then
				oSDA_to = oDatumSis(1)
			ElseIf oParameterName = "SDA_xoffset" Then
				oSDA_xoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_yoffset" Then
				oSDA_yoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_zoffset" Then
				oSDA_zoffset = oDatumSis(1)
			ElseIf oParameterName = "SDA_length" Then
				oSDA_length = oDatumSis(1)
			ElseIf oParameterName = "totalpcs" Then
				ototalpcs = oDatumSis(1)
			ElseIf oParameterName = "SDA_from_mir" Then
				oSDA_from_mir = oDatumSis(1)
			ElseIf oParameterName = "SDA_to_mir" Then
				oSDA_to_mir = oDatumSis(1)
			ElseIf oParameterName = "SDA_end2generate" Then
				oSDA_end2generate = oDatumSis(1)
			ElseIf oParameterName = "e_roof_pcs" Then
				oe_roof_pcs = oDatumSis(1)
			ElseIf oParameterName = "SDA_size" Then
				oSDA_size = oDatumSis(1)
			End If
		End If
	Next

	If oSDA_xoffset = "" Then : oSDA_xoffset = 0 : End If
	If oSDA_yoffset = "" Then : oSDA_yoffset = 0 : End If
	If oSDA_zoffset = "" Then : oSDA_zoffset = 0 : End If
	If ototalpcs = "" Then : ototalpcs = 0 : End If
	If oSDA_end2generate = "" Then : oSDA_end2generate = 0 : End If

	If returnValue = "SDA_from" Then
		Return oSDA_from
	ElseIf returnValue = "oOccName" Then
		Return oOccName
	ElseIf returnValue = "SDA_xoffset" Then
		Return oSDA_xoffset
	ElseIf returnValue = "SDA_yoffset" Then
		Return oSDA_yoffset
	ElseIf returnValue = "SDA_zoffset" Then
		Return oSDA_zoffset
	ElseIf returnValue = "totalpcs" Then
		Return ototalpcs
	ElseIf returnValue = "SDA_to" Then
		Return oSDA_to
	ElseIf returnValue = "SDA_from_mir" Then
		Return oSDA_from_mir
	ElseIf returnValue = "SDA_to_mir" Then
		Return oSDA_to_mir
	ElseIf returnValue = "SDA_end2generate" Then
		Return oSDA_end2generate
	ElseIf returnValue = "e_roof_pcs" Then
		Return oe_roof_pcs
	ElseIf returnValue = "SDA_size" Then
		Return oSDA_size
	End If
End Function

Function PlaceAutoCrossarm(TxtPara As String, PointtiHash As Hashtable, Optional FrameNume As Integer = 1, Optional RoofNume As Integer = 1, Optional NodeNum As Integer = 1, Optional oNewOccName As String = "", Optional oFileName As String = "")
	laskuri = 1
	Dim PituusTaulu As Hashtable = New Hashtable
	totalFramePcs = ReturnTXT(TxtPara, "totalpcs")
	CrossarmDict = makeDictionaryPara(CrossarmsA_txt)
	CrossarmSizeRoof = CrossarmDict("SDA_size")
	CrossarmSizeWall = Parameter("s", "e_buckling_crossarm_size_w")

	ListaCrossarm = ReturnListausCrossarmsSingle(ReturnTXT(TxtPara, "SDA_from"), totalFramePcs)

	oOccName = ReturnTXT(TxtPara, "oOccName")
	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")


	For Each oVal In ListaCrossarm
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
		Dim oData() As String = oVal.Split("/"c)
		oFrame = oData(0)
		oModule = oData(1)
		oNode = oData(2)

		If oNode.Contains("_s_") Then
			If CrossarmSizeWall < 50 Then
				oSteppiKeha = 2
			Else
				oSteppiKeha = 1
			End If
			SDA_size_CA = CrossarmSizeWall
		Else
			If CrossarmSizeRoof < 50 Then
				oSteppiKeha = 2
			Else
				oSteppiKeha = 1
			End If
			SDA_size_CA = CrossarmSizeRoof
		End If

		oFramNumTo = oFrame.Replace("f", "")
		loppuRaami = CDbl(oFramNumTo) + oSteppiKeha

		If loppuRaami>totalFramePcs Then
			loppuRaami = totalFramePcs
		End If

		oSDA_to_name = "f" & loppuRaami & "/" & oModule & "/" & oNode
		oSDA_to_name_mid = "f" & loppuRaami - 1 & "/" & oModule & "/" & oNode
		oPointsDistance = CalcPointsDistance(oVal, oSDA_to_name, PointtiHash)
		oPointsDistance_mid = CalcPointsDistance(oVal, oSDA_to_name_mid, PointtiHash)

		If laskuri = 1 Then 'ekassa aina tarkistetaan koffan occ pituus
			If oPointsDistance>500 Then
				Parameter(oOccName, "G_L") = oPointsDistance
				Parameter(oOccName, "G_L1") = oPointsDistance_mid
				Parameter(oOccName, "SDA_size") = SDA_size_CA 'tarkista olisiko paremmin
				Try
					PituusTaulu.Add(oPointsDistance & "_" & oPointsDistance_mid & "_" & SDA_size_CA, oFullFileName)
				Catch
					Logger.Debug("Data is already in table :" & oOccName & "_" & oPointsDistance)
				End Try
			Else
				Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
			End If
		Else 'kaikki mut
			uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_" & oModule & "_" & oNode & "_" & oFrame

			If Not PituusTaulu.ContainsKey(oPointsDistance & "_" & oPointsDistance_mid & "_" & SDA_size_CA) Then 'uusi tiedosto
				UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
				SaveFilu(oFullFileName, UusiFullFileName)
				PituusTaulu.Add(oPointsDistance & "_" & oPointsDistance_mid & "_" & SDA_size_CA, UusiFullFileName)
				Components.Add(uusiName, UusiFullFileName)
				If oPointsDistance>500 Then
					Parameter(uusiName, "G_L") = oPointsDistance
					Parameter(uusiName, "G_L1") = oPointsDistance_mid
					Parameter(uusiName, "SDA_size") = SDA_size_CA
				Else
					Logger.Debug("Error :" & uusiName & " cal len:" & oPointsDistance)
				End If
			Else 'käytetään vanhaa
				Components.Add(uusiName, PituusTaulu(oPointsDistance & "_" & oPointsDistance_mid & "_" & SDA_size_CA))
			End If

		End If


		oConsName = "Crossarm_" & laskuri & "#" & oVal
		Dim CompoArguJointOcc As ComponentArgument

		If laskuri = 1 Then
			CompoArguJointOcc = oOccName.ToString
		Else
			CompoArguJointOcc = uusiName.ToString
		End If

		Dim CompoArguStartPoint As ComponentArgument
		CompoArguStartPoint = {oFrame, oModule }

		If oModule = "s" Then
			Zpos = 0
		Else
			Zpos = oSDA_zoffset
		End If

		Try
			Constraints.AddUcsToUcs(oConsName, CompoArguStartPoint, oNode, CompoArguJointOcc, "ucs_start", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := Zpos)
		Catch ex As Exception
			Logger.Debug("Error in :" & TxtPara & " err:" & ex.Message)
		End Try
		laskuri += 1
	Next
End Function
Function ReturnListausCrossarmsSingle(SDA_from As String, totalFramePcs As Integer, Optional returnValue As String = "")
	If SDA_from Is Nothing Then
		Logger.Debug("Error SDA_from text is nothing!")
		Exit Function
	End If

	Dim k1_NodeTauluByRoof As Hashtable = New Hashtable
	Dim k2_NodeTauluByRoof As Hashtable = New Hashtable
	Dim SDA_from_list As New ArrayList
	Dim SDA_to_list As New ArrayList

	If CrossarmSizeRoof<50 Then
		oStep_k = 2
	Else
		oStep_k = 1
	End If

	If e_roof_pcs>0 Then 'loopppaus 
		k1_NodeTauluByRoof.Add(1, r1_k1_brace_pcs) 'itse arvo lasketaan ParametrinHaku AllParamsDict taulun avulla
		k2_NodeTauluByRoof.Add(1, r1_k2_brace_pcs) ' obs jos suorakatto niin arvo 1!
	End If
	If e_roof_pcs>1 Then
		k1_NodeTauluByRoof.Add(2, r2_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(2, r2_k2_brace_pcs)
	End If

	If e_roof_pcs>2 Then
		k1_NodeTauluByRoof.Add(3, r3_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(3, r3_k2_brace_pcs)
	End If
	If e_roof_pcs>3 Then
		k1_NodeTauluByRoof.Add(4, r4_k1_brace_pcs)
		k2_NodeTauluByRoof.Add(4, r4_k2_brace_pcs)
	End If 'obs lisää vielä 

	For RoofNume_i As Integer = 1 To e_roof_pcs 'kattoelementteihin

		For NodeNum_i = 1 To k1_NodeTauluByRoof(RoofNume_i)'ensin k1
			For Frame_i = 1 To Round(totalFramePcs - 1) Step oStep_k
				oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
				oSDA_from = oSDA_from.Replace("/p1k", "/p" & RoofNume_i & "k")
				oSDA_from = oSDA_from.Replace("_1", "_" & NodeNum_i)
				'f1/p1k/joint_crossarm_k2_3
				SDA_from_list.Add(oSDA_from)
				loppuRaami = Frame_i + oStep_k
				If loppuRaami>totalFramePcs Then
					loppuRaami = totalFramePcs
				End If
				oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
				SDA_to_list.Add(oSDA_to)
			Next
		Next

		k2_NodeTot = k2_NodeTauluByRoof(RoofNume_i) 'tarkista suora katto

		For Node_k2Num_i = 1 To k2_NodeTot 'k2 erikseen
			For Frame_i = 1 To Round(totalFramePcs - 1) Step oStep_k
				oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
				oSDA_from = oSDA_from.Replace("/p1k", "/p" & RoofNume_i & "k")
				oSDA_from = oSDA_from.Replace("_k1_", "_k" & 2 & "_")
				oSDA_from = oSDA_from.Replace("_1", "_" & Node_k2Num_i)

				SDA_from_list.Add(oSDA_from)
				loppuRaami = Frame_i + oStep_k
				If loppuRaami>totalFramePcs Then
					loppuRaami = totalFramePcs
				End If
				oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
				SDA_to_list.Add(oSDA_to)
			Next
		Next
	Next 'katto pcs


	If CrossarmSizeWall<50 Then 'seinä
		oStep_w = 2
	Else
		oStep_w = 1
	End If
	crossarm_pcs_wall = Parameter("s", "crossarm_pcs")

	For NodeNum_i = 1 To crossarm_pcs_wall
		For Frame_i = 1 To Round(totalFramePcs - 1) Step oStep_w
			oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
			oSDA_from = oSDA_from.Replace("/p1k", "/s")
			oSDA_from = oSDA_from.Replace("_1", "_" & NodeNum_i)
			oSDA_from = oSDA_from.Replace("joint_crossarm_k1_", "joint_crossarm_s_")
			SDA_from_list.Add(oSDA_from)
			loppuRaami = Frame_i + 2
			If loppuRaami>totalFramePcs Then
				loppuRaami = totalFramePcs
			End If
			oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
			SDA_to_list.Add(oSDA_to)
		Next
	Next 'seinä

	If e_ridge_width>0 Then 'harja
		For Frame_h = 1 To Round(totalFramePcs - 1) Step oStep_k 'harja
			oSDA_from = SDA_from.Replace("f1/", "f" & Frame_h & "/")
			oSDA_from = oSDA_from.Replace("/p1k", "/h")
			oSDA_from = oSDA_from.Replace("_1", "_" & 1)
			SDA_from_list.Add(oSDA_from)
			loppuRaami = Frame_h + oStep_k
			If loppuRaami>totalFramePcs Then
				loppuRaami = totalFramePcs
			End If
			oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
			SDA_to_list.Add(oSDA_to)
		Next
	End If

	If returnValue = "SDA_to" Then
		Return SDA_to_list
	Else
		Return SDA_from_list
	End If

	Return SDA_from_list 'laskenta myös to? 
End Function
Function PlaceAutoPurlin(TxtPara As String, PointtiHash As Hashtable, Optional FrameNume As Integer = 1, Optional RoofNume As Integer = 1, Optional NodeNum As Integer = 1, Optional oNewOccName As String = "", Optional oFileName As String = "")
	totalFramePcs = ReturnTXT(TxtPara, "totalpcs")
	e_roof_pcs = ReturnTXT(TxtPara, "e_roof_pcs")
	laskuri = 1
	Dim PituusTaulu As Hashtable = New Hashtable
	ListaPurlin = ReturnListausPurlinsSingle(ReturnTXT(TxtPara, "SDA_from"), totalFramePcs)

	oOccName = ReturnTXT(TxtPara, "oOccName")
	LyhennysPurlin = 10 'staattinen 
	oSDA_xoffset = ReturnTXT(TxtPara, "SDA_xoffset")
	oSDA_yoffset = ReturnTXT(TxtPara, "SDA_yoffset")
	oSDA_zoffset = ReturnTXT(TxtPara, "SDA_zoffset")
	oSDA_size = ReturnTXT(TxtPara, "SDA_size")


	For Each oVal In ListaPurlin
		oCompOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.ItemByName(oOccName)
		oFullFileName = oCompOcc.ReferencedFileDescriptor.FullFileName
		Dim oData() As String = oVal.Split("/"c)
		oFrame = oData(0)
		oModule = oData(1)
		oNode = oData(2)
		oFramNumTo = oFrame.Replace("f", "")
		loppuRaami = CDbl(oFramNumTo) + 2
		If loppuRaami>totalFramePcs Then
			loppuRaami = totalFramePcs
		End If
		oSDA_to_name = "f" & loppuRaami & "/" & oModule & "/" & oNode
		oPointsDistance = CalcPointsDistance(oVal, oSDA_to_name, PointtiHash)

		If laskuri = 1 Then 'ekassa aina tarkistetaan koffan occ pituus
			If oPointsDistance>500 Then
				Parameter(oOccName, "G_L") = oPointsDistance - LyhennysPurlin
				Parameter(oOccName, "SDA_size") = oSDA_size
				PituusTaulu.Add(oPointsDistance, oFullFileName)
			Else
				Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
			End If
		Else 'kaikki muut
			uusiName = oOccName & "_" & iProperties.Value("Project", "Project") & "_" & oModule & "_" & oNode & "_" & oFrame

			If Not PituusTaulu.ContainsKey(oPointsDistance) Then 'uusi tiedosto
				UusiFullFileName = ThisDoc.Path & "\" & uusiName & ".ipt"
				SaveFilu(oFullFileName, UusiFullFileName)
				PituusTaulu.Add(oPointsDistance, UusiFullFileName)
				Components.Add(uusiName, UusiFullFileName)

				If oPointsDistance>500 Then
					Parameter(uusiName, "G_L") = oPointsDistance - LyhennysPurlin
					Parameter(uusiName, "SDA_size") = oSDA_size
				Else
					Logger.Debug("Error :" & oOccName & " cal len:" & oPointsDistance)
				End If
			Else 'käytetään vanhaa
				Components.Add(uusiName, PituusTaulu(oPointsDistance))
			End If

		End If

		oConsName = "Purlin_" & laskuri & "#" & oVal
		Dim CompoArgu1 As ComponentArgument = {oFrame, oModule }
		Dim CompoArgu2 As ComponentArgument

		If laskuri = 1 Then
			CompoArgu2 = oOccName.ToString
		Else
			CompoArgu2 = uusiName.ToString
		End If
		Try
			Constraints.AddUcsToUcs(oConsName, CompoArgu1, oNode, CompoArgu2, "UCS", xOffset := oSDA_xoffset, yOffset := oSDA_yoffset, zOffset := oSDA_zoffset)
		Catch ex As Exception
			Logger.Debug("Error in :" & TxtPara & " err:" & ex.Message)
		End Try
		laskuri += 1
	Next
End Function
Function SaveFilu(SrcFilu As String, NewFilu As String)
	Try
		oDoc = ThisApplication.Documents.Open(SrcFilu, False)
	Catch
		Logger.Debug("Error not found base part :" & SrcFilu)
	End Try
	If oDoc Is Nothing Then
		Exit Function
	End If
	If IO.File.Exists(NewFilu) Then
		Logger.Debug("File already exists : " & NewFilu)
	Else

		Try
			oDoc.SaveAs(NewFilu, True)
			Logger.Debug("New part File being created : " & NewFilu)
		Catch ex As Exception
			Logger.Debug(ex.Message)
		End Try 'cache voi aiheuttaa virheen?
	End If
End Function
Function ReturnListausPurlinsSingle(SDA_from As String, ototalpcs As Integer, Optional returnValue As String = "")
	Dim k1_NodeTauluByRoof As Hashtable = New Hashtable
	Dim k2_NodeTauluByRoof As Hashtable = New Hashtable
	Dim SDA_from_list As New ArrayList
	Dim SDA_to_list As New ArrayList

	k1_NodeTauluByRoof.Add(1, r1_k1_purlin_pcs)
	k2_NodeTauluByRoof.Add(1, r1_k2_purlin_pcs) ' obs jos suorakatto niin arvo 1!

	If e_roof_pcs>1 Then
		k1_NodeTauluByRoof.Add(2, r2_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(2, r2_k2_purlin_pcs)
	End If

	If e_roof_pcs>2 Then
		k1_NodeTauluByRoof.Add(3, r3_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(3, r3_k2_purlin_pcs)
	End If

	If e_roof_pcs>3 Then
		k1_NodeTauluByRoof.Add(4, r4_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(4, r4_k2_purlin_pcs)
	End If
	If e_roof_pcs>4 Then
		k1_NodeTauluByRoof.Add(5, r5_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(5, r5_k2_purlin_pcs)
	End If

	If e_roof_pcs>5 Then
		k1_NodeTauluByRoof.Add(6, r6_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(6, r6_k2_purlin_pcs)
	End If
	If e_roof_pcs>6 Then
		k1_NodeTauluByRoof.Add(7, r7_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(7, r7_k2_purlin_pcs)
	End If

	If e_roof_pcs>7 Then
		k1_NodeTauluByRoof.Add(8, r8_k1_purlin_pcs)
		k2_NodeTauluByRoof.Add(8, r8_k2_purlin_pcs)
	End If
	For RoofNume_i As Integer = 1 To e_roof_pcs
		NodenTotNumero = k1_NodeTauluByRoof(RoofNume_i)
		If NodenTotNumero = 0 Then
			NodenTotNumero = 1 'looppi ei mene muuten läpi
		End If

		For NodeNum_i = 1 To NodenTotNumero 'ensi k1
			For Frame_i = 1 To Round(ototalpcs - 1) Step 2
				oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
				oSDA_from = oSDA_from.Replace("/p1k", "/p" & RoofNume_i & "k")
				oSDA_from = oSDA_from.Replace("_1", "_" & NodeNum_i)
				SDA_from_list.Add(oSDA_from)
				loppuRaami = Frame_i + 2
				If loppuRaami>ototalpcs Then
					loppuRaami = ototalpcs
				End If
				oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
				SDA_to_list.Add(oSDA_to)
			Next
		Next

		NodenTotNumero2 = k2_NodeTauluByRoof(RoofNume_i) 'tarkista suoralla
		'		If NodenTotNumero2 = 0 Then
		'			NodenTotNumero2 = 1 'looppi ei mene muuten läpi
		'		End If

		For Node_k2Num_i = 1 To NodenTotNumero2 'k2
			For Frame_i = 1 To Round(ototalpcs - 1) Step 2
				oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
				oSDA_from = oSDA_from.Replace("/p1k", "/p" & RoofNume_i & "k")
				oSDA_from = oSDA_from.Replace("_1", "_" & Node_k2Num_i)
				oSDA_from = oSDA_from.Replace("_k1_", "_k" & 2 & "_")
				SDA_from_list.Add(oSDA_from)
				loppuRaami = Frame_i + 2
				If loppuRaami>ototalpcs Then
					loppuRaami = ototalpcs
				End If
				oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
				SDA_to_list.Add(oSDA_to)
			Next
		Next
	Next 'roof pcs


	If e_h_purlin_pcs>0 Then 'harjan
		For Frame_i = 1 To Round(ototalpcs - 1) Step 2
			oSDA_from = SDA_from.Replace("f1/", "f" & Frame_i & "/")
			oSDA_from = oSDA_from.Replace("/p1k", "/h")
			SDA_from_list.Add(oSDA_from)
			loppuRaami = Frame_i + 2
			If loppuRaami>ototalpcs Then
				loppuRaami = ototalpcs
			End If
			oSDA_to = SDA_from.Replace("f1/", "f" & loppuRaami & "/")
			SDA_to_list.Add(oSDA_to)
		Next
	End If

	If returnValue = "SDA_to" Then
		Return SDA_to_list
	Else
		Return SDA_from_list
	End If
	Return SDA_from_list 'laskenta myös to? 
End Function
Function CalcPointsDistance(StartPoint As String, EndPoint As String, PointtiHash As Hashtable, Optional PointSuffix As String = ": Center Point", Optional Debuggaus As Boolean = False)
	oStartPoint = PointtiHash(StartPoint & PointSuffix) ' ucs default center point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found StartPoint:" & StartPoint & " using zero point instead")
		oStartPoint = {0, 0, 0 }
	End If
	oEndPoint = PointtiHash(EndPoint & PointSuffix)
	If oEndPoint Is Nothing Then 'virheen korjaus 

		Logger.Debug("Error not found oEndPoint:" & oEndPoint & " using zero point instead")
		oEndPoint = {0, 0, 0 }
	End If
	CalcPointsDistance = ((oEndPoint(0) -oStartPoint(0)) ^ 2 + (oEndPoint(1) -oStartPoint(1)) ^ 2 + (oEndPoint(2) -oStartPoint(2)) ^ 2) ^ 0.5
	'Logger.Debug(StartPoint & " to " & EndPoint & " L:" & CalcPointsDistance)
End Function
Function PointCoorHash(sWPFilter As String, Optional Debuggaus As Boolean = False)
	Dim oHashtable As Hashtable = New Hashtable
	Dim oAsmDoc As AssemblyDocument
	oAsmDoc = ThisApplication.ActiveDocument
	Dim oAsmDef As AssemblyComponentDefinition
	oAsmDef = oAsmDoc.ComponentDefinition
	oDataTeksti = ""

	For i = 1 To oAsmDef.Occurrences.Count
		thisOcc = oAsmDef.Occurrences(i)
		'Debug.Print thisOcc.Name
		'skip suppressed components
		If Not thisOcc.Suppressed Then
			'sub IAM or IPT: loop through all WP
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
				Dim currentWP As WorkPoint
				For Each currentWP In thisOcc.Definition.WorkPoints
					'check if pointname contains underscore
					If InStr(1, currentWP.Name, sWPFilter) Then
						Call oAsmDef.Occurrences(i).CreateGeometryProxy(currentWP, oAsmPoint)
						X = Round(oAsmPoint.point.X * 10, 1)
						Y = Round(oAsmPoint.point.Y * 10, 1)
						Z = Round(oAsmPoint.point.Z * 10, 1)
						If Debuggaus Then
							'Logger.Debug(thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
							oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
						End If
						oHashtable.Add(thisOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
					End If
				Next
			End If
			'sub IAM: loop through all suboccurences
			If thisOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
				For j = 1 To thisOcc.SubOccurrences.Count

					thisSubOcc = thisOcc.SubOccurrences(j)
					If Not thisSubOcc.Suppressed And Not TypeOf thisSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubOcc.Definition Is WeldsComponentDefinition Then
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
							Dim currentSubWP As WorkPoint
							For Each currentSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).Definition.WorkPoints
								'check if pointname contains underscore
								If InStr(1, currentSubWP.Name, sWPFilter) Then
									Call oAsmDef.Occurrences(i).SubOccurrences(j).CreateGeometryProxy(currentSubWP, oAsmPoint)
									X = oAsmPoint.point.X * 10
									Y = oAsmPoint.point.Y * 10
									Z = oAsmPoint.point.Z * 10
									If Debuggaus Then
										Logger.Debug(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]")
										oDataTeksti += thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name & " : [" & Round(X) & "," & Round(Y) & "," & Round(Z) & "]" & vbLf
									End If
									oHashtable.Add(thisOcc.Name & "/" & thisSubOcc.Name & "/" & oAsmPoint.name, {X, Y, Z })
								End If
							Next
						End If
						'subsub IAM: loop through all subsuboccurences
						If thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Then
							For k = 1 To thisSubOcc.SubOccurrences.Count
								thisSubSubOcc = thisSubOcc.SubOccurrences(k)
								If Not thisSubSubOcc.Suppressed And Not TypeOf thisSubSubOcc.Definition Is VirtualComponentDefinition And Not TypeOf thisSubSubOcc.Definition Is WeldsComponentDefinition Then
									If thisSubSubOcc.DefinitionDocumentType = kAssemblyDocumentObject Or kPartDocumentObject Then
										Dim currentSubSubWP As WorkPoint
										For Each currentSubSubWP In oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).Definition.WorkPoints
											'check if pointname contains underscore
											If InStr(1, currentSubSubWP.Name, sWPFilter) Then
												Call oAsmDef.Occurrences(i).SubOccurrences(j).SubOccurrences(k).CreateGeometryProxy(currentSubSubWP, oAsmPoint)
												X = oAsmPoint.point.X * 10
												Y = oAsmPoint.point.Y * 10
												Z = oAsmPoint.point.Z * 10

												If Debuggaus Then
													Logger.Debug(thisSubOcc.Name & " 3nd\" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]")
													oDataTeksti += thisOcc.Name & "/" & oAsmPoint.name & " : [" & X & "," & Y & "," & Z & "]" & vbLf
												End If
												If Not thisSubOcc.DefinitionDocumentType = kAssemblyDocumentObject
													oHashtable.Add(thisSubOcc.Name & " 3nd\" & oAsmPoint.name, {X, Y, Z })
												End If
											End If
										Next
									End If
								End If
							Next
						End If
						'END subsub IAM
					End If
				Next
			End If
			'END sub IAM
		End If
	Next
	If Debuggaus Then
		CoordDataa = oDataTeksti
		My.Computer.Clipboard.SetText(oDataTeksti)
	End If
	Return oHashtable
End Function
Function GroundCompos(oFilter As String)
	For Each oOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		If Left(oOccurrence.Name, Len(oFilter)) = oFilter Then
			oOccurrence.Grounded = True
		End If
	Next
End Function
Function DeltaZ(StartPoint As String, EndPoint As String, PointtiHash As Hashtable, Optional PointSuffix As String = ": Center Point", Optional Debuggaus As Boolean = False)
	oStartPoint = PointtiHash(StartPoint & PointSuffix) ' ucs default center point
	If oStartPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & StartPoint & " using zero point instead")
		oStartPoint = {0, 0, 0 }
	End If
	oEndPoint = PointtiHash(EndPoint & PointSuffix)
	If oEndPoint Is Nothing Then 'virheen korjaus 
		Logger.Debug("Error not found :" & oEndPoint & " using zero point instead")
		oEndPoint = {0, 0, 0 }
	End If
	DeltaZ = oEndPoint(2) -oStartPoint(2)
End Function
Function ParaList(TextLine As String, Optional ReturnValue As String = "Dict")
	'ParaTxt = "3004__LY41-60x10-90_53#4000_1_LY30-50x5-70_40"
	'koe = ParaList(ParaTxt, "1_2")
	Dim oDict As New Dictionary(Of String, Object)
	Dim TxtData() As String = TextLine.Split("#"c)
	oCount = TxtData.Count
	RiviLaskuri = 1
	For Each oVaLine In TxtData
		Try
			Dim ParaData As String() = oVaLine.Split(New Char() {"_"c })
			oLaskuri = 1
			For Each oVal In ParaData
				oDict.Add(RiviLaskuri & "_" & oLaskuri, oVal)
				oLaskuri += 1
			Next
		Catch
		End Try
		RiviLaskuri += 1
	Next
	If ReturnValue = "Dict" Then
		Return oDict
	Else
		Return oDict(ReturnValue)
	End If
End Function
Function makeDictionaryPara(TextLine As String)
	'testi = makeDictionaryPara(TxtPara)
	Dim oDict As New Dictionary(Of String, Object)
	laskuri = 0
	Dim TxtData() As String = TextLine.Split(";"c)
	For Each oVal In TxtData
		Try
			If laskuri = 0 'occ eka then
				oDict.Add("oOccName", oVal)
			Else
				Dim ParaData As String() = oVal.Split(New Char() {"="c })
				'				Try ' ei yksittäisen arvon hakua tässä funktiossa
				'					oArvo = ParaData(1).Split(New Char() {"/"c })
				'				Catch
				'					oArvo = oVal 'yksittäinen tietue
				'				End Try
				oDict.Add(ParaData(0), ParaData(1))
			End If
		Catch
		End Try
		laskuri += 1
	Next
	Return oDict
End Function
Function GetTXTparaValue(ParaName As String, oDict As Dictionary(Of String, Object), Optional returnValue As String = "")
	'koe = GetTXTparaValue("SDA_to", testi)
	Try
		oValue = oDict(ParaName)
		Logger.Debug("oValue:" & oValue)
		Return oValue
	Catch
	End Try
End Function
