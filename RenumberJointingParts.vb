Sub main()
'iLogicVb.RunRule("Finalize") '?
RunRuleSubPartOnlyMainLevel("JointPartFinalize")

Dim PreventNumbering As New ArrayList
'<PERSON><PERSON> esto näille vakio a<PERSON>, muista tehdä tyyliin finalize3
PreventNumbering.Add("XD01")
PreventNumbering.Add("XD02")
PreventNumbering.Add("XD03")
PreventNumbering.Add("XJ01")
PreventNumbering.Add("XJ02")
PreventNumbering.Add("XR1")
PreventNumbering.Add("XR3")
PreventNumbering.Add("XR2")
PreventNumbering.Add("XE01")
PreventNumbering.Add("XE02")

Try
	ProjectNum = SharedVariable("ProjectNum")
Catch
	ProjectNum = ""
End Try

If Len(ProjectNum) <3 Then
	FName = ThisDoc.FileName(False)
	ProjectNum = Left(FName, InStrRev(FName, "_") -1)
	SharedVariable("ProjectNum") = ProjectNum
End If

ResetNumbercal = True
If ResetNumbercal Then
	DL_PNum_calc = 0
	E_PNum_calc = 0
	ET_PNum_calc = 0
	VT_PNum_calc = 0
	G_PNum_calc = 0
	C_PNum_calc = 0
	T_PNum_calc = 0
	B_PNum_calc = 0
	XJ_PNum_calc = 0
End If


Dim oDictOccVsSDA_BASECODE As New Dictionary(Of String, String)
Dim oDictOccVsPartNumber As New Dictionary(Of String, String)
Dim DuplikaattiList As New ArrayList

Dim NumberSchema As New Dictionary(Of String, String())
NumberSchema.Add("BCO-0038", {"DL", "DL_PNum_calc", "_" & ProjectNum }) 'Katto-orsi – Purlin 
NumberSchema.Add("BCO-0022", {"E", "E_PNum_calc", "_" & ProjectNum }) 'Nurjahdusorsi – Buckling crossarm ' miksi maininta vakio soveliassa
NumberSchema.Add("BCO-0045", {"ET", "ET_PNum_calc", "_" & ProjectNum })
NumberSchema.Add("BCO-0039", {"G", "G_PNum_calc", "_" & ProjectNum })  'sideputki – (Brace) ei VT
NumberSchema.Add("BCO-0026", {"T", "T_PNum_calc", "_" & ProjectNum }) 'tuuliside – Wind brace 
NumberSchema.Add("BCO-0040", {"VT", "VT_PNum_calc", "_" & ProjectNum })  'Vinotuki – (Brace) ei sideputki?! Onko täm myös Seinän sisäpaarteen vinotuki
NumberSchema.Add("BCO-0028", {"B", "C_PNum_calc", "_" & ProjectNum }) 'Harjaputki – Ridge tube 'B
NumberSchema.Add("BCO-0029", {"C", "B_PNum_calc", "_" & ProjectNum }) 'Räystäsputki – eave tube 'C
NumberSchema.Add("BCO-0035", {"XJ", "XJ_PNum_calc", "_" & ProjectNum }) 'alaputki XJ

For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
	If Not TypeOf oOccurrence.Definition Is VirtualComponentDefinition And Not TypeOf oOccurrence.Definition Is WeldsComponentDefinition Then
		Try
			oOccName = oOccurrence.Name

			If oOccName.Contains("Mirrored") Then
				Continue For
			End If
			oFullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
			If Not DuplikaattiList.Contains(oFullFileName) Then
				DuplikaattiList.Add(oFullFileName)
				Try : oPNum = iProperties.Value(oOccName, "Project", "Part Number") : Catch : oPNum = "" : End Try

		If PreventNumbering.Contains(oPNum) Then 'ei numeroida aikaisemmin vakioiksi määrättyjä lähinnä JointPartFinalize, mutta toimii myös manuaalisesti.
			Continue For
		End If

		Try : oSDA_BASECODE = iProperties.Value(oOccName, "Custom", "SDA_BASECODE") : Catch : oSDA_BASECODE = "" : End Try
		oDictOccVsPartNumber.Add(oOccName, oPNum)
		oDictOccVsSDA_BASECODE.Add(oOccName, oSDA_BASECODE)
		'If Not oPNum.contains(ProjectNum) Then ehkä rigth toimisi?
		oSchema = oSDA_BASECODE
		Try
			osa1 = NumberSchema(oSDA_BASECODE)(0)
			oParaName = NumberSchema(oSDA_BASECODE)(1)
			Parameter(oParaName) += 1
			osa2 = Parameter(oParaName)
			oNewPartNumber = osa1 & osa2 & NumberSchema(oSDA_BASECODE)(2)
			Logger.Debug("oNewPartNumber:" & oNewPartNumber)
			iProperties.Value(oOccName, "Project", "Part Number") = oNewPartNumber
			'End If
		Catch
		end try
	End If 'duplikaattiesto
	Catch ex As Exception
		Logger.Debug("Problem in :" & oOccName & ":" & oSDA_BASECODE & " " & ex.Message)
	End Try
	End If 'virtuaaliosat pois
Next
RunRuleSubPartOnlyMainLevel("JointPartFinalize")
End Sub



Function RunRuleSubPartOnlyMainLevel(ruleName As String) 'testaa josko toimisi supress juttujen kanssa
	StartTime = Now : auto = iLogicVb.Automation
	Dim oAsmDoc As AssemblyDocument = ThisApplication.ActiveDocument

	For Each oRefDoc As Document In oAsmDoc.AllReferencedDocuments
		Dim rule As Object
		rule = auto.GetRule(oRefDoc, ruleName)
		If (rule Is Nothing) Then
		Else
			Logger.Debug(oRefDoc.DisplayName & " : " & oRefDoc.FullFileName & " :" & Now)
			Dim i As Integer
			i = auto.RunRuleDirect(rule)
		End If
	Next
	Logger.Debug("Total Time is all Run Rules : " & vbLf & Round((Now().Subtract(StartTime)).TotalSeconds, 1))
End Function