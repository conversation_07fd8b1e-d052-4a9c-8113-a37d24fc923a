Imports System.Windows.Forms
AddReference "C:\Vault_BH\ExternalRules_BH_Vault\MyTableForm.dll"
'[ Apu hashtable
Dim ArvOsa As Object
Dim Arvo As Object
Dim oAssy As Document = ThisApplication.ActiveDocument
Dim allDocs As DocumentsEnumerator = oAssy.AllReferencedDocuments
Dim oDoc As Document
Dim TableMuuttuja As Hashtable = New Hashtable

For Each oDoc In allDocs
	Try
		SYNi = oDoc.DisplayName	' iProperties.Value(System.IO.Path.GetFileName(oDoc.FullFileName), "Custom", "#SYN")
	Catch
		SYNi = ""
	End Try
	If Not TableMuuttuja.ContainsKey(SYNi)
		TableMuuttuja.Add(SYNi, oDoc.FullFileName)
	End If
Next
']

Dim myArrayList As New ArrayList
For Each oVal In TableMuuttuja.Keys
	myArrayList.Add(oVal)
Next

d_SYNlist = InputListBox("Select one.", myArrayList, myArrayList.Item(0))

Dim row As Integer

Using mf As New MyTableForm.MyForm
	mf.Text = "Compare"
	Dim dgv As DataGridView = mf.dgvTable
	' set columns
	Dim c1 As Integer = _
	dgv.Columns.Add("MyColumn1", "Parameter [main]")
	Dim c2 As Integer = _
	dgv.Columns.Add("MyColumn2", "Value")
	Dim c3 As Integer = _
	dgv.Columns.Add("MyColumn3", "Unit")
	Dim c4 As Integer = _
	dgv.Columns.Add("MyColumn3", "Value of Part: " & d_SYNlist)

	'[

	Dim UserParams As Parameters'UserParameters
	UserParams = ThisApplication.ActiveEditDocument.ComponentDefinition.Parameters '.ModelParameters            
	i = 0

	For Each Item In UserParams
		'Dim arvo As String
		If Left(Item.Name, 1) <> "d" 'Not Item.Units = "Text" Then
			If Item.Units = "mm" Then 'yksiköide korjaus cm/rad
				arvo = Item.Value * 10
			Else If Item.Units = "deg" Then
			arvo = Item.Value * 180 / PI
		Else
			arvo = Item.Value
		End If

		'logger.debug(Arvo)
		'logger.debug(Item.Units)

		Try
			'logger.debug(ArvOsa)
			Mikästämä = System.IO.Path.GetFileName(TableMuuttuja(d_SYNlist))
			ArvOsa = Parameter(System.IO.Path.GetFileName(TableMuuttuja(d_SYNlist)), Item.Name)
		Catch
			ArvOsa = Nothing
		End Try

		If Not ArvOsa Is Nothing Then
			Dim riviN As Integer = dgv.Rows.Add()
			dgv.Rows(i).Cells(0).Value = Item.Name
			dgv.Rows(i).Cells(1).Value = arvo
			dgv.Rows(i).Cells(2).Value = Item.Units
			dgv.Rows(i).Cells(3).Value = ArvOsa
			i = i + 1
		End If
		End If 'DD
	Next
	If mf.ShowDialog() = DialogResult.OK Then
	End If
End Using
']




