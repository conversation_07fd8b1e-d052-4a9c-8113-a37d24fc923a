Sub main()
oFileList = GetAllFileNames({"C:\Vault_BH\Designs\Proj" })

Publish(ThisDoc.PathAndFileName(True))

	For Each oVal In oFileList
		Publish(oVal)
	Next
	
iLogicVb.UpdateWhenDone = True

End Sub

Function Publish(oFileName As String)
	Try
		Dim addin As Inventor.ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{88621888-5F4D-4B77-BA89-A261C8865F47}")
		_addInInterface = addin.Automation
		_addInInterface.Publish(oFileName)
		Logger.Debug("Publish done :" & oFileName)
	Catch ex As Exception
		System.Windows.Forms.MessageBox.Show(ex.Message)
		Exit Function
	End Try
End Function
Function GetAllFileNames(Filtteri() As String)
	Dim myArrayList As New ArrayList : teksti = ""
	For Each oRefDoc As Document In ThisApplication.ActiveDocument.AllReferencedDocuments
		Try
			For Each oFiltteri In Filtteri
				oFullName = oRefDoc.FullFileName
				If Not myArrayList.Contains(oFullName) And oFullName.Contains(oFiltteri) Then
					myArrayList.Add(oFullName)
				End If
			Next
		Catch ex As Exception
			Logger.Debug("Error :" & ex.Message)
		End Try
	Next
	Return myArrayList
End Function
