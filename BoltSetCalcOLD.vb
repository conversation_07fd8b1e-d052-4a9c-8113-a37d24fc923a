Sub main() 'BoltSetCalculation
Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument 'ShowAllParams
Dim oRefDocs As DocumentsEnumerator = oDoc.AllReferencedDocuments : Dim oRefDoc As Document
Dim OccuLista As New ArrayList
Dim oFNameHashLista As Hashtable = New Hashtable

For Each oOccurrence As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
	If Left(oOccurrence.Name, 1) = "e" Then : 'Logger.Debug(FullFileName)
		FullFileName = oOccurrence.ReferencedFileDescriptor.FullFileName
		If Not oFNameHashLista.ContainsKey(FullFileName) Then
			oQty = ThisBOM.CalculateQuantity("Model Data", iProperties.Value(oOccurrence.Name, "Project", "Part Number"))
			oFNameHashLista.Add(FullFileName, oQty)
			'	OccuLista.Add(oOccurrence.Name)
		End If
	End If
Next

For Each oKey In oFNameHashLista.Keys
	If oKey.contains("brace.ipt") Then 'entä halk 28 (ADet305) M10x40 RS21/RT20 ADet305-8, ADet384
		RS42_pcs += 2 * oFNameHashLista(oKey) 'M16x60 molemmissa päissä x kpl 
		RA41_pcs += 4 * oFNameHashLista(oKey) 'molemmissa päissä x 2 x kpl
		RT40_pcs += 2 * oFNameHashLista(oKey) 'molemmissa päissä x kpl
	ElseIf oKey.contains("brace3.ipt") Then 'entä halk 28 RS21/RT20
		RS42_pcs += 3 * oFNameHashLista(oKey) 'M16x60 molemmissa päissä + keskellä x kpl 
		RA41_pcs += 6 * oFNameHashLista(oKey) 'M16 alus3 x 2 x kpl
		RT40_pcs += 3 * oFNameHashLista(oKey) 'M16 mutteri 3 x kpl
	ElseIf oKey.contains("purlin_tube_LY") Then 'onko aina Zn muissa HDG ADet322
		RK21_pcs += 4 * oFNameHashLista(oKey) 'molemmissa päissä 2 x kpl 
		RS31_pcs += 4 * oFNameHashLista(oKey) ' x kpl
	ElseIf oKey.contains("purlin_tube.ipt") Then 'onko aina Zn muissa HDG ADet321
		'RKxxx_pcs += 2 * oFNameHashLista(oKey) 'onko u -lenkki osa vai virtuaali!!! 
		RS31_pcs += 4 * oFNameHashLista(oKey) ' x kpl
	ElseIf oKey.contains("gable_end_pillar_brace.ipt") Then ' ADet309-10
		RS42_pcs += 2 * oFNameHashLista(oKey) 'M16x60 molemmissa päissä x kpl 
		RA41_pcs += 4 * oFNameHashLista(oKey) 'molemmissa päissä x 2 x kpl
		RT40_pcs += 2 * oFNameHashLista(oKey) 'molemmissa päissä x kpl
	ElseIf oKey.contains("wind_brace_purlin.ipt") Then ' ADet382-383
		RS61_pcs += 2 * oFNameHashLista(oKey) 'M24x80 molemmissa päissä x kpl 
		RA61_pcs += 4 * oFNameHashLista(oKey) 'M24 Aluslevy HDG 125 molemmissa päissä x 2 x kpl
		RT60_pcs += 2 * oFNameHashLista(oKey) 'M24 Mutteri HDG 934 8 molemmissa päissä x kpl
		'mistä kierretanko & tuplamutterit
	End If
Next

If RA41_pcs>0 Then
	CreateByIDdb("RA41", 1, RA41_pcs)
End If
If RA61_pcs>0 Then
	CreateByIDdb("RA61", 1, RA61_pcs)
End If
If RK21_pcs>0 Then
	CreateByIDdb("RK21", 1, RK21_pcs)
End If
If RS31_pcs>0 Then
	CreateByIDdb("RS31", 1, RS31_pcs)
End If
If RS42_pcs>0 Then
	CreateByIDdb("RS42", 1, RS42_pcs)
End If
If RS61_pcs>0 Then
	CreateByIDdb("RS61", 1, RS61_pcs)
End If

If RT40_pcs>0 Then
	CreateByIDdb("RT40", 1, RT40_pcs)
End If
If RT60_pcs>0 Then
	CreateByIDdb("RT60", 1, RT60_pcs)
End If

End Sub

Function ItemCodeCalculator(oSearchList As ArrayList, myHashtable As Hashtable)
	koe = myHashtable
	Dim myArrayList As New ArrayList
	For Each oSearch As String In oSearchList
		If oSearch = "XS06" Then
			myArrayList.Add("RS65") 'adet005 M24x140
			myArrayList.Add("RA61")  'aluslevy M24 x 2
			myArrayList.Add("RA61")
			myArrayList.Add("RT60") 'mutteri M24
		End If

		If Left(oSearch, 2) = "LJ" Then

			Dim oSearchListaus As String() = oSearch.Split(New Char() {"-"c })
			oSize = oSearchListaus(1).Split(New Char() {"x"c })
			oThickness = oSize(1)
			If oThickness>9 And oThickness<21 Then
				For i = 1 To 2 'pulttisettien määrä per levy
					myArrayList.Add("RS61") 'adet102&103 M24x80
					myArrayList.Add("RA61")  'aluslevy M24 x 2
					myArrayList.Add("RA61")
					myArrayList.Add("RT60") 'mutteri M24
				Next
			End If
		End If
	Next

	For Each oVal In myArrayList
		If Not myHashtable.ContainsKey(oVal) Then
			myHashtable.Add(oVal, 1)
		Else
			OldPcsValue = myHashtable(oVal)
			myHashtable.Remove(oval)
			myHashtable.Add(oVal, OldPcsValue + 1)
		End If
	Next

	Return myHashtable
End Function
Function ItemCodeCalculator(ht1 As Hashtable, ht2 As Hashtable)
	Dim ht3 As Hashtable = New Hashtable
	For Each key In ht1.Keys
		If ht2.ContainsKey(key) Then
			ht3.Add(key, ht1(key) + ht2(key))
		Else
			ht3.Add(key, ht1(key))
		End If
	Next key
	For Each key In ht2.Keys
		If Not ht1.ContainsKey(key) Then
			ht3.Add(key, ht2(key))
		End If
	Next key
	Return ht3
End Function
Function ParamsValuToArray(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional ReturnArray As Boolean = False)
	Dim myArrayList As New ArrayList

	For Each oRefDoc In oRefDocs
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)
		For Each Item In UserParams
			If Left(oFilter, 2) = "E=" Then
				If Item.Name = Right(oFilter, Len(oFilter) -2) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			Else
				If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
					myArrayList.Add(Item.Value)
				End If
			End If
		Next
	Next
	If ReturnArray Then
		Return myArrayList
	End If
	'My.Computer.Clipboard.SetText(teksti)
End Function
Function CreateByIDdb(VirtualNumber As String, VirtualRevisio As Integer, Optional oPcs As Integer = 1)
	Dim virtOcc As ComponentOccurrence
	Dim identity As Matrix = ThisApplication.TransientGeometry.CreateMatrix
	Dim virtOccNimi As String = VirtualNumber
	Try
		virtOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AddVirtual(virtOccNimi, identity)
	Catch
		DeleteAllByID(VirtualNumber)
		virtOcc = ThisApplication.ActiveDocument.ComponentDefinition.Occurrences.AddVirtual(virtOccNimi, identity)
	End Try
	virtOcc.Definition.PropertySets.Item("Design Tracking Properties").Item("Part Number").Value = VirtualNumber
	virtOcc.Definition.PropertySets.Item("Summary Information").Item("Revision Number").Value = VirtualRevisio
	If oPcs>1 Then
		ChangeQtyByPartNumberID(VirtualNumber, oPcs + oSaveQty)
	End If
End Function
Function DeleteAllByID(ID As String)
	For Each oOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
		Dim oName As String = oOccurrence.Name
		If oName.Contains(ID) Then
			Logger.Debug("Deleting ID " & ID & " occ: " & oName)
			Try : Component.InventorComponent(oName).Delete() : Catch : End Try
		End If
	Next
End Function
Function ChangeQtyByPartNumberID(ID As String, quantity As Integer)
	Try
		ThisBOM.OverrideQuantity("Model Data", ID, quantity)
		Logger.Debug("BOM Override for " & ID & " is now " & quantity & " pcs")
	Catch
		Logger.Debug("Qty error in itemNumber " & ID)
	End Try
End Function
Function ShowAllParams(oFilter As String, oNegFilter As String, oRefDocs As DocumentsEnumerator, Optional paraTaulu As Boolean = False)
	For Each oRefDoc In oRefDocs
		Logger.Debug("###" & oRefDoc.FullFileName)
		UserParams = oRefDoc.ComponentDefinition.Parameters
		Pituus = Len(oFilter)

		For Each Item In UserParams
			If Item.Name.contains(oFilter) And Left(Item.Name, Len(oNegFilter)) <> oNegFilter Then
				If paraTaulu Then
					Logger.Debug("ParaTaulu.Add(""" & Item.Name & """, " & Item.Expression & ")")
				Else
					Logger.Debug(Item.Name & " = " & Item.Value & " (expression : " & Item.Expression & ")")
				End If
				teksti += oRefDoc.displayname & ":" & Item.Name & ":" & Item.Expression & vbLf
			End If
		Next
	Next
	My.Computer.Clipboard.SetText(teksti)
End Function