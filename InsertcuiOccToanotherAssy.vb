Sub Main

	Dim text As String = CUI_occTranMatrix
	' Remove the last comma
	Dim trimmedText As String = text.TrimEnd(","c)

	' Convert the trimmed string into a flat array of doubles
	Dim lines() As String = trimmedText.Split(New String() {vbCrLf }, StringSplitOptions.RemoveEmptyEntries)
	Dim flatMatrix(15) As Double ' A flat array to hold 16 elements of the 4x4 matrix
	Dim index As Integer = 0

	' Parse each line and assign values to the flat matrix
	For Each line As String In lines
		Dim rowValues() As String = Line.Split(","c)
		For Each value As String In rowValues
			value = value.Trim() ' Remove any surrounding spaces
			If value <> "" Then ' Skip empty values caused by trailing commas
				flatMatrix(index) = CDbl(value)
				index += 1
			End If
		Next
	Next

	
	Dim newOccurrence As ComponentOccurrence
Dim inventorMatrix As Matrix = ThisApplication.TransientGeometry.CreateMatrix()

' Assign values to the Inventor.Matrix object
inventorMatrix.Cell(1, 1) = flatMatrix(0)
inventorMatrix.Cell(1, 2) = flatMatrix(1)
inventorMatrix.Cell(1, 3) = flatMatrix(2)
inventorMatrix.Cell(1, 4) = flatMatrix(3)
inventorMatrix.Cell(2, 1) = flatMatrix(4)
inventorMatrix.Cell(2, 2) = flatMatrix(5)
inventorMatrix.Cell(2, 3) = flatMatrix(6)
inventorMatrix.Cell(2, 4) = flatMatrix(7)
inventorMatrix.Cell(3, 1) = flatMatrix(8)
inventorMatrix.Cell(3, 2) = flatMatrix(9)
inventorMatrix.Cell(3, 3) = flatMatrix(10)
inventorMatrix.Cell(3, 4) = flatMatrix(11)
inventorMatrix.Cell(4, 1) = flatMatrix(12)
inventorMatrix.Cell(4, 2) = flatMatrix(13)
inventorMatrix.Cell(4, 3) = flatMatrix(14)
inventorMatrix.Cell(4, 4) = flatMatrix(15)





'	Dim xtra_sijoitus_1Pos = ThisAssembly.Geometry.Matrix(flatMatrix(0), flatMatrix(1), flatMatrix(2), flatMatrix(3) * 10,
'	flatMatrix(4), flatMatrix(5), flatMatrix(6), flatMatrix(7) * 10,
'	flatMatrix(8), flatMatrix(9), flatMatrix(10), flatMatrix(11) * 10,
'	flatMatrix(12), flatMatrix(13), flatMatrix(14), flatMatrix(15))

	Dim oFileDlg As Inventor.FileDialog = Nothing
	InventorVb.Application.CreateFileDialog(oFileDlg)
	oFileDlg.Filter = "Inventor Files (*.iam)|*.iam"
	oFileDlg.DialogTitle = "Run Ilogic Rule in drawing"
	oFileDlg.InitialDirectory = ThisDoc.Path
	oFileDlg.MultiSelectEnabled = True
	oFileDlg.FilterIndex = 1
	oFileDlg.CancelError = True
	On Error Resume Next
	oFileDlg.ShowOpen()
	If Err.Number <> 0 Then
		MessageBox.Show("File not chosen.", "Dialog Cancellation")
	ElseIf oFileDlg.FileName <> "" Then
		For Each wrd In oFileDlg.FileName.Split("|") 'Words
			Dim odoc As Document = ThisApplication.Documents.Open(wrd, True)
			'Dim xtra_sijoitus_1 = Components.Add(CUI_oOccname, CUI_oOccFullName, position := xtra_sijoitus_1Pos)
			newOccurrence = odoc.ComponentDefinition.Occurrences.Add(CUI_oOccFullName, inventorMatrix)
			newOccurrence.Grounded = True
		Next
	End If
End Sub




Function RunruleDoc(odoc As Document, oRuleName As String)
	Dim addIn As ApplicationAddIn = ThisApplication.ApplicationAddIns.ItemById("{3bdd8d79-2179-4b11-8a5a-257b1c0263ac}")
	Dim iLogic As Object = addIn.Automation
	Try
		Call iLogic.RunRule(odoc, oRuleName)
	Catch ex As Exception
		Logger.Debug("Error in Opening :" & vbLf & ex.Message)
	Finally
		odoc = Nothing
	End Try
End Function