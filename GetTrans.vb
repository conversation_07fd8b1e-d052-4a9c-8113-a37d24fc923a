Sub main()
'oAllOccur = GetAllOccurances()
'Occ1 = GetOccByName("p1k", "f1", oAllOccur)
'Occ2 = GetOccByName("f1", "p1k", oAllOccur)
'
'koe = GetTransMatrixOcc(Occ2)
' Example usage

matrix1 = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1\p1k")

matrix2 = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f3\p1k", "Y")

matrix2b = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f3\p1k")

matrix3 = GetOccurrenceTransMatrixByPath(ThisDoc.Document.ComponentDefinition, "f1\p1k", "X")

End Sub

' Define the function to find a specific occurrence by path
Function GetOccurrenceTransMatrixByPath(oCompDef As ComponentDefinition, FullPath As String, Optional Result As String = "")
	Dim Occ As ComponentOccurrence
	Dim SubOcc As ComponentOccurrence
	Dim SubCompDef As ComponentDefinition
	Dim PathParts() As String
	' Split the full path into parts
	PathParts = Split(FullPath, "\")
	' Initialize the current component definition
	Dim CurrentCompDef As ComponentDefinition = oCompDef
	' Iterate through the path parts
	For i As Integer = 0 To UBound(PathParts)
		Dim Found As Boolean = False
		' Iterate through occurrences in the current component definition
		For Each Occ In CurrentCompDef.Occurrences
			If Occ.Name = PathParts(i) Then
				If i = UBound(PathParts) Then

					If Result = "X" Then
						Return Occ.Transformation.Translation.X
					ElseIf Result = "Y" Then
						Return Occ.Transformation.Translation.Y
					ElseIf Result = "Z" Then
						Return Occ.Transformation.Translation.Z
					Else
						Return Occ.Transformation
					End If

				Else
					' Otherwise, traverse into the subassembly
					SubCompDef = Occ.Definition
					CurrentCompDef = SubCompDef
					Found = True
					Exit For
				End If
			End If
		Next
		' If the part of the path is not found, exit the loop
		If Not Found Then Exit Function
	Next
	' If no matching occurrence is found, return Nothing
	Return Nothing
End Function





Function GetTransMatrixOcc(oComp1 As ComponentOccurrence, Optional Result As String = "")
	If oComp1 Is Nothing Then
		Exit Function
	End If

	Dim pos1 As Vector = oComp1.Transformation.Translation

	If Result = "X" Then
		Return Round(pos1.X, 1)
	ElseIf Result = "Y" Then
		Round(pos1.Y, 1)
	ElseIf Result = "Z" Then
		Round(pos1.Z, 1)
	End If


End Function

Function GetSelectedOccTransMatrix(Occu As ComponentOccurrence)
	Dim showText As String = ""
	Dim oMatrix As Matrix = Occu.Transformation
	Dim oParent As ComponentOccurrence = Occu
	Dim oTopMatrix As Matrix = Occu.Transformation

	' Traverse up the hierarchy to the root assembly
	While Not oParent.ParentOccurrence Is Nothing
		oParent = oParent.ParentOccurrence
		oMatrix = oParent.Transformation
		oTopMatrix.TransformBy(oMatrix)
	End While

	' Extract the final transformation matrix and display it
	Dim i As Integer
	For i = 1 To 4
		Logger.Debug(
		Round(oTopMatrix.Cell(i, 1), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 2), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 3), 4) & ", " & _
		Round(oTopMatrix.Cell(i, 4), 4))

		showText += Round(oTopMatrix.Cell(i, 1), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 2), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 3), 4) & ", "
		showText += Round(oTopMatrix.Cell(i, 4), 4) & ", " & vbLf
	Next

	' Copy the final transformation matrix to the clipboard
	My.Computer.Clipboard.SetText(showText)
	Return showText
End Function

Function GetOccByName(filter1 As String, filter2 As String, oAllOccur As ObjectCollection)
	For Each oOcc As ComponentOccurrence In oAllOccur
		oOccName = oOcc.Name
		oOccPathName1 = oOcc.OccurrencePath.Item(1).Name
		Try
			oOccPathName2 = oOcc.OccurrencePath.Item(2).Name
		Catch
			oOccPathName2 = ""
		End Try
		Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
		If oOccPathName1 = filter1 Then
			'
			Logger.Debug(oOccName & " : " & oOccPathName2 & " \ " & oOccPathName1)
			Return oOcc

		End If
	Next
End Function

Function GetMinimumDistanceByOcc(Occ1 As ComponentOccurrence, Occ2 As ComponentOccurrence, DirectionFilter As String)

	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = pos1.X - pos2.X
		Dim deltaY As Double = pos1.Y - pos2.Y
		Dim deltaZ As Double = pos1.Z - pos2.Z
		Dim distance As Double = Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)
		' Print or use the distance value as needed
		MsgBox("Distance between Component1 and Component2: " & distance)
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function

Function GetAllOccurances(Optional debugMode As Boolean = False) As ObjectCollection
	Dim oDoc As AssemblyDocument = ThisApplication.ActiveDocument
	Dim oAllOccur As ObjectCollection = ThisApplication.TransientObjects.CreateObjectCollection
	Try
		' Top-level occurrences
		For Each oOcc As ComponentOccurrence In oDoc.ComponentDefinition.Occurrences
			ProcessOccurrence(oOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Log or handle the exception
		If debugMode Then Logger.Error("Error in GetAllOccurances: " & ex.Message)
	End Try
	Return oAllOccur
End Function
Sub ProcessOccurrence(ByVal oOcc As ComponentOccurrence, ByRef oAllOccur As ObjectCollection, Optional debugMode As Boolean = False)
	Try
		' Check for invalid or unresolved references
		If oOcc.Definition Is Nothing Then
			If debugMode Then Logger.Warn("Skipping occurrence due to invalid definition: " & oOcc.Name)
			Exit Sub
		End If
		' Skip Virtual and Weld Component Definitions
		If TypeOf oOcc.Definition Is VirtualComponentDefinition OrElse TypeOf oOcc.Definition Is WeldsComponentDefinition Then
			If debugMode Then Logger.Info("Skipping virtual or weld component: " & oOcc.Name)
			Exit Sub
		End If
		' Add the occurrence to the collection
		oAllOccur.Add(oOcc)
		If debugMode Then Logger.Debug("Added occurrence: " & oOcc.Name)
		' Process sub-occurrences recursively
		For Each oSubOcc As ComponentOccurrence In oOcc.SubOccurrences
			ProcessOccurrence(oSubOcc, oAllOccur, debugMode)
		Next
	Catch ex As Exception
		' Handle errors gracefully
		If debugMode Then Logger.Error("Error processing occurrence '" & oOcc.Name & "': " & ex.Message)
	End Try
End Sub

Function GetMinimumDistance(Occ1 As String, Occ2 As String, DirectionFilter As String)
	Dim oDoc As Document = ThisApplication.ActiveDocument
	Dim oComp1 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ1)
	Dim oComp2 As ComponentOccurrence = oDoc.ComponentDefinition.Occurrences.ItemByName(Occ2)

	' Check if the occurrences are valid
	If oComp1 IsNot Nothing And oComp2 IsNot Nothing Then
		' Get position vectors
		Dim pos1 As Vector = oComp1.Transformation.Translation
		Dim pos2 As Vector = oComp2.Transformation.Translation
		' Calculate the distance between occurrences using Pythagorean theorem
		Dim deltaX As Double = pos1.X - pos2.X
		Dim deltaY As Double = pos1.Y - pos2.Y
		Dim deltaZ As Double = pos1.Z - pos2.Z
		Dim distance As Double = Math.Sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ)
		' Print or use the distance value as needed
		MsgBox("Distance between Component1 and Component2: " & distance)
	Else
		MsgBox("Invalid occurrences.")
	End If
End Function