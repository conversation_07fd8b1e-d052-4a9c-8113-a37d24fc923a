Class ThisRule 'HideUnNeededUCS
	' Setup Progress Bar
	Dim ReferenceCount As Integer
	Dim oStep As Integer
	Dim oMessage As String = "Setting visibility for objects to off"
	Dim oProgressBar As Inventor.ProgressBar
	Dim DocFailed As Integer

	Sub Main()
		'get the active document
		Dim oDoc As Document
		oDoc = ThisApplication.ActiveDocument

		'check if the rule is run on an Assembly
		Dim Assydoc As AssemblyDocument
		If oDoc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject Then
			Assydoc = oDoc
		Else
			MessageBox.Show("Rule needs to be run from Assembly", "Rule: Object visibility")
			Exit Sub
		End If

		Dim PreventDup As New ArrayList
		Dim oRefDocs As New ArrayList
		oAssyPath = ThisDoc.Path
		Dim oAssyPathValue As String = oAssyPath

		For Each doc As Document In Assydoc.AllReferencedDocuments
			If Not PreventDup.Contains(doc.FullFileName) And doc.FullFileName.Contains(oAssyPathValue) Then
				PreventDup.Add(doc.FullFileName)
				oRefDocs.Add(doc)
			End If
		Next


		'Present user with multiple choice
		Dim Options() As String = {"Show All UCS", "Show between frames", "Hide All UCS", "Show all occurences", "Hide other than joint UCS (joint/wind_brace)", "Show Purlins UCS", "Show Wind UCS", "Show Crossarm UCS", "Show Brace UCS", "Hide selected occurences" }
		Result1 = InputListBox("Please select", Options, Options(0), Title := "Object Visibility", ListName := "List")
		'perform actions on basis of multiple choice. First all documents in the assembly are counted, then the objects are turned off whilst the user is presented with a progress bar
		Select Case Result1
			Case options(0) 'show all
				CountReferencedDocuments(Assydoc, 1)
				RemoveSketches(Assydoc)
				RemoveWorkpoints(Assydoc)
				RemoveAxis(Assydoc)
				ShowAllUcs(Assydoc)
			Case options(1) 'show betwween by bounding box
				CountReferencedDocuments(Assydoc, 1)
				ShowBetweenOcc()
			Case options(2) 'hide all
				CountReferencedDocuments(Assydoc, 1)
				HideAllUcs(Assydoc)
			Case options(3) 'show all occs
				CountReferencedDocuments(Assydoc, 1)
				ActivateAllOcc(False) ' vain piilotus, pitää sama kuin unactivated
			Case options(4)
				CountReferencedDocuments(Assydoc, 1)
				RemoveOtherUCS(Assydoc)
			Case options(5)
				CountReferencedDocuments(Assydoc, 1)
				ShowPurlins(Assydoc)
			Case options(6) 'wind
				CountReferencedDocuments(Assydoc, 1)
				ShowWind(Assydoc)
			Case options(7)'_crossarm_
				CountReferencedDocuments(Assydoc, 1)
				ShowCrossarm(Assydoc)
			Case options(8) 'joint_brace_
				CountReferencedDocuments(Assydoc, 1)
				ShowBrace(Assydoc)
			Case options(9) 'hide occs
				CountReferencedDocuments(Assydoc, 1)
				UnActivateOcc(False) ' vain piilotus
		End Select

		oProgressBar.Close
		'Check if there are any failed files, and show user how many files.
		If DocFailed <> 0 Then
			MessageBox.Show(DocFailed & " Objects failed to set to invisible", "Object Visibility")
		End If
		'Update file
		iLogicVb.UpdateWhenDone = True
	End Sub

	'this sub will count the objects and create the progress bar
	Sub CountReferencedDocuments(Assydoc As AssemblyDocument, Passes As Integer, Optional oFilter As String = "")
		For Each doc As Document In Assydoc.AllReferencedDocuments
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				ReferenceCount = ReferenceCount + Passes
			End If
		Next
		oProgressBar = ThisApplication.CreateProgressBar(False, ReferenceCount, oMessage)
	End Sub
	Sub ShowBetweenOcc()
		100 :
		StartNum = InputBox("Show/ Frame start number", "Instance Number", 0)
		EndNum = InputBox("Show/ Frame end number", "Instance Number", 0)
		Try
			koe = (StartNum + EndNum) / 2
		Catch
			MessageBox.Show("Not valid frame number", "Error")
			GoTo 100
		End Try
		Try
			oOccMin = ThisDoc.Document.ComponentDefinition.Occurrences.ItemByName("f" & StartNum)
			MinY = (oOccMin.PreciseRangeBox.MinPoint.Y + oOccMin.PreciseRangeBox.MaxPoint.Y) / 2
		Catch
			Logger.Debug("Error getting min occurence")
			MinY = -1000
		End Try
		Try
			oOccMax = ThisDoc.Document.ComponentDefinition.Occurrences.ItemByName("f" & EndNum)
			MaxY = (oOccMax.PreciseRangeBox.MinPoint.Y + oOccMax.PreciseRangeBox.MaxPoint.Y) / 2
		Catch
			Logger.Debug("Error getting max occurence")
			MaxY = 100000
		End Try

		For Each oOcc As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
			VertailuY = (oOcc.PreciseRangeBox.MinPoint.Y + oOcc.PreciseRangeBox.MaxPoint.Y) / 2

			If VertailuY<MinY Then
				'Logger.Debug("liian pieni:" & VertailuY & " < " & MinY)
				oHide = False
			ElseIf VertailuY>MaxY Then
				'Logger.Debug("liian iso:" & VertailuY & " > " & MaxY)
				oHide = False
			Else
				'Logger.Debug("sopiva" & VertailuY & " > " & MaxY & " < " & MinY)
				oHide = True
			End If
			oOcc.Visible = oHide
		Next
	End Sub
	Sub UnActivateOcc(Optional Suppressointi As Boolean = False)
		100 :
		StartNum = InputBox("Hide Frame start number", "Instance Number", 0)
		EndNum = InputBox("Hide Frame end number", "Instance Number", 0)

		Try
			koe = (StartNum + EndNum) / 2
		Catch
			MessageBox.Show("Not valid frame number", "Error")
			GoTo 100
		End Try

		If StartNum = 0 And EndNum = 0 Then
			ActivateAllOcc(Suppressointi)
		End If
		For Each oOcc As ComponentOccurrence In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
			If Left(oOcc.Name, 1) = "f" Then
				oNum = CInt(oOcc.Name.Replace("f", ""))

				If oNum >= StartNum And oNum <= EndNum Then

					If Suppressointi Then
						oOcc.Suppress
					Else
						oOcc.Visible = False
					End If

				End If

			End If
		Next
		i = MessageBox.Show("Would it be appropriate to continue hiding components?", "Hide", MessageBoxButtons.YesNo)
		If i = vbYes Then
			UnActivateOcc(Suppressointi)
		End If
	End Sub
	Sub ActivateAllOcc(Optional Suppressointi As Boolean = False)
		For Each oOcc In ThisApplication.ActiveDocument.ComponentDefinition.Occurrences
			If oNum >= StartNum And oNum <= EndNum Then
				If Suppressointi Then
					oOcc.Unsuppress
				Else
					oOcc.Visible = True
				End If

			End If
		Next
	End Sub
	Sub RemoveOtherUCS(Assydoc As AssemblyDocument)
		oFilter = "joint" : oFilter2 = "wind_brace_"
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Or oUserCoordinateSystem.Name.contains(oFilter2) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Or oUserCoordinateSystem.Name.contains(oFilter2) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Or oUserCoordinateSystem.Name.contains(oFilter2) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub ShowBrace(Assydoc As AssemblyDocument)
		oFilter = "joint_brace_"
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub ShowCrossarm(Assydoc As AssemblyDocument)

		oFilter = "_crossarm_"
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub ShowWind(Assydoc As AssemblyDocument)
		oFilter = "wind"
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub ShowPurlins(Assydoc As AssemblyDocument)
		oFilter = "joint_purlin_"
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub ShowAllUcs(Assydoc As AssemblyDocument)
		Dim myArrayList As New ArrayList
		oAssyPath = ThisDoc.Path
		oFilter = ""
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			If oUserCoordinateSystem.Name.contains(oFilter) Then
				oUserCoordinateSystem.Visible = True
			Else
				oUserCoordinateSystem.Visible = False
			End If
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)

			If Not myArrayList.Contains(doc.FullFileName) Then
				myArrayList.Add(doc.FullFileName)
				Dim oAssyPathValue As String = oAssyPath
				If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True And doc.FullFileName.Contains(oAssyPathValue) Then
					Dim Partdoc As PartDocument = doc
					For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
						If oUserCoordinateSystem.Name.contains(oFilter) Then
							oUserCoordinateSystem.Visible = True
						Else
							Try
								oUserCoordinateSystem.Visible = False
							Catch
								DocFailed = DocFailed + 1
							End Try
						End If
					Next
					Partdoc.Update()
				End If
			End If 'duplikaatti

			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					If oUserCoordinateSystem.Name.contains(oFilter) Then
						oUserCoordinateSystem.Visible = True
					Else
						Try
							oUserCoordinateSystem.Visible = False
						Catch
							DocFailed = DocFailed + 1
						End Try
					End If
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub HideAllUcs(Assydoc As AssemblyDocument)
		oFilter = ""
		For Each oUserCoordinateSystem In Assydoc.ComponentDefinition.UserCoordinateSystems
			oUserCoordinateSystem.Visible = False
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove origin planes- document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oUserCoordinateSystem In Partdoc.ComponentDefinition.UserCoordinateSystems
					Try
						oUserCoordinateSystem.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oUserCoordinateSystem In Asmdoc.ComponentDefinition.UserCoordinateSystems
					Try
						oUserCoordinateSystem.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub RemoveAxis(Assydoc As AssemblyDocument)
		For Each oAxes In Assydoc.ComponentDefinition.WorkPlanes
			oAxes.Visible = False
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove workpoints - document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oAxes In Partdoc.ComponentDefinition.WorkAxes
					Try
						oAxes.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oAxes In Asmdoc.ComponentDefinition.WorkAxes
					Try
						oAxes.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub RemoveWorkpoints(Assydoc As AssemblyDocument)
		For Each oWorkPoint In Assydoc.ComponentDefinition.WorkPoints
			oWorkPoint.Visible = False
		Next

		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove workpoints - document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oWorkPoint In Partdoc.ComponentDefinition.WorkPoints
					Try
						oWorkPoint.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oWorkPoint In Asmdoc.ComponentDefinition.WorkPoints
					Try
						oWorkPoint.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Asmdoc.Update()
			End If
			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	Sub RemoveSketches(Assydoc As AssemblyDocument)
		For Each doc As Document In Assydoc.AllReferencedDocuments
			Logger.Trace("remove sketches - document name:" & doc.FullDocumentName)
			If (doc.DocumentType = DocumentTypeEnum.kPartDocumentObject) And doc.IsModifiable = True Then
				Dim Partdoc As PartDocument = doc
				For Each oSketch In Partdoc.ComponentDefinition.Sketches
					Try
						oSketch.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				For Each o3DSketch In Partdoc.ComponentDefinition.Sketches3D
					Try
						o3DSketch.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				Partdoc.Update()
			End If
			If (doc.DocumentType = DocumentTypeEnum.kAssemblyDocumentObject) And doc.IsModifiable = True Then
				Dim Asmdoc As AssemblyDocument = doc
				For Each oSketch In Asmdoc.ComponentDefinition.Sketches
					Try
						oSketch.Visible = False
					Catch
						DocFailed = DocFailed + 1
					End Try
				Next
				' only 2d sketches are in assembly files
			End If


			oProgressBar.Message = ("File " & oStep & " of " & ReferenceCount & ", :Procesed ")
			oProgressBar.UpdateProgress
			oStep = oStep + 1
		Next
	End Sub
	'Functions to parse Filename and folder from complete Path
	Public Function FileNameFromPath(strFullPath As String) As String
		FileNameFromPath = Right(strFullPath, Len(strFullPath) -InStrRev(strFullPath, "\"))
	End Function
	Public Function FileNameFromPathNoExt(strFullPath As String) As String
		Dim FileNameFromPath2 As String = FileNameFromPath(strFullPath)
		FileNameFromPathNoExt = Left(FileNameFromPath2, (InStrRev(FileNameFromPath2, ".") -1))
	End Function
	Public Function FolderFromPath(strFullPath As String) As String
		FolderFromPath = Left(strFullPath, InStrRev(strFullPath, "\"))
	End Function
End Class